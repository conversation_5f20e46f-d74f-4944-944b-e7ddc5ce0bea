"""
@Author: JiangXin
@Description: 5日均线上穿 20日均线，并且 rsi 处于温和上涨状态
后续深入研究：1、不同的市值区间的表现；2、其他技术指标（PE、资金流）；3、情绪因子；4、行业/概念 繁荣度
2月 27号加入了主力资金，筛选出前 20% 的主力集中度
"""


import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
import logging
import os
import datetime
import tqdm  # 导入tqdm进度条库
import multiprocessing as mp  # 导入多进程库
from functools import partial  # 导入partial函数
# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 定义为独立函数，用于多进程处理
def process_stock_group(group):
    """
    处理单个股票的数据（用于并行计算）
    :param group: 单个股票的DataFrame
    :return: 处理后的DataFrame
    """
    try:
        # 计算移动平均
        group = group.copy()  # 创建副本避免警告
        group['MA5'] = group['close'].ewm(span=5, min_periods=5).mean()
        group['MA20'] = group['close'].ewm(span=20, min_periods=20).mean()
        
        # 生成交易信号
        group['Buy_Signal'] = (group['MA5'] > group['MA20']) & (group['RSI'] > 30) & (group['RSI'] < 60)
        group['Sell_Signal'] = (group['MA5'] < group['MA20']) & (group['RSI'] > 70)
        
        # 计算收益率
        group['return_3d'] = (group['day3_close'] - group['next_day_open']) / group['next_day_open'] * 100
        group['return_7d'] = (group['day7_close'] - group['next_day_open']) / group['next_day_open'] * 100
        group['return_10d'] = (group['day10_close'] - group['next_day_open']) / group['next_day_open'] * 100
        group['return_15d'] = (group['day15_close'] - group['next_day_open']) / group['next_day_open'] * 100
        
        # 保留两位小数
        group['return_3d'] = group['return_3d'].round(2)
        group['return_7d'] = group['return_7d'].round(2)
        group['return_10d'] = group['return_10d'].round(2)
        group['return_15d'] = group['return_15d'].round(2)
        
        return group
    except Exception as e:
        print(f"处理股票数据失败: {str(e)}")
        return None

class MAStrategy:
    def __init__(self, db_url, output_dir='adx_output'):
        """
        初始化策略
        :param db_url: 数据库连接URL
        :param output_dir: 输出目录
        """
        self.db_url = db_url  # 存储数据库URL而不是engine实例
        self.engine = create_engine(db_url)
        self.output_dir = output_dir
        self.logger = logging.getLogger(__name__)
        os.makedirs(self.output_dir, exist_ok=True)

    def get_stock_data(self, start_date=None, end_date=None):
        """
        获取股票数据
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: DataFrame
        """
        try:
            self.logger.info("开始获取股票数据...")
            # 移除不存在的索引提示
            query = """
            WITH filtered_stocks AS (
                SELECT ts_code, trade_date
                FROM daily_basic
                WHERE turnover_rate >= 3  -- 换手率大于3%
                AND circ_mv <= 1000000   
                AND circ_mv >= 100000     -- 10~100亿
            ),
            FuturePrice AS (
                SELECT 
                    sf.ts_code,
                    sf.trade_date,
                    sf.close_hfq as close,
                    LEAD(sf.open_hfq, 1) OVER (PARTITION BY sf.ts_code ORDER BY sf.trade_date) as next_day_open,
                    LEAD(sf.close_hfq, 3) OVER (PARTITION BY sf.ts_code ORDER BY sf.trade_date) as day3_close,
                    LEAD(sf.close_hfq, 7) OVER (PARTITION BY sf.ts_code ORDER BY sf.trade_date) as day7_close,
                    LEAD(sf.close_hfq, 10) OVER (PARTITION BY sf.ts_code ORDER BY sf.trade_date) as day10_close,
                    LEAD(sf.close_hfq, 15) OVER (PARTITION BY sf.ts_code ORDER BY sf.trade_date) as day15_close
                FROM stk_factor sf
                INNER JOIN filtered_stocks fs 
                ON fs.ts_code = sf.ts_code
                AND fs.trade_date = sf.trade_date
            ),
            TopMoneyStocks AS (
                SELECT 
                    fmd.ts_code,
                    fmd.trade_date,
                    fmd.main_money_ratio,
                    PERCENT_RANK() OVER (PARTITION BY fmd.trade_date ORDER BY fmd.main_money_ratio DESC) as money_rank
                FROM factors_money_focus_data fmd
                INNER JOIN filtered_stocks fs 
                ON fs.ts_code = fmd.ts_code
                AND fs.trade_date = fmd.trade_date
                WHERE fmd.main_money_ratio IS NOT NULL
            )
            SELECT 
                f.ts_code,
                f.trade_date,
                fp.close,
                fp.next_day_open,
                fp.day3_close,
                fp.day7_close,
                fp.day10_close,
                fp.day15_close,
                b.turnover_rate,
                b.circ_mv,
                f.rsi_6 as RSI,
                tms.main_money_ratio
            FROM stk_factor f
            INNER JOIN filtered_stocks fs 
                ON fs.ts_code = f.ts_code
                AND fs.trade_date = f.trade_date
            INNER JOIN daily_basic b 
                ON b.ts_code = f.ts_code
                AND f.trade_date = b.trade_date
            INNER JOIN FuturePrice fp 
                ON fp.ts_code = f.ts_code 
                AND f.trade_date = fp.trade_date
            INNER JOIN TopMoneyStocks tms
                ON tms.ts_code = f.ts_code
                AND f.trade_date = tms.trade_date
                AND tms.money_rank <= 0.2
            """
            if start_date:
                query += " WHERE f.trade_date >= :start_date"
            if end_date:
                query += " AND f.trade_date <= :end_date" if start_date else " WHERE f.trade_date <= :end_date"
            query += " ORDER BY f.trade_date, f.ts_code"
            
            params = {'start_date': start_date, 'end_date': end_date}
            
            # 直接执行查询，不再预先计算总行数
            self.logger.info("开始从数据库读取数据...")
            
            # 增加连接池大小和超时设置
            engine = create_engine(
                self.db_url, 
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=3600
            )
            
            # 使用更大的chunksize提高读取效率
            chunksize = 20000  # 增加每次处理的数据量
            chunks = []
            
            for chunk in pd.read_sql(text(query), engine, params=params, chunksize=chunksize):
                chunks.append(chunk)
                self.logger.info(f"已读取 {len(chunks) * chunksize} 行数据")
            
            df = pd.concat(chunks) if chunks else pd.DataFrame()
            self.logger.info(f"获取股票数据完成，共 {len(df)} 行")
            
            if df.empty:
                self.logger.warning("没有找到符合条件的股票数据")
            return df
        except Exception as e:
            self.logger.error(f"获取股票数据失败: {str(e)}")
            return None

    def calculate_indicators(self, df, use_parallel=True):
        """
        计算技术指标并生成信号 - 可选择是否使用并行计算
        :param df: 原始数据DataFrame
        :param use_parallel: 是否使用并行计算
        :return: 包含信号的DataFrame
        """
        try:
            if df is None or df.empty:
                return None
                
            self.logger.info("开始计算技术指标...")
            
            # 获取唯一的股票代码列表
            stock_codes = df['ts_code'].unique()
            stock_count = len(stock_codes)
            self.logger.info(f"需要处理的股票数量: {stock_count}")
            
            # 预先按股票代码分组
            grouped = df.groupby('ts_code')
            
            if use_parallel:
                # 使用并行计算
                # 获取CPU核心数，留一个核心给系统使用
                num_cores = max(1, mp.cpu_count() - 1)
                self.logger.info(f"使用 {num_cores} 个CPU核心进行并行计算")
                
                # 在启动进程池前关闭数据库连接，防止连接被错误地复制到子进程
                if hasattr(self, 'engine') and self.engine:
                    self.engine.dispose()
                
                groups = [group for _, group in grouped]
                
                # 使用多进程并行计算 - 使用全局函数而非类方法
                with mp.Pool(processes=num_cores) as pool:
                    results = list(tqdm.tqdm(
                        pool.imap(process_stock_group, groups),
                        total=len(groups),
                        desc="并行计算技术指标"
                    ))
                
                # 重新创建engine连接
                self.engine = create_engine(self.db_url)
                
                # 合并结果
                result_df = pd.concat([r for r in results if r is not None])
            else:
                # 使用串行计算
                self.logger.info("使用串行计算技术指标")
                results = []
                
                for ts_code, group in tqdm.tqdm(grouped, desc="计算技术指标"):
                    processed_group = process_stock_group(group)  # 使用全局函数
                    if processed_group is not None:
                        results.append(processed_group)
                
                result_df = pd.concat(results) if results else pd.DataFrame()
            
            self.logger.info("技术指标计算完成")
            return result_df
        except Exception as e:
            self.logger.error(f"计算技术指标失败: {str(e)}")
            return None

    def run_strategy(self, start_date=None, end_date=None, use_parallel=True):
        """
        运行策略
        :param start_date: 开始日期
        :param end_date: 结束日期
        :param use_parallel: 是否使用并行计算
        """
        try:
            # 显示总体进度
            self.logger.info("===== 策略执行开始 =====")
            self.logger.info(f"计算模式: {'并行' if use_parallel else '串行'}")
            
            # 获取数据
            df = self.get_stock_data(start_date, end_date)
            
            if df is None or df.empty:
                self.logger.error("没有获取到股票数据")
                return
                
            # 计算指标
            df = self.calculate_indicators(df, use_parallel=use_parallel)
            
            if df is None:
                return
            
            # 获取信号
            self.logger.info("开始生成交易信号...")
            signals = df[df['Buy_Signal'] | df['Sell_Signal']].copy()
            signals['Signal_Type'] = np.where(signals['Buy_Signal'], '买入', '卖出')
            signals = signals.sort_values('trade_date')
            self.logger.info(f"生成信号完成，共 {len(signals)} 条信号")
            
            # 选择要输出的列
            output_columns = ['ts_code', 'trade_date', 'close', 'next_day_open', 
                            'return_3d', 'return_7d', 'return_10d', 'return_15d',
                            'MA5', 'MA20', 'RSI', 'Buy_Signal', 'Sell_Signal', 'Signal_Type']
            
            # 获取当前时间戳并格式化
            current_time = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(self.output_dir, f'signals_{current_time}.csv')
            self.logger.info(f"保存信号到文件: {output_file}")
            signals[output_columns].to_csv(output_file, index=False, encoding='utf_8_sig')
            self.logger.info(f"信号已保存到 {output_file}")
            
            self.logger.info("===== 策略执行完成 =====")
        except Exception as e:
            self.logger.error(f"策略运行失败: {str(e)}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='运行MA策略')
    parser.add_argument('--start_date', type=str, default='2024-12-01', help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end_date', type=str, default='2025-04-25', help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--parallel', action='store_true', help='是否使用并行计算')
    parser.add_argument('--no-parallel', dest='parallel', action='store_false', help='不使用并行计算')
    parser.set_defaults(parallel=True)

    args = parser.parse_args()

    DB_URL = "mysql+pymysql://root:oyvla6qe@127.0.0.1:3306/test?charset=utf8mb4"
    strategy = MAStrategy(DB_URL)

    # 记录开始时间
    start_time = datetime.datetime.now()
    strategy.logger.info(f"开始执行时间: {start_time}")

    # 运行策略
    strategy.run_strategy(start_date=args.start_date, end_date=args.end_date, use_parallel=args.parallel)
    
    # 记录结束时间并计算总耗时
    end_time = datetime.datetime.now()
    strategy.logger.info(f"结束执行时间: {end_time}")
    strategy.logger.info(f"总耗时: {end_time - start_time}")
    logging.info(f"开始时间：{args.start_date}，结束时间：{args.end_date}")
