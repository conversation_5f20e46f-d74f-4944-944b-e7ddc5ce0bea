"""
@Author: Jiang<PERSON>in
@Date: 2025/2/25 12:01
@Description: 使用DQN强化学习进行股票交易
"""
import numpy as np
import pandas as pd
import gym
import random
import tensorflow as tf
from collections import deque
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sqlalchemy import create_engine
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from get_data.db_config import *

# 设置随机种子
np.random.seed(42)
tf.random.set_seed(42)


# 1. 股票交易环境（修复数据标准化与资产计算）
class StockTradingEnv(gym.Env):
    def __init__(self, raw_data, window_size=10, initial_balance=100000):
        super(StockTradingEnv, self).__init__()
        self.raw_data = raw_data  # 原始数据（未标准化）
        self.window_size = window_size
        self.initial_balance = initial_balance
        self.current_step = 0
        self.action_space = gym.spaces.Discrete(3)  # 0: 买入, 1: 卖出, 2: 持有
        self.observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf,
            shape=(window_size * raw_data.shape[1],),  # 展平为1D
            dtype=np.float32
        )
        # 初始化状态
        self.reset()

    def reset(self):
        self.balance = self.initial_balance
        self.shares_held = 0
        self.total_shares_sold = 0
        self.current_step = self.window_size
        # 动态标准化：仅使用初始窗口数据计算参数
        initial_data = self.raw_data[:self.window_size]
        self.scaler = StandardScaler().fit(initial_data)
        self.data_scaled = self.scaler.transform(self.raw_data)
        # 返回初始状态（展平）
        return self.data_scaled[self.current_step - self.window_size:self.current_step].flatten()

    def step(self, action):
        # 使用原始价格计算资产
        current_price = self.raw_data[self.current_step, 3]  # 收盘价

        # 执行动作（允许部分交易）
        if action == 0:  # 买入（使用20%可用资金）
            available_funds = self.balance * 0.2  # 控制仓位
            shares_to_buy = available_funds // current_price
            cost = shares_to_buy * current_price
            if self.balance >= cost:
                self.balance -= cost
                self.shares_held += shares_to_buy
        elif action == 1:  # 卖出（卖出20%持仓）
            shares_to_sell = self.shares_held // 5  # 分批卖出
            if shares_to_sell > 0:
                self.balance += shares_to_sell * current_price
                self.shares_held -= shares_to_sell
                self.total_shares_sold += shares_to_sell

        # 更新时间步
        self.current_step += 1
        done = self.current_step >= len(self.raw_data) - 1

        # 计算奖励（加入风险惩罚）
        current_value = self.balance + self.shares_held * current_price
        profit = current_value - self.initial_balance
        # 惩罚频繁交易和波动率
        volatility = np.std(self.raw_data[self.current_step - 10:self.current_step, 3]) if self.current_step > 10 else 0
        reward = profit - 0.1 * volatility  # 风险调整后收益

        # 获取下一状态（动态标准化）
        next_state = self.data_scaled[self.current_step - self.window_size:self.current_step].flatten()

        return next_state, reward, done, {}

    def render(self):
        print(f"Step: {self.current_step}, Balance: {self.balance:.2f}, Shares: {self.shares_held}")


# 2. DQN模型（添加目标网络）
class DQN:
    def __init__(self, state_size, action_size):
        """
        Initialize a DQN model.

        Parameters
        ----------
        state_size : int
            The size of the state space.
        action_size : int
            The size of the action space.

        Attributes
        ----------
        state_size : int
            The size of the state space.
        action_size : int
            The size of the action space.
        memory : deque
            A double-ended queue storing the experience tuples.
        gamma : float
            The discount factor.
        epsilon : float
            The exploration rate.
        epsilon_min : float
            The minimum exploration rate.
        epsilon_decay : float
            The decay rate for the exploration rate.
        learning_rate : float
            The learning rate for the optimizer.
        model : Model
            The main model for predicting the Q-values.
        target_model : Model
            The target model for predicting the target Q-values.
        """
        self.state_size = state_size
        self.action_size = action_size
        self.memory = deque(maxlen=2000)
        self.gamma = 0.95  # 折扣因子
        self.epsilon = 1.0  # 初始探索率
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = 0.001
        self.model = self._build_model()
        self.target_model = self._build_model()
        self.update_target_model()

    def _build_model(self):
        model = tf.keras.Sequential([
            tf.keras.layers.Dense(64, input_dim=self.state_size, activation='relu'),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(self.action_size, activation='linear')
        ])
        model.compile(loss='mse', optimizer=tf.keras.optimizers.Adam(learning_rate=self.learning_rate))
        return model

    def update_target_model(self):
        self.target_model.set_weights(self.model.get_weights())

    def remember(self, state, action, reward, next_state, done):
        # 确保状态是一维的
        state = state.flatten()
        next_state = next_state.flatten()
        self.memory.append((state, action, reward, next_state, done))

    def act(self, state):
        if np.random.rand() <= self.epsilon:
            return random.randrange(self.action_size)
        # 确保状态是二维的，形状为(1, state_size)
        state = np.reshape(state, [1, self.state_size])
        act_values = self.model.predict(state)
        return np.argmax(act_values[0])

    def replay(self, batch_size):
        if len(self.memory) < batch_size:
            return

        minibatch = random.sample(self.memory, batch_size)
        
        # 创建numpy数组来存储状态和下一状态
        states = np.zeros((batch_size, self.state_size))
        next_states = np.zeros((batch_size, self.state_size))
        
        # 准备训练数据
        for i, (state, action, reward, next_state, done) in enumerate(minibatch):
            states[i] = state
            next_states[i] = next_state

        # 使用目标网络预测next_state的Q值
        targets = self.model.predict(states)
        q_next = self.target_model.predict(next_states)

        for i, (state, action, reward, next_state, done) in enumerate(minibatch):
            target = reward
            if not done:
                target = reward + self.gamma * np.max(q_next[i])
            targets[i][action] = target

        self.model.fit(states, targets, epochs=1, verbose=0)

        # 衰减探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay


# 3. 训练与评估函数
def train_dqn(data, window_size=10, episodes=50, batch_size=32):
    env = StockTradingEnv(data, window_size)
    state_size = env.observation_space.shape[0]
    action_size = env.action_space.n
    agent = DQN(state_size, action_size)
    rewards = []

    # 计算并打印总步数
    max_steps = len(data) - window_size
    print(f"每个episode的最大步数: {max_steps}")
    print(f"预计总训练步数: {episodes * max_steps}")

    for e in range(episodes):
        state = env.reset()
        state = state.flatten()  # 确保状态是一维的
        total_reward = 0
        done = False
        step_count = 0

        while not done:
            action = agent.act(state)
            next_state, reward, done, _ = env.step(action)
            next_state = next_state.flatten()  # 确保下一状态是一维的
            agent.remember(state, action, reward, next_state, done)
            
            # 每隔100步才进行一次训练，减少计算量
            if len(agent.memory) >= batch_size and step_count % 100 == 0:
                agent.replay(batch_size)
            
            state = next_state
            total_reward += reward
            step_count += 1

        # 每5个episode更新一次目标网络
        if e % 5 == 0:
            agent.update_target_model()

        rewards.append(total_reward)
        print(f"Episode {e}/{episodes}, Steps: {step_count}, Reward: {total_reward:.2f}, Epsilon: {agent.epsilon:.2f}")

    return agent, rewards


def evaluate_agent(agent, data, window_size=10):
    env = StockTradingEnv(data, window_size)
    state = env.reset()
    state = state.flatten()  # 确保状态是一维的
    total_reward = 0
    done = False
    agent.epsilon = 0  # 关闭探索

    while not done:
        action = agent.act(state)
        next_state, reward, done, _ = env.step(action)
        next_state = next_state.flatten()  # 确保下一状态是一维的
        state = next_state
        total_reward += reward
        env.render()

    print(f"Final Portfolio Value: {env.balance + env.shares_held * env.raw_data[env.current_step, 3]:.2f}")


# 4. 从数据库获取股票数据
def get_stock_data(stock_code, start_date, end_date):
    """
    从数据库获取股票数据
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        DataFrame: 包含OHLCV数据的DataFrame
    """
    engine = create_engine(DB_URL)
    
    # 获取后复权的日线数据
    query = f"""
    SELECT trade_date, 
           open_hfq,
           high_hfq,
           low_hfq,
           close_hfq,
           vol as volume
    FROM stk_factor
    WHERE ts_code = '{stock_code}'
    AND trade_date BETWEEN '{start_date}' AND '{end_date}'
    ORDER BY trade_date
    """
    
    df = pd.read_sql(query, engine)
    
    # 转换为numpy数组，按照OHLCV顺序排列
    return df[['open_hfq', 'high_hfq', 'low_hfq', 
              'close_hfq', 'volume']].values


# 主程序
if __name__ == "__main__":
    # 从数据库获取数据
    stock_code = '000001.SZ'  # 以平安银行为例
    start_date = '2024-06-01'
    end_date = '2024-12-31'
    
    # 获取股票数据
    data = get_stock_data(stock_code, start_date, end_date)
    
    # 训练模型
    window_size = 10
    episodes = 50  # 可以根据需要调整训练轮数
    agent, rewards = train_dqn(data, window_size=window_size, episodes=episodes)
    
    # 绘制训练过程中的奖励变化
    plt.plot(rewards)
    plt.title(f'Training Rewards - {stock_code}')
    plt.xlabel('Episode')
    plt.ylabel('Total Reward')
    plt.show()
    
    # 评估模型
    print(f"\n评估模型在 {stock_code} 上的表现：")
    evaluate_agent(agent, data, window_size)