# 分析当天各市场涨跌个股数量
# 分析各市场不同涨跌幅分组下的个股数量

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from get_data.db_config import get_engine
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'PingFang SC']
plt.rcParams['axes.unicode_minus'] = False

class MarketAnalyzer:
    def __init__(self):
        self.engine = get_engine()
        self.current_date = None
        self.market_mapping = {
            ('主板', 'SSE'): '上海主板',
            ('科创板', 'SSE'): '科创板',
            ('主板', 'SZSE'): '深圳主板',
            ('创业板', 'SZSE'): '创业板',
            ('北交所', 'BSE'): '北交所'
        }
        
    def get_latest_trade_date(self):
        """获取最新交易日期"""
        query = """
        SELECT MAX(trade_date) as latest_date 
        FROM daily_basic 
        WHERE trade_date <= CURDATE()
        """
        result = pd.read_sql(query, self.engine)
        return result['latest_date'].iloc[0]
    
    def get_stock_basic_info(self):
        """获取股票基础信息"""
        query = """
        SELECT ts_code, name, market, exchange, list_date
        FROM stock_basic 
        WHERE list_status = 'L'
        """
        return pd.read_sql(query, self.engine)
    
    def analyze_daily_ups_downs(self, trade_date):
        """分析当天各市场涨跌个股数量 - 优化版本"""
        # 使用SQL直接统计，避免Python循环
        query = f"""
        SELECT 
            sb.market,
            sb.exchange,
            SUM(CASE WHEN sf.pct_change > 0 THEN 1 ELSE 0 END) as up_count,
            SUM(CASE WHEN sf.pct_change < 0 THEN 1 ELSE 0 END) as down_count,
            SUM(CASE WHEN sf.pct_change = 0 THEN 1 ELSE 0 END) as flat_count,
            COUNT(*) as total_count
        FROM stk_factor sf
        JOIN stock_basic sb ON sf.ts_code = sb.ts_code
        WHERE sf.trade_date = '{trade_date}'
        AND sb.list_status = 'L'
        AND sf.pct_change IS NOT NULL
        GROUP BY sb.market, sb.exchange
        ORDER BY sb.market, sb.exchange
        """
        
        df = pd.read_sql(query, self.engine)
        
        # 添加market_name字段
        df['market_name'] = df.apply(lambda x: self.market_mapping.get((x['market'], x['exchange']), '其他'), axis=1)
        
        # 构建完整结果（确保所有市场都有数据）
        all_markets = ['上海主板', '科创板', '深圳主板', '创业板', '北交所']
        result = []
        
        for market in all_markets:
            market_data = df[df['market_name'] == market]
            if len(market_data) > 0:
                row = market_data.iloc[0]
                up_count = int(row['up_count'])
                down_count = int(row['down_count'])
                flat_count = int(row['flat_count'])
                total_count = int(row['total_count'])
                
                result.append({
                    '市场': market,
                    '上涨家数': up_count,
                    '下跌家数': down_count,
                    '平盘家数': flat_count,
                    '总家数': total_count,
                    '上涨占比%': round(up_count / total_count * 100, 2) if total_count > 0 else 0,
                    '下跌占比%': round(down_count / total_count * 100, 2) if total_count > 0 else 0
                })
            else:
                result.append({
                    '市场': market,
                    '上涨家数': 0,
                    '下跌家数': 0,
                    '平盘家数': 0,
                    '总家数': 0,
                    '上涨占比%': 0,
                    '下跌占比%': 0
                })
        
        return pd.DataFrame(result)
    
    def analyze_pct_change_distribution(self, trade_date):
        """分析各市场不同涨跌幅分组下的个股数量 - 优化版本"""
        # 使用SQL直接计算涨跌幅分组，避免Python循环
        query = f"""
        SELECT 
            CASE 
                WHEN (sb.market = 'zhu_ban' AND sb.exchange = 'SSE') THEN 'sh_main'
                WHEN (sb.market = 'ke_chuang_ban' AND sb.exchange = 'SSE') THEN 'ke_chuang'
                WHEN (sb.market = 'zhu_ban' AND sb.exchange = 'SZSE') THEN 'sz_main'
                WHEN (sb.market = 'chuang_ye_ban' AND sb.exchange = 'SZSE') THEN 'chuang_ye'
                WHEN (sb.market = 'bei_jiao_suo' AND sb.exchange = 'BSE') THEN 'bei_jiao'
                ELSE 'other'
            END as market_code,
            sb.market,
            sb.exchange,
            CASE 
                WHEN sf.pct_change <= -10 THEN 'down_limit'
                WHEN sf.pct_change <= -7 THEN 'big_down'
                WHEN sf.pct_change <= -3 THEN 'mid_down'
                WHEN sf.pct_change < 0 THEN 'small_down'
                WHEN sf.pct_change = 0 THEN 'flat'
                WHEN sf.pct_change < 3 THEN 'small_up'
                WHEN sf.pct_change < 7 THEN 'mid_up'
                WHEN sf.pct_change < 10 THEN 'big_up'
                ELSE 'up_limit'
            END as pct_group,
            COUNT(*) as count
        FROM stk_factor sf
        JOIN stock_basic sb ON sf.ts_code = sb.ts_code
        WHERE sf.trade_date = '{trade_date}'
        AND sb.list_status = 'L'
        AND sf.pct_change IS NOT NULL
        GROUP BY sb.market, sb.exchange, pct_group
        ORDER BY sb.market, sb.exchange, pct_group
        """
        
        df = pd.read_sql(query, self.engine)
        
        # 添加market_name字段
        df['market_name'] = df.apply(lambda x: self.market_mapping.get((x['market'], x['exchange']), '其他'), axis=1)
        
        # 创建组别映射
        group_mapping = {
            'down_limit': '跌停(-10%以下)',
            'big_down': '大跌(-10%~-7%)',
            'mid_down': '中跌(-7%~-3%)',
            'small_down': '小跌(-3%~0%)',
            'flat': '平盘(0%)',
            'small_up': '小涨(0%~3%)',
            'mid_up': '中涨(3%~7%)',
            'big_up': '大涨(7%~10%)',
            'up_limit': '涨停(10%以上)'
        }
        df['pct_group_name'] = df['pct_group'].map(group_mapping)
        
        # 构建结果表格
        markets = ['上海主板', '科创板', '深圳主板', '创业板', '北交所']
        groups = ['跌停(-10%以下)', '大跌(-10%~-7%)', '中跌(-7%~-3%)', '小跌(-3%~0%)', 
                 '平盘(0%)', '小涨(0%~3%)', '中涨(3%~7%)', '大涨(7%~10%)', '涨停(10%以上)']
        
        result = []
        for market in markets:
            row = {}
            row['市场'] = market
            market_data = df[df['market_name'] == market]
            total = 0
            
            for group in groups:
                count = int(market_data[market_data['pct_group_name'] == group]['count'].sum())
                row[group] = count
                total += count
            
            row['总计'] = int(total)
            result.append(row)
        
        return pd.DataFrame(result)
    
    def generate_report(self):
        """生成完整报告 - 不保存文件，仅显示结果"""
        # 获取最新交易日期
        trade_date = self.get_latest_trade_date()
        self.current_date = trade_date
        
        print(f"正在分析 {trade_date} 的市场数据...")
        
        # 1. 各市场当天涨跌个股数量
        ups_downs = self.analyze_daily_ups_downs(trade_date)
        
        # 2. 各市场不同涨跌幅分组下的个股数量
        pct_distribution = self.analyze_pct_change_distribution(trade_date)
        
        print(f"\n=== {trade_date} 市场分析报告 ===")
        print("\n1. 各市场涨跌统计:")
        print(ups_downs.to_string(index=False))
        
        print("\n2. 各市场涨跌幅分布:")
        print(pct_distribution.to_string(index=False))
        
        return {
            'trade_date': trade_date,
            'ups_downs': ups_downs,
            'pct_distribution': pct_distribution
        }

def main():
    analyzer = MarketAnalyzer()
    results = analyzer.generate_report()

if __name__ == "__main__":
    main()
