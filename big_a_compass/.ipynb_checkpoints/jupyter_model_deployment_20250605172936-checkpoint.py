#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jupyter适配版：训练好的强化学习模型部署和使用

这个文件包含了所有必要的类和函数，可以直接在Jupyter中运行
无需导入其他自定义模块

使用方法：
1. 在Jupyter中运行所有cell
2. 修改model_path为你的实际模型路径
3. 运行main()函数

作者: AI Assistant
日期: 2025-06-05
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'PingFang SC']
plt.rcParams['axes.unicode_minus'] = False

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque
import random
import sys
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp
import os
from sklearn.preprocessing import StandardScaler
import joblib

# 检查CUDA是否可用
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"🖥️  使用设备: {device}")

# 数据文件路径
TRAINING_DATA_DIR = "training_data"

class ConceptTradingEnvironmentGPU:
    """GPU优化的概念板块交易环境"""
    
    def __init__(self, data, lookback_days=20, holding_days=5, device='cuda'):
        self.device = torch.device(device) if isinstance(device, str) else device
        self.data = data.copy()
        self.lookback_days = lookback_days
        self.holding_days = holding_days
        self.current_step = 0
        
        # 获取唯一交易日期数量
        unique_dates = len(data['trade_date'].unique())
        self.max_steps = unique_dates - holding_days - lookback_days
        
        # 状态特征维度
        self.feature_cols = [
            'pct_change', 'vol_ratio', 'turnover_rate', 
            'pe_ttm', 'pb_mrq', 'ma5', 'ma10', 'ma20',
            'rsi', 'momentum_5', 'momentum_10', 'momentum_20',
            'bb_upper', 'bb_lower', 'bb_mid', 'macd', 'macd_signal',
            'vol_ma5', 'vol_ma20', 'price_vol_corr'
        ]
        
        # 概念代码
        self.concept_codes = sorted(data['ts_code'].unique())[:100]
        self.state_dim = len(self.feature_cols) * len(self.concept_codes)
        self.action_dim = len(self.concept_codes) + 1
        
        # 预处理数据
        self._preprocess_data_to_gpu()
        self.reset()
    
    def _preprocess_data_to_gpu(self):
        """预处理数据并转换为GPU张量"""
        print("🔄 预处理数据...")
        
        # 计算技术指标
        processed_data = []
        for concept in self.concept_codes:
            concept_data = self.data[self.data['ts_code'] == concept].copy()
            if len(concept_data) > 0:
                concept_data = self._calculate_enhanced_features(concept_data)
                processed_data.append(concept_data)
        
        self.processed_data = pd.concat(processed_data, ignore_index=True)
        self.dates = sorted(self.processed_data['trade_date'].unique())
        
        # 转换为GPU张量
        self.feature_tensors = {}
        self.price_tensors = {}
        
        for date in self.dates:
            day_data = self.processed_data[self.processed_data['trade_date'] == date]
            
            features = []
            prices = []
            
            for concept in self.concept_codes:
                concept_day_data = day_data[day_data['ts_code'] == concept]
                if len(concept_day_data) > 0:
                    feature_values = concept_day_data[self.feature_cols].iloc[0].values
                    price = concept_day_data['close'].iloc[0]
                else:
                    feature_values = np.zeros(len(self.feature_cols))
                    price = 0.0
                
                features.append(feature_values)
                prices.append(price)
            
            # 转换为GPU张量
            self.feature_tensors[date] = torch.FloatTensor(features).to(self.device)
            self.price_tensors[date] = torch.FloatTensor(prices).to(self.device)
    
    def _calculate_enhanced_features(self, df):
        """计算增强的技术指标特征"""
        df = df.copy()
        df = df.sort_values('trade_date')
        
        # 基础特征
        df['vol_ratio'] = df['vol'] / df['vol'].rolling(20).mean()
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma10'] = df['close'].rolling(10).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        
        # 动量指标
        df['momentum_5'] = df['close'].pct_change(5)
        df['momentum_10'] = df['close'].pct_change(10)
        df['momentum_20'] = df['close'].pct_change(20)
        
        # RSI指标
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        rolling_mean = df['close'].rolling(20).mean()
        rolling_std = df['close'].rolling(20).std()
        df['bb_upper'] = rolling_mean + (rolling_std * 2)
        df['bb_lower'] = rolling_mean - (rolling_std * 2)
        df['bb_mid'] = rolling_mean
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # 成交量指标
        df['vol_ma5'] = df['vol'].rolling(5).mean()
        df['vol_ma20'] = df['vol'].rolling(20).mean()
        
        # 价量相关性
        df['price_vol_corr'] = df['close'].rolling(20).corr(df['vol'])
        
        # 填充缺失值
        df = df.bfill().fillna(0)
        
        return df
    
    def reset(self):
        """重置环境"""
        self.current_step = self.lookback_days
        return self._get_state()
    
    def _get_state(self):
        """获取当前状态"""
        if self.current_step >= len(self.dates):
            return torch.zeros(self.state_dim).to(self.device)
        
        current_date = self.dates[self.current_step]
        
        if current_date in self.feature_tensors:
            features = self.feature_tensors[current_date]
            state = features.flatten()
        else:
            state = torch.zeros(self.state_dim).to(self.device)
        
        return state
    
    def step(self, action):
        """执行动作"""
        reward = 0
        done = False
        
        if action == len(self.concept_codes):
            selected_concept = None
        else:
            selected_concept = self.concept_codes[action]
        
        if self.current_step >= len(self.dates) or self.current_step + self.holding_days >= len(self.dates):
            done = True
            return None, 0, done, {'selected_concept': selected_concept}
        
        current_date = self.dates[self.current_step]
        future_date = self.dates[min(self.current_step + self.holding_days, len(self.dates) - 1)]
        
        if selected_concept is not None:
            concept_idx = self.concept_codes.index(selected_concept)
            
            if current_date in self.price_tensors and future_date in self.price_tensors:
                current_price = self.price_tensors[current_date][concept_idx]
                future_price = self.price_tensors[future_date][concept_idx]
                
                if current_price > 0:
                    reward = ((future_price - current_price) / current_price).item()
                else:
                    reward = -0.01
            else:
                reward = -0.01
        else:
            reward = 0
        
        self.current_step += 1
        
        if self.current_step >= len(self.dates) - self.holding_days:
            done = True
        
        next_state = self._get_state() if not done else None
        
        return next_state, reward, done, {'selected_concept': selected_concept}

class DQNNetworkGPU(nn.Module):
    """GPU优化的深度Q网络"""
    
    def __init__(self, state_dim, action_dim, hidden_dims=[2048, 1024, 512, 256, 128]):
        super(DQNNetworkGPU, self).__init__()
        
        layers = []
        prev_dim = state_dim
        
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.3 if i < len(hidden_dims) - 1 else 0.1)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Xavier初始化权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        return self.network(x)

class DQNAgentGPU:
    """GPU优化的DQN智能体"""
    
    def __init__(self, state_dim, action_dim, lr=1e-4, gamma=0.95, epsilon=1.0, 
                 epsilon_decay=0.995, epsilon_min=0.01, batch_size=512):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.device = device
        self.batch_size = batch_size
        
        # 神经网络
        self.q_network = DQNNetworkGPU(state_dim, action_dim).to(self.device)
        self.target_network = DQNNetworkGPU(state_dim, action_dim).to(self.device)
        
        # 优化器
        self.optimizer = optim.AdamW(self.q_network.parameters(), lr=lr, weight_decay=1e-5)
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=100, gamma=0.95)
        
        # 初始化目标网络
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # 经验回放
        self.memory = deque(maxlen=100000)
        self.update_target_frequency = 50
        self.step_count = 0
    
    def act(self, state):
        """选择动作"""
        if np.random.random() <= self.epsilon:
            return random.randrange(self.action_dim)
        
        if isinstance(state, np.ndarray):
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        else:
            state_tensor = state.unsqueeze(0) if state.dim() == 1 else state
            
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
        return q_values.argmax().item()

class ConceptRotationRLGPU:
    """GPU优化的概念板块轮动强化学习策略"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.agent = None
        self.train_data = None
        self.val_data = None
        self.test_data = None
        self.data = None
        
        # 创建保存模型的目录
        self.model_dir = f"models_gpu_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.model_dir, exist_ok=True)
    
    def load_and_prepare_data(self):
        """加载和预处理数据"""
        print("🔄 加载和预处理数据...")
        
        try:
            # 加载数据文件
            daily_file = os.path.join(TRAINING_DATA_DIR, "ths_daily.csv")
            
            print(f"📂 加载数据文件: {daily_file}")
            data = pd.read_csv(daily_file)
            data['trade_date'] = pd.to_datetime(data['trade_date'])
            
            print(f"✅ 数据加载完成: {len(data)} 条记录")
            
            # 并行计算技术指标
            unique_concepts = data['ts_code'].unique()
            
            def process_concept(concept):
                concept_data = data[data['ts_code'] == concept].copy()
                return self._calculate_enhanced_features(concept_data)
            
            print(f"⚙️  计算技术指标...")
            processed_data = []
            for concept in unique_concepts:
                processed_data.append(process_concept(concept))
            
            self.data = pd.concat(processed_data, ignore_index=True)
            print(f"✅ 技术指标计算完成")
            
            # 数据集划分
            self._split_data()
            
            return self.data
            
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise e
    
    def _calculate_enhanced_features(self, df):
        """计算技术指标特征"""
        df = df.copy()
        df = df.sort_values('trade_date')
        
        # 基础特征
        df['vol_ratio'] = df['vol'] / df['vol'].rolling(20).mean()
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma10'] = df['close'].rolling(10).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        
        # 动量指标
        df['momentum_5'] = df['close'].pct_change(5)
        df['momentum_10'] = df['close'].pct_change(10)
        df['momentum_20'] = df['close'].pct_change(20)
        
        # RSI指标
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        rolling_mean = df['close'].rolling(20).mean()
        rolling_std = df['close'].rolling(20).std()
        df['bb_upper'] = rolling_mean + (rolling_std * 2)
        df['bb_lower'] = rolling_mean - (rolling_std * 2)
        df['bb_mid'] = rolling_mean
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # 成交量指标
        df['vol_ma5'] = df['vol'].rolling(5).mean()
        df['vol_ma20'] = df['vol'].rolling(20).mean()
        
        # 价量相关性
        df['price_vol_corr'] = df['close'].rolling(20).corr(df['vol'])
        
        # 填充缺失值
        df = df.bfill().fillna(0)
        
        return df
    
    def _split_data(self):
        """划分训练集、验证集和测试集"""
        dates = sorted(self.data['trade_date'].unique())
        total_days = len(dates)
        
        train_end_idx = int(total_days * 0.6)
        val_end_idx = int(total_days * 0.8)
        
        train_end_date = dates[train_end_idx - 1]
        val_end_date = dates[val_end_idx - 1]
        
        self.train_data = self.data[self.data['trade_date'] <= train_end_date].copy()
        self.val_data = self.data[
            (self.data['trade_date'] > train_end_date) & 
            (self.data['trade_date'] <= val_end_date)
        ].copy()
        self.test_data = self.data[self.data['trade_date'] > val_end_date].copy()
        
        print(f"📊 数据集划分:")
        print(f"   训练集: {len(self.train_data)} 条记录")
        print(f"   验证集: {len(self.val_data)} 条记录")
        print(f"   测试集: {len(self.test_data)} 条记录")
    
    def load_checkpoint(self, checkpoint_path):
        """加载检查点"""
        try:
            checkpoint = torch.load(checkpoint_path, map_location=device)
            
            # 重新创建智能体（如果还没有）
            if self.agent is None:
                temp_env = ConceptTradingEnvironmentGPU(self.train_data, device=device)
                self.agent = DQNAgentGPU(
                    state_dim=temp_env.state_dim,
                    action_dim=temp_env.action_dim,
                    batch_size=512
                )
            
            # 加载模型状态
            self.agent.q_network.load_state_dict(checkpoint['model_state_dict'])
            self.agent.target_network.load_state_dict(checkpoint['target_state_dict'])
            self.agent.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.agent.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            self.agent.epsilon = checkpoint['epsilon']
            self.agent.step_count = checkpoint['step_count']
            
            episode = checkpoint['episode']
            best_reward = checkpoint['best_reward']
            episode_rewards = checkpoint['episode_rewards']
            episode_losses = checkpoint['episode_losses']
            
            print(f"✅ 检查点加载成功:")
            print(f"   Episode: {episode}")
            print(f"   最佳奖励: {best_reward:.4f}")
            print(f"   当前探索率: {self.agent.epsilon:.4f}")
            print(f"   训练步数: {self.agent.step_count}")
            
            return episode, best_reward, episode_rewards, episode_losses
            
        except Exception as e:
            print(f"❌ 检查点加载失败: {str(e)}")
            raise e

class JupyterModelDeployment:
    """Jupyter适配的模型部署类"""
    
    def __init__(self, model_path, data_path="training_data"):
        self.model_path = model_path
        self.data_path = data_path
        self.strategy = None
        self.agent = None
        
        print(f"🚀 Jupyter模型部署初始化")
        print(f"📁 模型路径: {model_path}")
        print(f"📂 数据路径: {data_path}")
    
    def load_trained_model(self):
        """加载训练好的模型"""
        print("\n🔄 加载训练好的模型...")
        
        try:
            # 1. 创建策略实例并加载数据
            self.strategy = ConceptRotationRLGPU()
            self.strategy.load_and_prepare_data()
            
            # 2. 加载模型检查点
            if "checkpoint" in self.model_path:
                episode, best_reward, rewards, losses = self.strategy.load_checkpoint(self.model_path)
                print(f"✅ 检查点加载成功 - Episode: {episode}, 最佳奖励: {best_reward:.4f}")
            else:
                # 如果是完整模型文件
                checkpoint = torch.load(self.model_path, map_location=device)
                
                # 创建环境和智能体
                temp_env = ConceptTradingEnvironmentGPU(self.strategy.train_data)
                self.strategy.agent = DQNAgentGPU(
                    state_dim=temp_env.state_dim,
                    action_dim=temp_env.action_dim,
                    batch_size=512
                )
                
                # 加载模型权重
                self.strategy.agent.q_network.load_state_dict(checkpoint['model_state_dict'])
                self.strategy.agent.target_network.load_state_dict(checkpoint.get('target_state_dict', checkpoint['model_state_dict']))
                print(f"✅ 完整模型加载成功")
            
            # 设置为评估模式
            self.strategy.agent.q_network.eval()
            self.strategy.agent.epsilon = 0  # 不进行随机探索
            
            self.agent = self.strategy.agent
            
            print(f"🎯 模型状态:")
            print(f"   网络参数: {sum(p.numel() for p in self.agent.q_network.parameters()):,}")
            print(f"   探索率: {self.agent.epsilon}")
            print(f"   设备: {self.agent.device}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {str(e)}")
            return False
    
    def predict_best_concept(self, current_date=None):
        """预测当前最佳概念板块"""
        print(f"\n🔮 预测最佳概念板块...")
        
        if current_date is None:
            available_dates = sorted(self.strategy.data['trade_date'].unique())
            current_date = available_dates[-1]
        
        print(f"📅 预测日期: {current_date}")
        
        # 创建测试环境
        test_env = ConceptTradingEnvironmentGPU(self.strategy.test_data)
        
        # 找到对应的日期索引
        test_dates = sorted(self.strategy.test_data['trade_date'].unique())
        if current_date not in test_dates:
            print(f"⚠️  指定日期不在测试数据中，使用最近日期: {test_dates[-1]}")
            current_date = test_dates[-1]
        
        date_idx = test_dates.index(current_date)
        test_env.current_step = date_idx + test_env.lookback_days
        
        # 获取当前状态
        state = test_env._get_state()
        
        # 进行预测
        with torch.no_grad():
            if isinstance(state, torch.Tensor):
                state_tensor = state.unsqueeze(0)
            else:
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.agent.device)
            
            q_values = self.agent.q_network(state_tensor)
            q_values_cpu = q_values.cpu().numpy().flatten()
        
        # 分析预测结果
        action = np.argmax(q_values_cpu)
        confidence = q_values_cpu[action]
        
        if action == len(test_env.concept_codes):
            prediction = {
                'action': '不投资',
                'concept_code': None,
                'concept_name': '现金',
                'confidence': confidence,
                'q_value': confidence
            }
        else:
            selected_concept = test_env.concept_codes[action]
            prediction = {
                'action': '投资',
                'concept_code': selected_concept,
                'concept_name': selected_concept,
                'confidence': confidence,
                'q_value': confidence
            }
        
        # 显示前5个最佳选择
        top_5_indices = np.argsort(q_values_cpu)[-5:][::-1]
        
        print(f"\n🎯 预测结果:")
        print(f"   推荐动作: {prediction['action']}")
        print(f"   概念代码: {prediction['concept_code']}")
        print(f"   置信度: {prediction['confidence']:.4f}")
        
        print(f"\n📊 前5个选择:")
        for i, idx in enumerate(top_5_indices, 1):
            if idx == len(test_env.concept_codes):
                concept_name = "现金/不投资"
            else:
                concept_name = test_env.concept_codes[idx]
            print(f"   {i}. {concept_name}: {q_values_cpu[idx]:.4f}")
        
        return prediction, q_values_cpu
    
    def backtest_strategy(self):
        """回测策略效果"""
        print(f"\n📈 开始策略回测...")
        
        # 使用测试集进行回测
        test_env = ConceptTradingEnvironmentGPU(self.strategy.test_data)
        
        # 回测记录
        backtest_results = []
        portfolio_value = 1.0
        
        state = test_env.reset()
        step = 0
        
        while True:
            # 获取当前日期
            current_date = test_env.dates[test_env.current_step] if test_env.current_step < len(test_env.dates) else None
            if current_date is None:
                break
            
            # 进行预测
            action = self.agent.act(state)
            next_state, reward, done, info = test_env.step(action)
            
            # 更新投资组合价值
            portfolio_value *= (1 + reward)
            
            # 记录结果
            selected_concept = info['selected_concept']
            backtest_results.append({
                'date': current_date,
                'action': action,
                'selected_concept': selected_concept,
                'reward': reward,
                'portfolio_value': portfolio_value,
                'step': step
            })
            
            step += 1
            
            if step % 20 == 0:
                print(f"   回测进度: {step}/{test_env.max_steps}, 当前收益: {(portfolio_value-1)*100:.2f}%")
            
            if done:
                break
                
            state = next_state
        
        # 转换为DataFrame
        backtest_df = pd.DataFrame(backtest_results)
        
        # 计算回测指标
        total_return = (portfolio_value - 1) * 100
        daily_returns = backtest_df['reward'].values
        
        # 风险指标
        volatility = np.std(daily_returns) * np.sqrt(252) * 100
        sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(252) if np.std(daily_returns) > 0 else 0
        
        # 最大回撤
        portfolio_values = backtest_df['portfolio_value'].values
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (peak - portfolio_values) / peak
        max_drawdown = np.max(drawdown) * 100
        
        # 胜率
        win_rate = (daily_returns > 0).sum() / len(daily_returns) * 100
        
        print(f"\n📊 回测结果汇总:")
        print(f"   📅 回测期间: {backtest_df['date'].min()} 到 {backtest_df['date'].max()}")
        print(f"   📈 总收益率: {total_return:.2f}%")
        print(f"   📉 年化波动率: {volatility:.2f}%")
        print(f"   ⚡ 夏普比率: {sharpe_ratio:.4f}")
        print(f"   📉 最大回撤: {max_drawdown:.2f}%")
        print(f"   🎯 胜率: {win_rate:.1f}%")
        print(f"   📊 交易次数: {len(backtest_df)}")
        
        return backtest_df
    
    def plot_backtest_results(self, backtest_df):
        """绘制回测结果"""
        print(f"\n📊 绘制回测结果图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 投资组合价值曲线
        axes[0, 0].plot(backtest_df['date'], backtest_df['portfolio_value'])
        axes[0, 0].set_title('投资组合价值曲线')
        axes[0, 0].set_xlabel('日期')
        axes[0, 0].set_ylabel('投资组合价值')
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True)
        
        # 每日收益分布
        axes[0, 1].hist(backtest_df['reward'], bins=50, alpha=0.7, color='skyblue')
        axes[0, 1].set_title('每日收益分布')
        axes[0, 1].set_xlabel('日收益率')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].grid(True)
        
        # 累积收益
        cumulative_returns = (backtest_df['portfolio_value'] - 1) * 100
        axes[1, 0].plot(backtest_df['date'], cumulative_returns)
        axes[1, 0].set_title('累积收益率 (%)')
        axes[1, 0].set_xlabel('日期')
        axes[1, 0].set_ylabel('累积收益率 (%)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True)
        
        # 回撤曲线
        portfolio_values = backtest_df['portfolio_value'].values
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (peak - portfolio_values) / peak * 100
        axes[1, 1].fill_between(backtest_df['date'], drawdown, 0, alpha=0.3, color='red')
        axes[1, 1].set_title('回撤曲线 (%)')
        axes[1, 1].set_xlabel('日期')
        axes[1, 1].set_ylabel('回撤 (%)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.show()
        
        return fig

# Jupyter使用示例函数
def jupyter_demo(model_path):
    """Jupyter中的完整演示"""
    print("🚀 Jupyter模型部署演示")
    print("=" * 60)
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("💡 请检查路径是否正确")
        return None
    
    try:
        # 1. 初始化部署系统
        deployment = JupyterModelDeployment(model_path)
        
        # 2. 加载训练好的模型
        if not deployment.load_trained_model():
            return None
        
        # 3. 进行预测
        prediction, q_values = deployment.predict_best_concept()
        
        # 4. 策略回测
        backtest_df = deployment.backtest_strategy()
        
        # 5. 绘制结果
        fig = deployment.plot_backtest_results(backtest_df)
        
        print("\n🎉 演示完成！")
        
        return {
            'deployment': deployment,
            'prediction': prediction,
            'backtest_df': backtest_df,
            'fig': fig
        }
        
    except Exception as e:
        print(f"❌ 演示过程出现错误: {str(e)}")
        return None

# 主函数
def main():
    """主函数 - 在Jupyter中调用"""
    print("📝 Jupyter使用说明:")
    print("1. 修改下面的model_path为你的实际模型路径")
    print("2. 运行 jupyter_demo(model_path)")
    print("3. 查看预测结果和回测效果")
    print()
    
    # 🔧 在这里修改为你的实际模型路径
    model_path = "models_gpu_20250605_172936/checkpoint_episode_200.pth"
    
    # 检查常见的模型路径
    possible_paths = [
        "models_gpu_20250605_172936/checkpoint_episode_50.pth",
        "models_gpu_20250605_172936/checkpoint_episode_100.pth", 
        "models_gpu_20250605_172936/checkpoint_episode_150.pth",
        "models_gpu_20250605_172936/checkpoint_episode_200.pth",
        "models_gpu_20250605_172936/dqn_gpu_model.pth"
    ]
    
    print("🔍 检查可能的模型路径:")
    for path in possible_paths:
        exists = "✅" if os.path.exists(path) else "❌"
        print(f"   {exists} {path}")
    
    # 找到第一个存在的模型文件
    for path in possible_paths:
        if os.path.exists(path):
            model_path = path
            print(f"\n🎯 使用模型: {model_path}")
            break
    else:
        print(f"\n⚠️  没有找到模型文件，请检查路径")
        return None
    
    # 运行演示
    return jupyter_demo(model_path)

if __name__ == "__main__":
    # 如果直接运行此文件
    results = main()
else:
    # 如果在Jupyter中导入
    print("🎯 Jupyter模块已加载！")
    print("📋 使用方法:")
    print("   results = main()  # 自动检测模型并运行演示")
    print("   # 或者")
    print("   results = jupyter_demo('你的模型路径.pth')  # 指定模型路径")