{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# 🚀 强化学习概念板块轮动模型 - Jupyter快速入门\n", "\n", "这个notebook演示如何使用训练好的强化学习模型进行概念板块预测和策略回测。\n", "\n", "## 📋 使用步骤:\n", "1. 运行第一个cell导入所有必要的模块\n", "2. 修改模型路径(如果需要)\n", "3. 运行模型加载和预测\n", "4. 查看回测结果\n", "\n", "作者: AI Assistant  \n", "日期: 2025-06-05\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["## 1. 导入模块和初始化\n", "\n", "运行这个cell导入所有必要的模块（这可能需要几秒钟）：\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入完整的模型部署模块\n", "exec(open('big_a_compass/jupyter_model_deployment_20250605172936.py').read())\n", "\n", "print(\"✅ 所有模块导入完成！\")\n", "print(f\"🖥️  当前使用设备: {device}\")\n", "print(f\"📂 工作目录: {os.getcwd()}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 2. 检查可用的模型文件\n", "\n", "让我们看看有哪些训练好的模型可以使用：\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "\n", "# 查找所有可能的模型文件\n", "model_patterns = [\n", "    \"models_gpu_*/checkpoint_*.pth\",\n", "    \"models_gpu_*/dqn_*.pth\",\n", "    \"big_a_compass/models_*/checkpoint_*.pth\",\n", "    \"big_a_compass/models_*/dqn_*.pth\"\n", "]\n", "\n", "all_models = []\n", "for pattern in model_patterns:\n", "    all_models.extend(glob.glob(pattern))\n", "\n", "print(\"🔍 找到的模型文件:\")\n", "if all_models:\n", "    for i, model in enumerate(all_models, 1):\n", "        size = os.path.getsize(model) / (1024*1024)  # MB\n", "        mtime = os.path.getmtime(model)\n", "        mtime_str = pd.to_datetime(mtime, unit='s').strftime('%Y-%m-%d %H:%M:%S')\n", "        print(f\"   {i}. {model} ({size:.1f}MB, {mtime_str})\")\n", "else:\n", "    print(\"   ❌ 没有找到模型文件\")\n", "    print(\"   💡 请确保已经完成模型训练，或者模型文件在正确的路径\")\n", "\n", "# 选择最新的模型文件\n", "if all_models:\n", "    # 按修改时间排序，选择最新的\n", "    latest_model = max(all_models, key=os.path.getmtime)\n", "    print(f\"\\n🎯 将使用最新模型: {latest_model}\")\n", "    MODEL_PATH = latest_model\n", "else:\n", "    MODEL_PATH = None\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 3. 加载训练好的模型\n", "\n", "现在让我们加载模型并初始化部署系统：\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if MODEL_PATH is None:\n", "    print(\"❌ 没有找到可用的模型文件，请先训练模型\")\n", "else:\n", "    # 初始化模型部署系统\n", "    deployment = JupyterModelDeployment(MODEL_PATH)\n", "    \n", "    # 加载训练好的模型\n", "    success = deployment.load_trained_model()\n", "    \n", "    if success:\n", "        print(\"\\n🎉 模型加载成功！可以开始使用了。\")\n", "    else:\n", "        print(\"\\n❌ 模型加载失败，请检查模型文件。\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 4. 进行概念板块预测\n", "\n", "让我们使用模型预测当前最佳的概念板块：\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'deployment' in locals() and deployment.agent is not None:\n", "    # 进行预测\n", "    prediction, q_values = deployment.predict_best_concept()\n", "    \n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"📈 投资建议:\")\n", "    print(\"=\"*50)\n", "    \n", "    if prediction['concept_code']:\n", "        print(f\"✅ 推荐投资概念: {prediction['concept_code']}\")\n", "        print(f\"🎯 模型置信度: {prediction['confidence']:.4f}\")\n", "    else:\n", "        print(\"💰 推荐持有现金，暂不投资\")\n", "        print(f\"🎯 模型置信度: {prediction['confidence']:.4f}\")\n", "else:\n", "    print(\"❌ 请先成功加载模型\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 5. 策略回测分析\n", "\n", "现在让我们对策略进行完整的回测，看看历史表现如何：\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'deployment' in locals() and deployment.agent is not None:\n", "    # 进行策略回测\n", "    backtest_df = deployment.backtest_strategy()\n", "    \n", "    # 显示前10条回测记录\n", "    print(\"\\n📊 回测记录样例 (前10条):\")\n", "    print(backtest_df[['date', 'selected_concept', 'reward', 'portfolio_value']].head(10))\n", "    \n", "    # 保存回测结果\n", "    output_file = f\"backtest_results_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "    backtest_df.to_csv(output_file, index=False)\n", "    print(f\"\\n💾 回测结果已保存到: {output_file}\")\n", "else:\n", "    print(\"❌ 请先成功加载模型\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 6. 可视化回测结果\n", "\n", "绘制详细的回测结果图表：\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'backtest_df' in locals():\n", "    # 绘制回测结果图表\n", "    fig = deployment.plot_backtest_results(backtest_df)\n", "    \n", "    # 保存图表\n", "    fig_file = f\"backtest_charts_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.png\"\n", "    fig.savefig(fig_file, dpi=300, bbox_inches='tight')\n", "    print(f\"\\n💾 图表已保存到: {fig_file}\")\n", "else:\n", "    print(\"❌ 请先运行回测分析\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 7. 快速预测接口\n", "\n", "你可以使用这个函数对任意日期进行预测：\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def quick_predict(date_str=None):\n", "    \"\"\"快速预测接口\"\"\"\n", "    if 'deployment' not in locals() or deployment.agent is None:\n", "        print(\"❌ 请先加载模型\")\n", "        return\n", "    \n", "    if date_str:\n", "        target_date = pd.to_datetime(date_str)\n", "    else:\n", "        target_date = None\n", "    \n", "    prediction, q_values = deployment.predict_best_concept(target_date)\n", "    \n", "    print(f\"🔮 预测结果:\")\n", "    if prediction['concept_code']:\n", "        print(f\"   推荐概念: {prediction['concept_code']}\")\n", "    else:\n", "        print(f\"   推荐: 持有现金\")\n", "    print(f\"   置信度: {prediction['confidence']:.4f}\")\n", "    \n", "    return prediction\n", "\n", "# 示例使用\n", "print(\"💡 使用示例:\")\n", "print(\"   quick_predict()  # 预测最新日期\")\n", "print(\"   quick_predict('2024-12-01')  # 预测指定日期\")\n", "\n", "# 进行一次示例预测\n", "if 'deployment' in locals() and deployment.agent is not None:\n", "    result = quick_predict()\n", "else:\n", "    print(\"\\n❌ 模型未加载，无法进行预测\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 🎉 使用完成！\n", "\n", "### 📋 总结\n", "\n", "现在你已经成功:\n", "1. ✅ 加载了训练好的强化学习模型\n", "2. ✅ 进行了概念板块预测\n", "3. ✅ 完成了策略回测分析\n", "4. ✅ 生成了可视化图表\n", "5. ✅ 获得了详细的分析报告\n", "\n", "### 🚀 下一步\n", "\n", "你可以:\n", "- 修改预测日期进行不同时间的预测\n", "- 调整模型参数重新训练\n", "- 集成到实盘交易系统\n", "- 添加更多技术指标\n", "\n", "### 📞 需要帮助？\n", "\n", "如果遇到任何问题，请检查:\n", "1. 数据文件是否在正确位置 (`training_data/ths_daily.csv`)\n", "2. 模型文件是否存在且完整\n", "3. Python环境是否安装了所有依赖\n", "4. GPU/CUDA是否正常工作（如果使用GPU）\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}