"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/6/11 18:21
@File: active_industry_analysis.py
@Version: 1.0
@Description: 活跃行业分析，从数据库读取申万行业指数日行情数据，计算活跃度并可视化展示。
"""
import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
from sqlalchemy import create_engine

# 将项目根目录添加到Python路径，以便导入自定义模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from get_data.db_config import DB_URL


def load_data(conn, days_to_load=60):
	"""
	从数据库连接中加载最近N天的数据。
	"""
	# 只加载最近N天的数据以提高效率
	today = pd.to_datetime('today')
	start_date = (today - pd.Timedelta(days=days_to_load)).strftime('%Y-%m-%d')
	query = f"SELECT * FROM sw_index_daily WHERE trade_date >= '{start_date}'"
	print(f"从数据库加载从 {start_date} 开始的数据...")
	df = pd.read_sql(query, conn, parse_dates=['trade_date'])

	df = df.sort_values(['ts_code', 'trade_date'])
	return df


def calculate_activity_indicators(df):
	"""
	计算板块活跃度相关指标
	V0.1---5 日跟 20日计算，感觉时间太长，改成以下：
	V0.2---3日跟 10日的计算
	V0.3---标准化+主观权重法
	"""
	print("正在计算活跃度指标...")
	# 计算原始指标
	df['pct_change_3d'] = df.groupby('ts_code')['pct_change'].transform(lambda x:x.rolling(3).sum())
	df['amount_3d_avg'] = df.groupby('ts_code')['amount'].transform(lambda x:x.rolling(3).mean())
	df['amount_10d_avg'] = df.groupby('ts_code')['amount'].transform(lambda x:x.rolling(10).mean())
	df['close_3d_avg'] = df.groupby('ts_code')['close'].transform(lambda x:x.rolling(3).mean())
	df['close_10d_avg'] = df.groupby('ts_code')['close'].transform(lambda x:x.rolling(10).mean())

	# 加上一个小的数以避免除以零
	df['vol_ratio_3_10'] = df['amount_3d_avg'] / (df['amount_10d_avg'] + 1e-6)
	df['price_ratio_3_10'] = df['close_3d_avg'] / (df['close_10d_avg'] + 1e-6)

	# 对各项指标进行z-score标准化；
	# 每一列的数值大致分布在 -2 ~ +2 之间（极端情况可能更大或更小），大部分集中在 -1 ~ +1。

	for col in ['pct_change_3d', 'vol_ratio_3_10', 'price_ratio_3_10']:
		mean = df[col].mean()
		std = df[col].std()
		if std == 0:
			std = 1e-6
		df[col + '_norm'] = (df[col] - mean) / std

	# 主观权重
	w = {'pct_change_3d_norm': 0.2, 'vol_ratio_3_10_norm': 0.6, 'price_ratio_3_10_norm': 0.2}
	# 计算加权得分
	df['active_score'] = (
		df['pct_change_3d_norm'].fillna(0) * w['pct_change_3d_norm'] +
		df['vol_ratio_3_10_norm'].fillna(0) * w['vol_ratio_3_10_norm'] +
		df['price_ratio_3_10_norm'].fillna(0) * w['price_ratio_3_10_norm']
	)
	return df


def score_and_select(df, top_n=10):
	"""
	根据加权打分计算活跃度，并选出最活跃的前 top_n 个板块
	"""
	print("正在进行打分和排序...")
	latest_date = df['trade_date'].max()
	latest_df = df[df['trade_date'] == latest_date].copy()

	# 过滤掉交易额过小的板块，避免噪音 (成交额单位: 万元)
	latest_df = latest_df[latest_df['amount'] > 10000]

	# 直接使用active_score
	active_board = latest_df.sort_values('active_score', ascending=False).head(top_n)

	# 获取排名最后的5个板块
	# active_board = latest_df.sort_values('active_score', ascending=False).tail(top_n)

	return active_board, latest_date


def plot_active_board(active_board, trade_date):
	"""
	绘制活跃板块得分柱状图
	"""
	# 设置中文字体
	plt.rcParams['font.sans-serif'] = ['STHeiti']
	plt.rcParams['axes.unicode_minus'] = False

	plt.figure(figsize=(12, 8))
	bars = plt.bar(active_board['name'], active_board['active_score'], color='skyblue')
	plt.title(f'近期最活跃的行业板块 ({trade_date.date()})', fontsize=16)
	plt.ylabel('活跃度得分', fontsize=12)
	plt.xticks(rotation=45, ha='right', fontsize=10)

	# 在柱状图上显示数值
	for bar in bars:
		yval = bar.get_height()
		plt.text(bar.get_x() + bar.get_width() / 2.0, yval, f'{yval:.2f}', va='bottom', ha='center')

	plt.tight_layout()
	plt.show()


def main():
	# 创建数据库连接
	engine = create_engine(DB_URL)

	# 加载数据：从数据库加载最近50 天的数据
	df = load_data(conn=engine, days_to_load=50)

	if df.empty:
		print("未能从数据库加载到数据，程序退出。")
		return

	# 计算指标
	df = calculate_activity_indicators(df)

	# 打分排序
	active_board, trade_date = score_and_select(df, top_n=10)

	# 输出结果
	print("\n=============================================")
	print(f"截止 {trade_date.date()}，最活跃的TOP 10行业板块：")
	print("=============================================")
	print(active_board[['ts_code', 'name', 'pct_change_3d', 'vol_ratio_3_10', 'price_ratio_3_10', 'active_score']].to_string())

	# 绘图展示
	plot_active_board(active_board, trade_date)


if __name__ == "__main__":
	main()