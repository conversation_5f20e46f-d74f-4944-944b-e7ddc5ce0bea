"""
@Author: ji<PERSON><PERSON>
@Date: 2025/6/12 15:51
@File: sw_stock_analysis.py
@Version: 1.0
@Description: 通过指定的概念板块，获取板块内的股票，并进行分析
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings


warnings.filterwarnings('ignore')

# 将项目根目录添加到Python路径，以便导入自定义模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from get_data.db_config import get_engine

# 导入活跃行业分析模块
from big_a_compass.sw.active_industry_analysis import load_data, calculate_activity_indicators, score_and_select


def get_active_industries(engine, top_n=10):
    """
    获取当前最活跃的行业板块，根据最近50天的交易日计算
    """
    print("正在获取最活跃的行业板块...")
    
    # 加载申万行业数据
    df = load_data(conn=engine, days_to_load=50)
    if df.empty:
        print("未能获取申万行业数据")
        return pd.DataFrame()
    
    # 计算活跃度指标
    df = calculate_activity_indicators(df)
    
    # 获取最活跃的板块
    active_board, _ = score_and_select(df, top_n=top_n)
    
    return active_board


def get_industry_stocks(engine, industry_codes):
    """
    获取指定行业的成分股，并过滤掉不符合条件的股票
    """
    print(f"正在获取行业成分股，行业代码: {industry_codes}")
    
    # 构建IN子句的代码列表
    codes_list = []
    for code in industry_codes:
        codes_list.append(f"'{code}'")
    codes_str = ','.join(codes_list)
    
    # 查询申万行业成分股 - 使用简单的字符串拼接，转义%字符
    query = (
        "SELECT DISTINCT sm.ts_code, sm.name, sm.l3_code, sm.l3_name, sb.list_date, sb.list_status "
        "FROM sw_member sm "
        "JOIN stock_basic sb ON sm.ts_code = sb.ts_code "
        "WHERE sm.l3_code IN (" + codes_str + ") "
        "AND sm.is_new = 'Y' "
        "AND sb.list_status = 'L' "
        "AND sb.name NOT LIKE '%%ST%%' "
        "AND sb.name NOT LIKE '%%*ST%%' "
        "AND sb.name NOT LIKE '%%退市%%' "
        "AND sb.list_date <= DATE_SUB(CURDATE(), INTERVAL 30 DAY)"
    )
    
    # 执行查询
    from sqlalchemy import text
    stocks_df = pd.read_sql(text(query), engine, parse_dates=['list_date'])
    print(f"获取到 {len(stocks_df)} 只符合条件的股票")
    
    return stocks_df


def get_stock_analysis_data(engine, ts_codes, days=30):
    """
    获取股票分析所需的数据
    """
    print("正在获取股票分析数据...")
    
    # 构建股票代码IN子句
    codes_list = []
    for code in ts_codes:
        codes_list.append(f"'{code}'")
    codes_str = ','.join(codes_list)
    
    # 获取股票基础交易数据（最近30天）
    stk_query = (
        "SELECT ts_code, trade_date, close, open, high, low, "
        "pct_change, vol, amount, close_hfq, "
        "macd, kdj_k, kdj_d, rsi_6, rsi_12 "
        "FROM stk_factor "
        "WHERE ts_code IN (" + codes_str + ") "
        "AND trade_date >= DATE_SUB(CURDATE(), INTERVAL " + str(days) + " DAY) "
        "ORDER BY ts_code, trade_date"
    )
    
    # 获取股票基本面数据
    basic_query = (
        "SELECT ts_code, trade_date, close, turnover_rate, volume_ratio, "
        "pe_ttm, pb, total_mv, circ_mv "
        "FROM daily_basic "
        "WHERE ts_code IN (" + codes_str + ") "
        "AND trade_date >= DATE_SUB(CURDATE(), INTERVAL " + str(days) + " DAY) "
        "ORDER BY ts_code, trade_date"
    )
    
    # 获取资金流向数据
    flow_query = (
        "SELECT ts_code, trade_date, "
        "buy_lg_amount + buy_elg_amount as big_buy_amount, "
        "sell_lg_amount + sell_elg_amount as big_sell_amount, "
        "net_mf_amount "
        "FROM stock_fund_flow "
        "WHERE ts_code IN (" + codes_str + ") "
        "AND trade_date >= DATE_SUB(CURDATE(), INTERVAL " + str(days) + " DAY) "
        "ORDER BY ts_code, trade_date"
    )
    
    try:
        stk_data = pd.read_sql(stk_query, engine, parse_dates=['trade_date'])
        basic_data = pd.read_sql(basic_query, engine, parse_dates=['trade_date'])
        flow_data = pd.read_sql(flow_query, engine, parse_dates=['trade_date'])
        
        print(f"获取到股票交易数据: {len(stk_data)} 条")
        print(f"获取到基本面数据: {len(basic_data)} 条")
        print(f"获取到资金流向数据: {len(flow_data)} 条")
        
        return stk_data, basic_data, flow_data
        
    except Exception as e:
        print(f"获取数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()


def calculate_technical_indicators(df):
    """
    计算技术指标
    """
    if df.empty:
        return df
        
    df = df.sort_values(['ts_code', 'trade_date'])
    
    # 计算移动平均线
    df['ma5'] = df.groupby('ts_code')['close_hfq'].transform(lambda x: x.rolling(5).mean())
    df['ma10'] = df.groupby('ts_code')['close_hfq'].transform(lambda x: x.rolling(10).mean())
    df['ma20'] = df.groupby('ts_code')['close_hfq'].transform(lambda x: x.rolling(20).mean())
    
    # 计算成交量移动平均
    df['vol_ma5'] = df.groupby('ts_code')['vol'].transform(lambda x: x.rolling(5).mean())
    df['vol_ma10'] = df.groupby('ts_code')['vol'].transform(lambda x: x.rolling(10).mean())
    
    return df


def analyze_stock_performance(stk_data, basic_data, flow_data):
    """
    分析股票表现，计算各项指标
    """
    print("正在分析股票表现...")
    
    if stk_data.empty:
        return pd.DataFrame()
    
    # 计算技术指标
    stk_data = calculate_technical_indicators(stk_data)
    
    results = []
    
    for ts_code in stk_data['ts_code'].unique():
        try:
            # 获取该股票的数据
            stock_stk = stk_data[stk_data['ts_code'] == ts_code].sort_values('trade_date')
            stock_basic = basic_data[basic_data['ts_code'] == ts_code].sort_values('trade_date')
            stock_flow = flow_data[flow_data['ts_code'] == ts_code].sort_values('trade_date')
            
            if len(stock_stk) < 10:  # 数据不足，跳过
                continue
            
            # 获取最新数据
            latest_stk = stock_stk.iloc[-1]
            latest_basic = stock_basic.iloc[-1] if not stock_basic.empty else None
            latest_flow = stock_flow.iloc[-1] if not stock_flow.empty else None
            
            # 1. 量价齐升分析
            price_trend_score = 0
            volume_trend_score = 0
            
            # 价格趋势：最近5天相对于前10天的涨幅
            recent_5_price = stock_stk.iloc[-5:]['pct_change'].mean() if len(stock_stk) >= 5 else 0
            if recent_5_price > 2:
                price_trend_score = 10
            elif recent_5_price > 0:
                price_trend_score = 5
                
            # 成交量趋势：最近5天平均成交量相对于前20天
            if not pd.isna(latest_stk['vol_ma5']) and not pd.isna(latest_stk['vol_ma10']):
                vol_ratio = latest_stk['vol_ma5'] / (latest_stk['vol_ma10'] + 1e-6)
                if vol_ratio > 1.5:
                    volume_trend_score = 10
                elif vol_ratio > 1.2:
                    volume_trend_score = 5
            
            # 2. 换手率增加分析
            turnover_score = 0
            if latest_basic is not None and not pd.isna(latest_basic['turnover_rate']):
                # 最近5天平均换手率
                recent_turnover = stock_basic.iloc[-5:]['turnover_rate'].mean() if len(stock_basic) >= 5 else 0
                prev_turnover = stock_basic.iloc[-15:-5]['turnover_rate'].mean() if len(stock_basic) >= 15 else recent_turnover
                
                if recent_turnover > prev_turnover * 1.3:
                    turnover_score = 10
                elif recent_turnover > prev_turnover * 1.1:
                    turnover_score = 5
            
            # 3. 均线齐升分析
            ma_score = 0
            if not pd.isna(latest_stk['ma5']) and not pd.isna(latest_stk['ma10']) and not pd.isna(latest_stk['ma20']):
                if latest_stk['ma5'] > latest_stk['ma10'] > latest_stk['ma20']:
                    ma_score = 10
                elif latest_stk['ma5'] > latest_stk['ma10']:
                    ma_score = 5
            
            # 4. 大资金流入分析
            fund_flow_score = 0
            if latest_flow is not None and not pd.isna(latest_flow['net_mf_amount']):
                # 最近5天大单净流入
                recent_flow = stock_flow.iloc[-5:]['net_mf_amount'].sum() if len(stock_flow) >= 5 else 0
                if recent_flow > 1000:  # 大于1000万净流入
                    fund_flow_score = 10
                elif recent_flow > 0:
                    fund_flow_score = 5
            
            # 5. 技术指标分析
            tech_score = 0
            if not pd.isna(latest_stk['rsi_6']) and not pd.isna(latest_stk['kdj_k']):
                # RSI在30-70之间，KDJ金叉
                if 30 < latest_stk['rsi_6'] < 70:
                    tech_score += 3
                if latest_stk['kdj_k'] > latest_stk['kdj_d']:
                    tech_score += 2
                    
            # MACD分析
            if not pd.isna(latest_stk['macd']) and latest_stk['macd'] > 0:
                tech_score += 5
            
            # 计算综合得分
            total_score = (price_trend_score * 0.25 + 
                         volume_trend_score * 0.2 + 
                         turnover_score * 0.15 + 
                         ma_score * 0.2 + 
                         fund_flow_score * 0.15 + 
                         tech_score * 0.05)
            
            # 获取股票名称
            stock_name = stock_stk.iloc[0]['ts_code']  # 使用ts_code作为默认名称
            
            result = {
                'ts_code': ts_code,
                'stock_name': stock_name,
                'latest_price': latest_stk['close'],
                'latest_change': latest_stk['pct_change'],
                'price_trend_score': price_trend_score,
                'volume_trend_score': volume_trend_score,
                'turnover_score': turnover_score,
                'ma_score': ma_score,
                'fund_flow_score': fund_flow_score,
                'tech_score': tech_score,
                'total_score': total_score,
                'recent_5d_return': recent_5_price,
                'turnover_rate': latest_basic['turnover_rate'] if latest_basic is not None else None,
                'net_mf_amount': latest_flow['net_mf_amount'] if latest_flow is not None else None
            }
            
            results.append(result)
            
        except Exception as e:
            print(f"分析股票 {ts_code} 时出错: {e}")
            continue
    
    if not results:
        return pd.DataFrame()
        
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('total_score', ascending=False)
    
    return results_df


def plot_analysis_results(results_df, top_n):
    """
    绘制分析结果图表
    """
    if results_df.empty:
        print("没有数据可以绘制")
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['STHeiti']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 取前top_n只股票
    top_stocks = results_df.head(top_n)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 综合得分排序
    bars1 = ax1.barh(range(len(top_stocks)), top_stocks['total_score'], color='skyblue')
    ax1.set_yticks(range(len(top_stocks)))
    ax1.set_yticklabels(top_stocks['ts_code'], fontsize=8)
    ax1.set_xlabel('综合得分')
    ax1.set_title(f'股票综合评分排名 (前{top_n}名)')
    ax1.grid(True, alpha=0.3)
    
    # 在柱状图上显示数值
    for i, bar in enumerate(bars1):
        width = bar.get_width()
        ax1.text(width + 0.1, bar.get_y() + bar.get_height()/2, 
                f'{width:.1f}', ha='left', va='center', fontsize=7)
    
    # 2. 各维度得分对比（前10只股票）
    top_10 = top_stocks.head(10)
    categories = ['price_trend_score', 'volume_trend_score', 'turnover_score', 
                 'ma_score', 'fund_flow_score', 'tech_score']
    category_names = ['价格趋势', '成交量', '换手率', '均线', '资金流', '技术指标']
    
    x = np.arange(len(top_10))
    width = 0.12
    
    for i, cat in enumerate(categories):
        ax2.bar(x + i*width, top_10[cat], width, label=category_names[i])
    
    ax2.set_xlabel('股票')
    ax2.set_ylabel('得分')
    ax2.set_title('各维度得分对比 (前10名)')
    ax2.set_xticks(x + width * 2.5)
    ax2.set_xticklabels(top_10['ts_code'], rotation=45, fontsize=8)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 涨跌幅分布
    ax3.hist(results_df['latest_change'], bins=20, color='lightgreen', alpha=0.7, edgecolor='black')
    ax3.set_xlabel('涨跌幅 (%)')
    ax3.set_ylabel('股票数量')
    ax3.set_title('股票涨跌幅分布')
    ax3.grid(True, alpha=0.3)
    
    # 4. 得分与涨跌幅的关系
    scatter = ax4.scatter(results_df['total_score'], results_df['latest_change'], 
                         c=results_df['total_score'], cmap='viridis', alpha=0.6)
    ax4.set_xlabel('综合得分')
    ax4.set_ylabel('涨跌幅 (%)')
    ax4.set_title('综合得分与涨跌幅关系')
    ax4.grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=ax4, label='综合得分')
    
    plt.tight_layout()
    #plt.show()
    #保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plt.savefig(f"results/stock_analysis_results_{timestamp}.png")


def main():
    """
    主函数
    """
    try:
        # 创建数据库连接
        engine = get_engine()
        
        print("=" * 60)
        print("开始股票分析流程")
        print("=" * 60)
        
        # 1. 获取最活跃的行业板块
        active_industries = get_active_industries(engine, top_n=10)
        
        if active_industries.empty:
            print("未能获取到活跃行业数据，程序退出")
            return
        
        print(f"\n最活跃的前10个行业板块：")
        print(active_industries[['ts_code', 'name', 'active_score']].to_string())
        
        # 2. 获取这些行业的成分股
        industry_codes = active_industries['ts_code'].tolist()
        stocks_df = get_industry_stocks(engine, industry_codes)
        
        if stocks_df.empty:
            print("未能获取到符合条件的股票，程序退出")
            return
        
        print(f"\n获取到 {len(stocks_df)} 只符合条件的股票")
        
        # 3. 获取股票分析数据
        ts_codes = stocks_df['ts_code'].tolist()
        stk_data, basic_data, flow_data = get_stock_analysis_data(engine, ts_codes, days=30)
        
        if stk_data.empty:
            print("未能获取到股票交易数据，程序退出")
            return
        
        # 4. 进行股票分析
        results_df = analyze_stock_performance(stk_data, basic_data, flow_data)
        
        if results_df.empty:
            print("分析过程中未产生有效结果")
            return
        
        # --------------------------------------------------------------
        # 行业配额过滤：每个行业(申万三级)最多保留 3 只得分最高的股票
        # --------------------------------------------------------------
        # 先将行业信息并入结果表
        results_df = results_df.merge(stocks_df[['ts_code', 'l3_code', 'l3_name']],
                                       on='ts_code', how='left')

        # 根据 total_score 重新排序，计算行业内排名
        results_df = results_df.sort_values('total_score', ascending=False)
        results_df['industry_rank'] = results_df.groupby('l3_code').cumcount() + 1

        # 只保留行业内排名 <= 3 的股票
        max_per_industry = 3
        results_df = results_df[results_df['industry_rank'] <= max_per_industry]

        if results_df.empty:
            print("行业配额过滤后没有符合条件的股票，程序退出")
            return
        
        # 5. 输出结果
        print("\n" + "=" * 80)
        print("股票分析结果 - 前30名")
        print("=" * 80)
        
        top_30 = results_df.head(30)
        display_cols = ['ts_code', 'latest_price', 'latest_change', 'total_score', 
                        'recent_5d_return', 'turnover_rate', 'net_mf_amount']
        
        print(top_30[display_cols].to_string(index=False, float_format='%.2f'))
        
        # 6. 保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.dirname(__file__)
        print(output_dir)
        output_file = f"{output_dir}/results/stock_analysis_results_{timestamp}.csv"
        os.makedirs(output_dir, exist_ok=True)
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n分析结果已保存到: {output_file}")
        
        # 7. 绘制分析图表
        plot_analysis_results(results_df, top_n=30)
        
        print("\n分析完成！")
        
    except Exception as e:
        print(f"程序执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
