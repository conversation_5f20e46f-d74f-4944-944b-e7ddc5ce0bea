import os
import sys
from datetime import datetime, timedelta
from math import floor
from typing import Dict, List

import pandas as pd
from sqlalchemy import text

# 将项目根目录加入路径，以便复用分析模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from get_data.db_config import get_engine
from big_a_compass.sw.active_industry_analysis import (
    load_data as sw_load_data,
    calculate_activity_indicators,
    score_and_select,
)
from big_a_compass.sw.sw_stock_analysis import (
    analyze_stock_performance,
)

SLIPPAGE = 0.005  # 0.5% 滑点
SELL_FEE_RATE = 0.0001  # 卖出佣金万一
MIN_COMMISSION = 5  # 最小 5 元
MAX_HOLD_DAYS = 5  # 最长持仓天数
STOP_LOSS = -0.02  # -2%
TAKE_PROFIT = 0.10  # +10%
SCORE_LOOKBACK_DAYS = 300  # 选股指标使用的历史天数

INITIAL_CAPITAL = 500_000

# --------------------------------------------------------------
# 数据获取辅助函数
# --------------------------------------------------------------

def get_trading_days(engine, num_days: int = 31) -> List[pd.Timestamp]:
    """获取最近 num_days 个交易日（升序）。"""
    sql = (
        f"SELECT DISTINCT trade_date FROM daily_basic "
        f"WHERE trade_date >= DATE_SUB(CURDATE(), INTERVAL {num_days} DAY) "
        "ORDER BY trade_date"
    )
    df = pd.read_sql(sql, engine, parse_dates=["trade_date"])
    return df["trade_date"].tolist()


def get_active_industries_at_date(engine, end_date: pd.Timestamp, top_n: int = 10) -> pd.DataFrame:
    """在指定日期计算活跃行业。"""
    start_date = (end_date - timedelta(days=50)).strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    query = (
        "SELECT * FROM sw_index_daily "
        f"WHERE trade_date BETWEEN '{start_date}' AND '{end_date_str}'"
    )
    df = pd.read_sql(query, engine, parse_dates=["trade_date"])
    if df.empty:
        return pd.DataFrame()
    df = df.sort_values(["ts_code", "trade_date"])
    df = calculate_activity_indicators(df)
    latest_df = df[df["trade_date"] == end_date]
    latest_df = latest_df[latest_df["amount"] > 10000]
    latest_df["active_score"] = (
        latest_df["pct_change_5d"].fillna(0) * 0.5
        + latest_df["vol_ratio_5_20"].fillna(0) * 30
        + latest_df["price_ratio_5_20"].fillna(0) * 20
    )
    return (
        latest_df.sort_values("active_score", ascending=False)
        .head(top_n)[["ts_code", "name", "active_score"]]
        .reset_index(drop=True)
    )


def get_industry_stocks_at_date(engine, industry_codes: List[str], base_date: pd.Timestamp) -> pd.DataFrame:
    codes_str = ",".join([f"'{c}'" for c in industry_codes])
    sql = (
        "SELECT DISTINCT sm.ts_code, sm.name, sm.l3_code, sm.l3_name, sb.list_date, sb.list_status "
        "FROM sw_member sm JOIN stock_basic sb ON sm.ts_code = sb.ts_code "
        f"WHERE sm.l3_code IN ({codes_str}) "
        "AND sm.is_new = 'Y' AND sb.list_status = 'L' "
        "AND sb.name NOT LIKE '%%ST%%' AND sb.name NOT LIKE '%%*ST%%' "
        "AND sb.name NOT LIKE '%%退市%%' "
        f"AND sb.list_date <= '{(base_date - timedelta(days=30)).strftime('%Y-%m-%d')}'"
    )
    return pd.read_sql(sql, engine, parse_dates=["list_date"])


def get_stock_analysis_data_at_date(engine, ts_codes: List[str], base_date: pd.Timestamp, days: int = SCORE_LOOKBACK_DAYS):
    if not ts_codes:
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    codes_str = ",".join([f"'{c}'" for c in ts_codes])
    start = (base_date - timedelta(days=days)).strftime("%Y-%m-%d")
    end = base_date.strftime("%Y-%m-%d")

    stk_query = (
        "SELECT ts_code, trade_date, close, open, high, low, pct_change, vol, amount, close_hfq, macd, kdj_k, kdj_d, rsi_6, rsi_12 "
        "FROM stk_factor "
        f"WHERE ts_code IN ({codes_str}) AND trade_date BETWEEN '{start}' AND '{end}'"
    )

    basic_query = (
        "SELECT ts_code, trade_date, close, turnover_rate, volume_ratio, pe_ttm, pb, total_mv, circ_mv "
        "FROM daily_basic "
        f"WHERE ts_code IN ({codes_str}) AND trade_date BETWEEN '{start}' AND '{end}'"
    )

    flow_query = (
        "SELECT ts_code, trade_date, buy_lg_amount + buy_elg_amount AS big_buy_amount, sell_lg_amount + sell_elg_amount AS big_sell_amount, net_mf_amount "
        "FROM stock_fund_flow "
        f"WHERE ts_code IN ({codes_str}) AND trade_date BETWEEN '{start}' AND '{end}'"
    )

    stk_data = pd.read_sql(stk_query, engine, parse_dates=["trade_date"])
    basic_data = pd.read_sql(basic_query, engine, parse_dates=["trade_date"])
    flow_data = pd.read_sql(flow_query, engine, parse_dates=["trade_date"])
    return stk_data, basic_data, flow_data


def select_stocks_by_score(engine, analysis_day: pd.Timestamp) -> List[str]:
    active_ind = get_active_industries_at_date(engine, analysis_day, top_n=10)
    if active_ind.empty:
        return []
    stocks_df = get_industry_stocks_at_date(engine, active_ind["ts_code"].tolist(), analysis_day)
    if stocks_df.empty:
        return []

    ts_codes = stocks_df["ts_code"].tolist()
    stk_data, basic_data, flow_data = get_stock_analysis_data_at_date(engine, ts_codes, analysis_day)
    if stk_data.empty:
        return []

    results_df = analyze_stock_performance(stk_data, basic_data, flow_data)
    if results_df.empty:
        return []

    results_df = results_df.merge(stocks_df[["ts_code", "l3_code"]], on="ts_code", how="left").sort_values("total_score", ascending=False)
    results_df["rank"] = results_df.groupby("l3_code").cumcount() + 1
    final_df = results_df[results_df["rank"] <= 3]
    return final_df["ts_code"].tolist()


def fetch_price_data(engine, ts_codes: List[str], start_date: pd.Timestamp, end_date: pd.Timestamp) -> pd.DataFrame:
    codes_str = ",".join([f"'{c}'" for c in ts_codes])
    sql = (
        "SELECT ts_code, trade_date, open, high, low, close "
        "FROM stk_factor "
        f"WHERE trade_date BETWEEN '{start_date.strftime('%Y-%m-%d')}' AND '{end_date.strftime('%Y-%m-%d')}' "
        f"AND ts_code IN ({codes_str})"
    )
    return pd.read_sql(sql, engine, parse_dates=["trade_date"])


# --------------------------------------------------------------
# 回测逻辑
# --------------------------------------------------------------

class Backtester:
    def __init__(self, engine):
        self.engine = engine
        self.cash = INITIAL_CAPITAL
        self.positions: Dict[str, Dict] = {}
        self.trade_log: List[Dict] = []
        self.daily_equity: List[Dict] = []

    def _record_trade(self, date: pd.Timestamp, ts_code: str, side: str, price: float, shares: int, amount: float, commission: float, profit_rate: float | None = None, reason: str = ""):
        self.trade_log.append({
            "date": date.strftime("%Y-%m-%d"),
            "ts_code": ts_code,
            "side": side,
            "price": price,
            "shares": shares,
            "amount": amount,
            "commission": commission,
            "profit_rate": profit_rate if profit_rate is not None else "",
            "reason": reason,
        })

    def _update_daily_equity(self, date: pd.Timestamp, price_df: pd.DataFrame):
        market_value = 0
        for ts, pos in self.positions.items():
            # 取该股票当日收盘价，如无则用买入价作为估值
            row = price_df[(price_df["ts_code"] == ts) & (price_df["trade_date"] == date)]
            if not row.empty:
                market_value += row.iloc[0]["close"] * pos["shares"]
            else:
                market_value += pos["buy_price"] * pos["shares"]
        equity = self.cash + market_value
        self.daily_equity.append({
            "date": date.strftime("%Y-%m-%d"),
            "market_value": round(market_value, 2),
            "cash": round(self.cash, 2),
            "equity": round(equity, 2),
        })

    def run(self, trading_days: List[pd.Timestamp]):
        for i in range(1, len(trading_days)):
            analysis_day = trading_days[i - 1]
            trade_day = trading_days[i]
            print(f"================ {trade_day.date()} 开始交易 =================")

            # 步骤 1：用完整打分体系选股
            picks = select_stocks_by_score(self.engine, analysis_day)
            if not picks:
                print("当日无符合条件的选股。")
                self._update_daily_equity(trade_day, pd.DataFrame())
                continue

            # 步骤 2：买入（仅未持仓且有现金的）
            buy_candidates = [ts for ts in picks if ts not in self.positions]
            if buy_candidates:
                # 获取开盘价
                price_df = fetch_price_data(self.engine, buy_candidates, trade_day, trade_day)
                if not price_df.empty:
                    allocation = self.cash / len(buy_candidates)
                    for ts in buy_candidates:
                        row = price_df[price_df["ts_code"] == ts]
                        if row.empty:
                            continue
                        open_price = row.iloc[0]["open"] * (1 + SLIPPAGE)
                        shares = floor(allocation / open_price / 100) * 100
                        if shares <= 0:
                            continue
                        cost = open_price * shares
                        if cost > self.cash:
                            continue
                        self.cash -= cost
                        self.positions[ts] = {
                            "buy_price": open_price,
                            "shares": shares,
                            "buy_date": trade_day,
                            "hold_days": 0,
                        }
                        self._record_trade(trade_day, ts, "BUY", open_price, shares, cost, 0, None, "")

            # 步骤 3：评估卖出
            if self.positions:
                price_df_all = fetch_price_data(self.engine, list(self.positions.keys()), trade_day, trade_day)
                sell_list = []
                for ts, pos in self.positions.items():
                    # T+1 限制：买入当日不能卖
                    if pos["hold_days"] == 0:
                        pos["hold_days"] += 1
                        continue

                    row = price_df_all[(price_df_all["ts_code"] == ts) & (price_df_all["trade_date"] == trade_day)]
                    if row.empty:
                        pos["hold_days"] += 1
                        continue
                    high_p = row.iloc[0]["high"]
                    low_p = row.iloc[0]["low"]
                    close_p = row.iloc[0]["close"]
                    buy_p = pos["buy_price"]
                    gain_high = (high_p - buy_p) / buy_p
                    gain_low = (low_p - buy_p) / buy_p
                    pos["hold_days"] += 1
                    trigger = False
                    reason = ""
                    if gain_high >= TAKE_PROFIT:
                        trigger = True
                        reason = "止盈"
                    elif gain_low <= STOP_LOSS:
                        trigger = True
                        reason = "止损"
                    elif pos["hold_days"] >= MAX_HOLD_DAYS:
                        trigger = True
                        reason = "到期卖出"
                    if trigger:
                        sell_price = close_p * (1 - SLIPPAGE)
                        amount = sell_price * pos["shares"]
                        commission = max(amount * SELL_FEE_RATE, MIN_COMMISSION)
                        self.cash += amount - commission
                        profit_rate = round((sell_price - buy_p) / buy_p * 100, 2)
                        self._record_trade(trade_day, ts, "SELL", sell_price, pos["shares"], amount, commission, profit_rate, reason)
                        sell_list.append(ts)
                for ts in sell_list:
                    self.positions.pop(ts, None)

            # 步骤 4：记录每日净值
            price_df_today = fetch_price_data(self.engine, list(self.positions.keys()), trade_day, trade_day) if self.positions else pd.DataFrame()
            self._update_daily_equity(trade_day, price_df_today)

        print("\n回测结束！")

    def to_dataframe(self):
        trade_df = pd.DataFrame(self.trade_log)
        equity_df = pd.DataFrame(self.daily_equity)
        return trade_df, equity_df


# --------------------------------------------------------------
# 运行入口
# --------------------------------------------------------------

def main():
    engine = get_engine()
    bt = Backtester(engine)
    trading_days = get_trading_days(engine, num_days=300)  # 取最近 31 天，保证有 30 天跨度
    if len(trading_days) < 2:
        print("交易日数量不足")
        return
    bt.run(trading_days)
    trades, equity = bt.to_dataframe()

    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    cur_dir = os.path.dirname(__file__)
    trades.to_csv(os.path.join(cur_dir, f"bt_trades_{timestamp}.csv"), index=False, float_format="%.2f", encoding="utf-8-sig")
    equity.to_csv(os.path.join(cur_dir, f"bt_equity_{timestamp}.csv"), index=False, float_format="%.2f", encoding="utf-8-sig")
    print("交易记录与每日净值已保存。")


if __name__ == "__main__":
    main() 