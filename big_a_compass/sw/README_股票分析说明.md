# 申万行业股票分析系统

## 功能概述

本系统基于申万行业分类，对最活跃的行业板块中的股票进行多维度分析，筛选出具有投资潜力的个股。

## 分析流程

### 1. 活跃行业识别
- 从申万行业指数中识别最近最活跃的前5个行业板块
- 基于以下指标计算活跃度得分：
  - 5日涨跌幅
  - 成交量比率（5日/20日）
  - 价格比率（5日/20日）

### 2. 股票筛选
从活跃行业中筛选符合条件的股票：
- 剔除ST、*ST股票
- 剔除退市整理股票
- 剔除上市不足30天的新股
- 只包含正常交易状态的股票

### 3. 多维度分析

#### 价格趋势分析 (25%权重)
- 最近5日平均涨跌幅
- 大于2%得满分，大于0%得半分

#### 成交量分析 (20%权重)
- 5日平均成交量与10日平均成交量比率
- 大于1.5倍得满分，大于1.2倍得半分

#### 换手率分析 (15%权重)
- 最近5日换手率与前10日换手率比较
- 增长30%以上得满分，增长10%以上得半分

#### 均线系统分析 (20%权重)
- MA5 > MA10 > MA20 得满分
- MA5 > MA10 得半分

#### 大资金流向分析 (15%权重)
- 最近5日大单净流入
- 净流入超过1000万得满分，净流入为正得半分

#### 技术指标分析 (5%权重)
- RSI(6)在30-70之间
- KDJ金叉（K > D）
- MACD > 0

## 输出结果

### 控制台输出
- 最活跃的前5个行业板块及其活跃度得分
- 筛选出的股票总数
- 前30名股票的综合评分和关键指标

### CSV文件
包含所有分析股票的详细数据：
- `ts_code`: 股票代码
- `latest_price`: 最新价格
- `latest_change`: 最新涨跌幅
- `total_score`: 综合得分
- `price_trend_score`: 价格趋势得分
- `volume_trend_score`: 成交量得分
- `turnover_score`: 换手率得分
- `ma_score`: 均线得分
- `fund_flow_score`: 资金流得分
- `tech_score`: 技术指标得分
- `recent_5d_return`: 近5日收益率
- `turnover_rate`: 换手率
- `net_mf_amount`: 净资金流入（万元）

### 图表展示
程序会生成4个子图的综合分析图表：
1. 股票综合评分排名（前20名）
2. 各维度得分对比（前10名）
3. 股票涨跌幅分布
4. 综合得分与涨跌幅关系散点图

## 使用方法

```bash
cd /path/to/light_sys_0.1
python big_a_compass/sw/sw_stock_analysis.py
```

## 注意事项

1. **数据库依赖**: 需要确保数据库中有以下表的最新数据：
   - `sw_index_daily`: 申万行业指数日行情
   - `sw_member`: 申万行业成分股
   - `stock_basic`: 股票基本信息
   - `stk_factor`: 股票技术指标数据
   - `daily_basic`: 股票基本面数据
   - `stock_fund_flow`: 股票资金流向数据

2. **时间窗口**: 分析基于最近30个交易日的数据

3. **评分解读**:
   - 10分：该项指标表现优秀
   - 5分：该项指标表现良好
   - 0分：该项指标表现一般或不符合条件

4. **综合得分**: 满分10分，建议关注得分7分以上的股票

## 风险提示

本分析结果仅供参考，不构成投资建议。股票投资有风险，入市需谨慎。

## 最新运行结果示例

根据最新运行结果，表现最好的前几只股票包括：
- 300127.SZ (综合得分9.85)
- 300224.SZ (综合得分9.85)  
- 300364.SZ (综合得分9.85)
- 300748.SZ (综合得分9.85)
- 300835.SZ (综合得分9.85)

这些股票在量价齐升、均线多头排列、资金流入等方面表现优异。 