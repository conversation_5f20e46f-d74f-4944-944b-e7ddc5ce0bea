import os
import sys
import requests
from mcp.server import FastMCP

# 获取当前文件所在目录的父目录（即项目根目录）
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

API_BASE = "http://0.0.0.0:8000"

mcp = FastMCP()

@mcp.tool()
def get_stk_factor(stock: str, start_date: str, end_date: str) -> dict:
    """
    获取股票后复权行情数据
    :param stock: 股票代码
    :param start_date: 开始时间
    :param end_date: 结束时间
    :return: 行情数据
    """
    resp = requests.get(f"{API_BASE}/stk_factor", params={
        "stock": stock,
        "start_date": start_date,
        "end_date": end_date
    })
    return resp.json()

@mcp.tool()
def get_income_report(stock: str) -> dict:
    """
    获取股票利润表数据
    :param stock: 股票代码
    :return: 利润表数据
    """
    resp = requests.get(f"{API_BASE}/income_report", params={"stock": stock})
    return resp.json()

@mcp.tool()
def get_balance_sheet(stock: str) -> dict:
    """
    获取股票资产负债表数据
    :param stock: 股票代码
    :return: 资产负债表数据
    """
    resp = requests.get(f"{API_BASE}/balance_sheet", params={"stock": stock})
    return resp.json()

@mcp.tool()
def get_cashflow_report(stock: str) -> dict:
    """
    获取股票现金流量表数据
    :param stock: 股票代码
    :return: 现金流量表数据
    """
    resp = requests.get(f"{API_BASE}/cashflow_report", params={"stock": stock})
    return resp.json()
@mcp.tool()
def get_core_financial_indicator(stock: str) -> dict:
    """
    获取股票核心财务指标
    :param stock: 股票代码
    :return: 核心财务指标
    """
    resp = requests.get(f"{API_BASE}/core_financial_indicator", params={"stock": stock})
    return resp.json()

@mcp.tool()
def get_all_financial_indicator(stock: str) -> dict:
    """
    获取股票全部财务指标
    :param stock: 股票代码
    :return: 全部财务指标
    """
    resp = requests.get(f"{API_BASE}/all_financial_indicator", params={"stock": stock})
    return resp.json()

@mcp.tool()
def get_stock_basic(stock: str) -> dict:
    """
    获取股票基本信息
    :param stock: 股票代码
    :return: 股票基本信息
    """
    resp = requests.get(f"{API_BASE}/stock_basic", params={"stock": stock})
    return resp.json()

@mcp.tool()
def get_daily_basic(stock: str, start_date: str, end_date: str) -> dict:
    """
    获取股票每日基础信息
    :param stock: 股票代码
    :param start_date: 开始时间
    :param end_date: 结束时间
    :return: 每日基础信息
    """
    resp = requests.get(f"{API_BASE}/daily_basic", params={
        "stock": stock,
        "start_date": start_date,
        "end_date": end_date
    })
    return resp.json() 