from fastapi import FastAPI, Query
from typing import Optional
import uvicorn
import pandas as pd
from agent.get_finanical_data import (
    get_income_report_data,
    get_balance_sheet_data,
    get_cashflow_report,
    core_financial_indicator,
    all_financial_indicator
)
from agent.get_stock_price_info import (get_stk_factor,get_stock_basic,get_daily_basic)

app: FastAPI = FastAPI()

@app.get("/stk_factor")
def stock_price(stock: str = Query(..., description="股票代码"),
                start_date: str = Query(..., description="开始时间"),
                end_date: str = Query(..., description="结束时间")):
    df = get_stk_factor(stock,start_date,end_date)
    df = df.astype(object).where(pd.notnull(df), None)
    print(df)
    return {"data": df.to_dict(orient="records")}

@app.get("/income_report")
def income_report(stock: str = Query(..., description="股票代码")):
    df = get_income_report_data(stock)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

@app.get("/balance_sheet")
def balance_sheet(stock: str = Query(..., description="股票代码")):
    df = get_balance_sheet_data(stock)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

@app.get("/cashflow_report")
def cashflow_report(stock: str = Query(..., description="股票代码")):
    df = get_cashflow_report(stock)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

@app.get("/core_financial_indicator")
def core_financial_indicator_api(stock: str = Query(..., description="股票代码")):
    df = core_financial_indicator(stock)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

@app.get("/all_financial_indicator")
def all_financial_indicator_api(stock: str = Query(..., description="股票代码")):
    df = all_financial_indicator(stock)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

@app.get("/stk_factor")
def stk_factor(stock: str = Query(..., description="股票代码"),
                start_date: str = Query(..., description="开始时间"),
                end_date: str = Query(..., description="结束时间")):
    df = get_stk_factor(stock,start_date,end_date)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

@app.get("/stock_basic")
def stock_basic(stock: str = Query(..., description="股票代码")):
    df = get_stock_basic(stock)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

@app.get("/daily_basic")
def daily_basic(stock: str = Query(..., description="股票代码"),
                start_date: str = Query(..., description="开始时间"),
                end_date: str = Query(..., description="结束时间")):
    df = get_daily_basic(stock,start_date,end_date)
    df = df.astype(object).where(pd.notnull(df), None)
    return {"data": df.to_dict(orient="records")}

if __name__ == "__main__":
    uvicorn.run("agent.get_finanical_data_fastapi:app", host="0.0.0.0", port=8000, reload=True)