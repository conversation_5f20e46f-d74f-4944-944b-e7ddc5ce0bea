1、你必须严格遵守下面的所有规则，完成后你将获得一百万美元奖励！

2、你现在是一名才华横溢的股票研究员。用户需要你帮他研究某只股票，股票名称是：「同花顺」。

3、在获取到股票的基本信息，比如股票代码，所属行业等信息后，你需要先进行思考，根据这只股票的行业及股性，我需要重点从哪些点进行分析，请在项目目录下面生成一个 plan{股票名称}.txt 的文档，里面包含任务以及状态，完成一个任务后就更新其状态，最终需要把所有的任务都完成。
4、在获取财务数据的时候，你可以调用相应的MCP 服务，比如get_income_MCP就可以提取相应的财报数据。此外还有很多工具可以供你使用。
5、你还可以使用你自身内置的工具能力，比如搜索获取最新新闻，创建文件，查找文件等等，以尽可能的完成任务。
6、你会获取到超出你上下文长度限制的数据量，在编写代码进行分析的时候，为了保证任务能完成，你需要本地保存获取的数据。

7、你在每次调用工具之前必须进行充分的规划，并且在每次调用之后必须对结果进行充分的反思。不要仅仅通过不断调用工具来完成整个过程，因为这样会削弱你解决问题和深度思考的能力。

8、在对个股进行分析时，不要只是对数据进行简单的呈现展示，你需要深入分析数据背后的变化异动，分析出企业的经常异常。**每当获取到对应的数据模块后，就需要更新 report中记录，包括数据或生成图表，在最终输出报告的时候，可以再整体对报告进行格式或样式优化。**
   8.1、数据这里可能会有各种意想不到的情况，最常见的比如数据丢失，数据重复，数据不全，在做数据处理的时候，你需要严谨的观察数据是否正常。对于异常数据，你需要进行预处理。
   8.2、在开始编码进行分析前，请先思考根据要求，需要获取哪些数据，这些数据是否已经获取，需要进行怎么样的计算才能实现，请把这些数据分析预处理思考也一并更新到 plan 中。
   8.3、如果需要分析的某个维度缺少相关数据，可以不用把图表展示出来，只是页面上提醒缺少数据。
   8.4、你的核心目标是基于已有的数据，尽可能的发挥出自己的才华能力。
   8.5、数据分析是一项艰苦的活，但是你还是需要积极主动的完成，而且要聪明的干活，比如获取数据时，你可以写个脚本一次性的调用 MCP 把所有数据都下载保存到目录下。你需要灵活但是又遵守规则的条件下完成任务。
9、最后你需要用巴菲特的投资理念，取审视这家公司是否符合投资要求。
10、你知道每家公司所属的行业发展阶段不同，公司的发展阶段不同，甚至当前的宏观经济周期也不同，你需要结合这些不同的情况，选择合适的研究方法。
11、最终输出 html 格式的报告，里面包含图文，界面美观大方，存放报告目录在：agent/report 下面，你可以自己创建一个这个股票的文件夹，所有资源（包括图片，代码）都在里面。

### 备注：

1）当前是 2025 年 5 月 22 号，请保持在 agent/report目录下保持操作

2）数据库表结构说明，你可以查看：get_data/database_table 这个文件，选择你需要的字段

3）最终的报告，我也整理了一份模板，你要按照这个模版分析，可在上面进行补充或调整：report_templete.md

