1、你必须严格遵守下面的所有规则，完成后你将获得一百万美元奖励！
2、请对【浙江华运】这只股票进行分析，请先对你需要进行的动作进行整体规划，在项目目录下面生成一个 plan{股票名称}.txt 的文档，里面包含任务以及状态，完成一个更新其状态，最终需要把所有的任务都完成。
3、在获取财务数据的时候，你可以调用相应的MCP 服务，比如get_income_MCP就包可以提取相应的财报数据。
4、你还可以使用你自身内置的工具能力，比如搜索，创建文件，查找文件等等，以尽可能的完成任务。
5、数据也不是越多越好，因为这会超出你的上下文长度，你大概率会获取到非常多的数据，在编写代码进行分析的时候，为了保证任务能完成，你可能需要保存下载获取的各种数据。
6、在对个股进行分析时，不要只是对数据进行简单的展示，你需要深入分析数据的变化异动，分析出企业的经常异常。
   6.1、数据这里可能会有各种意想不到的情况，最常见的比如数据丢失，数据重复，数据不全，在做数据处理的时候，你需要严谨的观察数据是否正常。对于异常数据，你需要进行预处理。
   6.2、在开始编码进行分析前，请先思考根据要求，需要获取哪些数据，这些数据是否已经获取，需要进行怎么样的计算才能实现，请把这些数据分析预处理思考也一并更新到 plan.txt 中。
   6.3、如果需要分析的某个维度缺少相关数据，可以不用把图表展示出来，只是页面上提醒缺少数据。
   6.4、你的核心目标是基于已有的数据，尽可能的发挥出自己的才华能力。
   6.5、数据分析是一项艰苦的活，但是你还是需要积极主动的完成，而且要聪明的干活，比如获取数据时，你可以写个脚本一次性的调用 MCP 把所有数据都下载保存到目录下。
7、你需要用巴菲特的投资理念，取审视这家公司是否符合投资要求。
	7.1、你知道每家公司所属的行业发展阶段不同，公司的发展阶段不同，甚至当前的宏光经济周期也不同，你需要结合这些不同的情况，选择合适的分析方式，一概而就是不可能有杰出的分析结果的。
8、最终输出markdown格式的报告，里面包含图文，界面美观大方，存放报告目录在：agent/report 下面，你可以自己创建一个这个股票的文件夹，所有资源（包括图片，代码）都在里面。

备注：当前是 2025 年 5 月 21 号，请保持在 agent/report目录下保持操作