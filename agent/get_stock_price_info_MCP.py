"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/21 18:43
@File: get_stock_price_info_MCP.py
@Version: 1.0
@Description: 
"""
# 添加项目根目录到Python路径
import os
import sys
import traceback
import pandas as pd

# 获取当前文件所在目录的父目录（即项目根目录）
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)


from mcp.server import FastMCP
# 初始化FastMCP服务
mcp = FastMCP()


@mcp.tool()
def get_stock_basic_mcp(ts_code: str = None) -> str | None:
    """
    获取股票的信息，包含：股票名称，代码，行业，上市时间等信息，同时对数据进行CSV格式保存
    
    Args:
        ts_code: 股票代码，例如 '300033.SZ'
    Returns:
        str: 获取股票的信息，包含：股票名称，代码，行业，上市时间等信息
    """
    try:
        if not ts_code:
            return "请提供股票代码"
        
        from agent.get_stock_price_info import get_stock_basic_dataframe
        # 直接调用返回DataFrame的函数，避免JSON转换
        df = get_stock_basic_dataframe(stock=ts_code)

        # 检查是否获取到数据
        if df.empty:
            return f'未找到股票 {ts_code} 的基本数据'

        # 直接保存DataFrame为CSV文件
        try:
            from datetime import datetime
            import os
            
            # 使用相对路径，避免暴露隐私信息
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "data_download")
            os.makedirs(project_root, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f'{ts_code}_stock_basic_{timestamp}.csv'
            full_path = os.path.join(project_root, filename)
            
            df.to_csv(full_path, index=False, encoding='utf-8-sig')
            print(f"股票基本信息已保存到CSV文件: {full_path}")
            return f"已保存 stock_basic_mcp 结果到 {full_path}"
            
        except Exception as save_error:
            print(f"保存CSV文件时发生错误: {str(save_error)}")
            return f"数据获取成功，但保存失败: {str(save_error)}"
        
    except Exception as e:
        error_message = f"获取基本数据时发生错误: {str(e)}"
        print(f"错误: {error_message}")
        print(f"详细错误信息: {traceback.format_exc()}")
        return error_message


@mcp.tool()
def get_daily_basic_mcp(ts_code: str = None,  start_date: str = None, end_date: str = None) -> str | None:
    """
    获取时间区间内股票每日的基础信息，包括每天的收盘价（除权后），PE,PB,换手率,市值等信息，同时对数据进行CSV格式保存
    
    Args:
        ts_code: 股票代码，例如 '300033.SZ'
        start_date:开始时间，日期格式必须为：YYYY-MM-DD，必填
        end_date:开始时间，日期格式必须为：YYYY-MM-DD，默认为当前日期
    Returns:
        str: 获取时间区间内股票每日的基础信息，时间格式为：日期格式必须为：YYYY-MM-DD
    """
    try:
        if not ts_code or not start_date or not end_date:
            return "请填写所有必要的参数"
        
        from agent.get_stock_price_info import get_daily_basic_dataframe
        # 直接调用返回DataFrame的函数，避免JSON转换
        df = get_daily_basic_dataframe(stock=ts_code, start_date=start_date, end_date=end_date)

        # 检查是否获取到数据
        if df.empty:
            return f'未找到股票 {ts_code} 的daily_basic数据'

        # 直接保存DataFrame为CSV文件
        try:
            from datetime import datetime
            import os
            
            # 使用相对路径，避免暴露隐私信息
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "data_download")
            os.makedirs(project_root, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f'{ts_code}_daily_basic_{start_date}_{end_date}_{timestamp}.csv'
            full_path = os.path.join(project_root, filename)
            
            df.to_csv(full_path, index=False, encoding='utf-8-sig')
            print(f"每日基础数据已保存到CSV文件: {full_path}")
            return f"已保存 daily_basic_mcp 结果到 {full_path}"
            
        except Exception as save_error:
            print(f"保存CSV文件时发生错误: {str(save_error)}")
            return f"数据获取成功，但保存失败: {str(save_error)}"
        
    except Exception as e:
        error_message = f"获取daily_basic数据时发生错误: {str(e)}"
        print(f"错误: {error_message}")
        print(f"详细错误信息: {traceback.format_exc()}")
        return error_message


@mcp.tool()
def get_stk_factor_mcp(ts_code: str = None,  start_date: str = None, end_date: str = None) -> str | None:
    """
    获取时间区间内股票每日的除权或后复权的开盘、收盘、最高、最低以及成交量，涨跌幅等数据，同时对数据进行CSV格式保存。如需回测，必须要使用后复权价格
    
    Args:
        ts_code: 股票代码，例如 '300033.SZ'
        start_date:开始时间，日期格式必须为：YYYY-MM-DD，必填
        end_date:开始时间，日期格式必须为：YYYY-MM-DD，默认为当前日期
    Returns:
        str: 获取时间区间内股票每日的除权或后复权的开盘、收盘、最高、最低以及成交量，涨跌幅等数据。如需回测，必须要使用后复权价格，时间格式为：日期格式必须为：YYYY-MM-DD
    """
    try:
        if not ts_code or not start_date or not end_date:
            return "请填写所有必要的参数"
        
        from agent.get_stock_price_info import get_stk_factor_dataframe
        # 直接调用返回DataFrame的函数，避免JSON转换
        df = get_stk_factor_dataframe(stock=ts_code, start_date=start_date, end_date=end_date)

        # 检查是否获取到数据
        if df.empty:
            return f'未找到股票 {ts_code} 的stk_factor数据'

        # 直接保存DataFrame为CSV文件
        try:
            from datetime import datetime
            import os
            
            # 使用相对路径，避免暴露隐私信息
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "data_download")
            os.makedirs(project_root, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f'{ts_code}_stk_factor_{start_date}_{end_date}_{timestamp}.csv'
            full_path = os.path.join(project_root, filename)
            
            df.to_csv(full_path, index=False, encoding='utf-8-sig')
            print(f"股票价格因子数据已保存到CSV文件: {full_path}")
            return f"已保存 stk_factor_mcp 结果到 {full_path}"
            
        except Exception as save_error:
            print(f"保存CSV文件时发生错误: {str(save_error)}")
            return f"数据获取成功，但保存失败: {str(save_error)}"
        
    except Exception as e:
        error_message = f"获取stk_factor数据时发生错误: {str(e)}"
        print(f"错误: {error_message}")
        print(f"详细错误信息: {traceback.format_exc()}")
        return error_message

if __name__ == "__main__":
    print("启动MCP服务...")
    try:
        # 使用stdio作为传输方式启动MCP服务
        mcp.run(transport="stdio")
    except Exception as e:
        print(f"MCP服务启动失败: {str(e)}")
        print(traceback.format_exc())