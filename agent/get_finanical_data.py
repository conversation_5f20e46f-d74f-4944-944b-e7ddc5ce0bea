"""
@Author: ji<PERSON><PERSON>
@Date: 2025/5/21 09:58
@File: get_finanical_data.py
@Version: 1.0
@Description: 获取某只股票的最近几个季度的财务数据
"""
from pandas import DataFrame

from get_data.db_config import get_engine
import pandas as pd

# ================================
# 新增：返回DataFrame的函数
# ================================

def get_income_report_dataframe(stock) -> DataFrame:
    """
    获取某股票最多最近8个季度的营收利润数据
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT 
                -- 识别字段
                inc.ts_code AS 'TS代码',
                inc.end_date AS '报告期',
                inc.ann_date AS '公告日期',
                
                -- 收入规模
                inc.total_revenue/10000 AS '营业总收入(万元)',
                inc.revenue/10000 AS '营业收入(万元)',
                
                -- 利润水平
                inc.operate_profit/10000 AS '营业利润(万元)',
                inc.total_profit/10000 AS '利润总额(万元)',
                inc.n_income/10000 AS '净利润(含少数股东损益)(万元)',
                inc.n_income_attr_p/10000 AS '归属母公司净利润(万元)',
                
                -- 每股指标
                inc.basic_eps AS '基本每股收益(元)',
                inc.diluted_eps AS '稀释每股收益(元)',
                
                -- 盈利质量
                inc.ebit/10000 AS '息税前利润(万元)',
                inc.ebitda/10000 AS '息税折旧摊销前利润(万元)',
                inc.invest_income/10000 AS '投资净收益(万元)',
                inc.forex_gain/10000 AS '汇兑净收益(万元)',
                
                -- 同步控制
                inc.update_flag AS '更新标志'
            FROM (
                SELECT 
                    *,
                    ROW_NUMBER() OVER (
                        PARTITION BY ts_code, end_date 
                        ORDER BY ann_date DESC, update_flag DESC
                    ) AS rn
                FROM 
                    income
                WHERE 
                    ts_code = %(stock)s
                    AND report_type = '1'  -- 合并报表
            ) inc
            WHERE 
                inc.rn = 1
            ORDER BY 
                inc.end_date DESC
            LIMIT 8;"""

    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock})
    # 数据清理
    df = df.round(4)
    df = df.astype(object).where(pd.notnull(df), None)
    
    return df

def get_balance_sheet_dataframe(stock) -> DataFrame:
    """
    获取某股票最多最近8个季度的资产负债表数据
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT 
                -- 识别字段
                bs.ts_code AS 'TS股票代码',
                bs.end_date AS '报告期',
                bs.ann_date AS '公告日期',
                
                -- 流动性资产
                bs.money_cap/********* AS '货币资金(亿元)',
                bs.accounts_receiv/********* AS '应收账款(亿元)',
                bs.inventories/********* AS '存货(亿元)',
                bs.total_cur_assets/********* AS '流动资产合计(亿元)',
                
                -- 资产结构
                bs.fix_assets/********* AS '固定资产(亿元)',
                bs.total_nca/********* AS '非流动资产合计(亿元)',
                bs.total_assets/********* AS '资产总计(亿元)',
                
                -- 负债结构
                bs.st_borr/********* AS '短期借款(亿元)',
                bs.lt_borr/********* AS '长期借款(亿元)',
                bs.total_cur_liab/********* AS '流动负债合计(亿元)',
                bs.total_ncl/********* AS '非流动负债合计(亿元)',
                bs.total_liab/********* AS '负债合计(亿元)',
                
                -- 股东权益
                bs.total_hldr_eqy_exc_min_int/********* AS '股东权益合计(亿元)',
                bs.cap_rese/********* AS '资本公积金(亿元)',
                bs.surplus_rese/********* AS '盈余公积金(亿元)',
                bs.undistr_porfit/********* AS '未分配利润(亿元)'
            FROM (
                SELECT 
                    *,
                    ROW_NUMBER() OVER (
                        PARTITION BY ts_code, end_date 
                        ORDER BY ann_date DESC, update_flag DESC
                    ) AS rn
                FROM 
                    balance_sheet
                WHERE 
                    ts_code = %(stock)s
                    AND report_type = '1'  -- 合并报表
            ) bs
            WHERE 
                bs.rn = 1
            ORDER BY 
                bs.end_date DESC
            LIMIT 8;"""

    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock})
    df = df.astype(object).where(pd.notnull(df), None)
    
    return df

def get_cashflow_dataframe(stock) -> DataFrame:
    """
    获取某股票最多最近8个季度的现金流量表数据
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT 
                -- 标识字段
                cf.ts_code AS '股票代码',
                cf.end_date AS '报告期',
                cf.ann_date AS '公告日',
                
                -- 经营活动
                cf.n_cashflow_act/10000 AS '经营活动现金流量净额(万元)',
                cf.c_inf_fr_operate_a/10000 AS '经营活动现金流入小计(万元)',
                cf.st_cash_out_act/10000 AS '经营活动现金流出小计(万元)',
                cf.c_fr_sale_sg/10000 AS '销售商品劳务收到的现金(万元)',
                cf.c_paid_to_for_empl/10000 AS '支付职工现金(万元)',
                cf.c_paid_for_taxes/10000 AS '支付税费(万元)',
                
                -- 投资活动
                cf.n_cashflow_inv_act/10000 AS '投资活动现金流量净额(万元)',
                cf.c_pay_acq_const_fiolta/10000 AS '购建长期资产支付的现金(万元)',
                cf.c_disp_withdrwl_invest/10000 AS '收回投资收到的现金(万元)',
                
                -- 筹资活动
                cf.n_cash_flows_fnc_act/10000 AS '筹资活动现金流量净额(万元)',
                cf.c_recp_borrow/10000 AS '取得借款收到的现金(万元)',
                cf.c_prepay_amt_borr/10000 AS '偿还债务支付的现金(万元)',
                cf.c_pay_dist_dpcp_int_exp/10000 AS '分配股利偿付利息支付的现金(万元)',
                cf.proc_issue_bonds/10000 AS '发行债券收到的现金(万元)',
                
                -- 自由现金流 & 余额
                cf.free_cashflow/10000 AS '企业自由现金流量(万元)',
                cf.n_incr_cash_cash_equ/10000 AS '现金及等价物净增加额(万元)',
                cf.c_cash_equ_end_period/10000 AS '期末现金及等价物余额(万元)',
                
                -- 辅助指标
                cf.net_profit/10000 AS '净利润(万元)',
                cf.finan_exp/10000 AS '财务费用(万元)',
                cf.update_flag AS '更新标志'
            FROM (
                SELECT 
                    *,
                    ROW_NUMBER() OVER (
                        PARTITION BY ts_code, end_date 
                        ORDER BY ann_date DESC, update_flag DESC
                    ) AS rn
                FROM 
                    cashflow
                WHERE 
                    ts_code = %(stock)s
                    AND report_type = '1'  -- 合并报表
            ) cf
            WHERE 
                cf.rn = 1
            ORDER BY 
                cf.end_date DESC
            LIMIT 8;"""

    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock})
    df = df.round(4)
    df = df.astype(object).where(pd.notnull(df), None)
    
    return df

def core_financial_indicator_dataframe(stock) -> DataFrame:
    """
    获取某股票最多最近8个季度核心财务指标
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT 
                -- 识别字段
                fi.ts_code AS '公司代码',
                fi.end_date AS '报告期',
                fi.ann_date AS '公告日',
                
                -- 每股指标
                fi.eps AS '基本每股收益(元)',
                fi.dt_eps AS '稀释每股收益(元)',
                fi.bps AS '每股净资产(元)',
                
                -- 盈利能力
                fi.roe AS '净资产收益率',
                fi.roa AS '总资产报酬率',
                fi.profit_dedt/10000 AS '扣非净利润(万元)',
                
                -- 偿债能力
                fi.debt_to_assets AS '资产负债率',
                fi.interestdebt/10000 AS '带息债务总额(万元)',
                
                -- 流动性
                fi.current_ratio AS '流动比率',
                fi.quick_ratio AS '速动比率',
                fi.cash_ratio AS '保守速动比率',
                
                -- 运营效率
                fi.assets_turn AS '总资产周转率(次)',
                fi.inv_turn AS '存货周转率(次)',
                fi.ar_turn AS '应收账款周转率(次)',
                
                -- 现金流指标
                fi.fcff/10000 AS '企业自由现金流(万元)',
                fi.fcfe/10000 AS '股权自由现金流(万元)',
                
                -- 成长性
                fi.basic_eps_yoy AS '基本每股收益同比(百分比）',
                fi.netprofit_yoy AS '归母净利润同比(百分比)',
                fi.or_yoy AS '营业收入同比(百分比)'
            FROM (
                SELECT 
                    *,
                    ROW_NUMBER() OVER (
                        PARTITION BY ts_code, end_date 
                        ORDER BY ann_date DESC, update_flag DESC
                    ) AS rn
                FROM 
                    financial_indicator
                WHERE 
                    ts_code = %(stock)s
                    AND update_flag = '0'  -- 通常0表示最新数据
            ) fi
            WHERE 
                fi.rn = 1
            ORDER BY 
                fi.end_date DESC
            LIMIT 8;"""
    
    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock})
    df = df.round(4)
    df = df.astype(object).where(pd.notnull(df), None)
    
    return df

def all_financial_indicator_dataframe(stock) -> DataFrame:
    """
    获取某股票最多最近4个季度的全部财务数据,如果在core_financial_indicator(stock)中没有需要的数据
    可以尝试从这里获取。
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT
                *
            FROM
                (
                    SELECT
                        *,
                        ROW_NUMBER() OVER (PARTITION BY ts_code, end_date ORDER BY ann_date DESC, update_flag DESC) AS rn
                    FROM
                        financial_indicator
                    WHERE
                        ts_code = %(stock)s
                        AND update_flag = '0' -- 通常0表示最新数据
                ) fi
            WHERE
                fi.rn = 1
            ORDER BY
                fi.end_date DESC
                LIMIT 4;"""

    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock})
    df = df.round(4)
    df = df.astype(object).where(pd.notnull(df), None)
    
    return df

# ================================
# 修改原有函数，使其调用新的DataFrame函数
# ================================

def get_income_report_data(stock) -> str:
    """
    获取某股票最多最近8个季度的营收利润数据
    :return: JSON字符串
    """
    df = get_income_report_dataframe(stock)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

def get_balance_sheet_data(stock) -> str:
    """
    获取某股票最多最近8个季度的资产负债表数据
    :return: JSON字符串
    """
    df = get_balance_sheet_dataframe(stock)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

def get_cashflow_report(stock) -> str:
    """
    获取某股票最多最近8个季度的现金流量表数据
    :return: JSON字符串
    """
    df = get_cashflow_dataframe(stock)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

def core_financial_indicator(stock) -> str:
    """
    获取某股票最多最近8个季度核心财务指标
    :return: JSON字符串
    """
    df = core_financial_indicator_dataframe(stock)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

def all_financial_indicator(stock) -> str:
    """
    获取某股票最多最近4个季度的全部财务数据,如果在core_financial_indicator(stock)中没有需要的数据
    可以尝试从这里获取。
    :return: JSON字符串
    """
    df = all_financial_indicator_dataframe(stock)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

if __name__ == '__main__':
    stock = input("请输入股票代码：")
    
    # 测试原始函数
    income_df = get_income_report_data(stock)
    print("原始收入报表数据:")
    print(income_df) 

    # 其他函数测试
    balance_sheet_df = get_balance_sheet_data(stock)
    print("\n资产负债表数据:")
    print(balance_sheet_df)
    
    # 核心财务指标测试
    core_df = core_financial_indicator(stock)
    print("\n核心财务指标:")
    print(core_df)
    


