"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/21 10:26
@File: get_income_MCP.py
@Version: 1.0
@Description: 提供获取股票财务数据的MCP服务
"""

# 添加项目根目录到Python路径
import json
import os
import sys
import traceback
import pandas as pd

# 获取当前文件所在目录的父目录（即项目根目录）
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

try:
    from mcp.server import FastMCP
except ImportError:
    print("错误：找不到 mcp.server.FastMCP 模块，请检查 mcp 包是否正确安装")
    sys.exit(1)

# 导入获取财务数据的函数
from agent.get_finanical_data import (
    get_income_report_dataframe, 
    get_balance_sheet_dataframe, 
    get_cashflow_dataframe, 
    core_financial_indicator_dataframe
)

# 初始化FastMCP服务
mcp = FastMCP()

# 获取当前日期
@mcp.tool()
def get_current_date() -> str | None:
    """
    获取当前日期，返回当前的年月日，比如 2025-06-23
    """
    try:
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d")
    except Exception as e:
        return f"获取当前日期时发生错误: {str(e)}"

# @mcp.tool()
# def get_income_mcp(ts_code: str = None) -> str | None:
#     """
#     获取某只股票最近 8 个季度的利润简报，如果没有 8 个季度，有几个季度就返回几个季度
#
#     Args:
#         ts_code: 股票代码，例如 '300033.SZ'
#
#     Returns:
#         str: 包含财务数据的
#     """
#     try:
#         if not ts_code:
#             return "请提供股票代码"
#
#         income_json = get_income_report_data(stock=ts_code)
#
#         # 如果返回的income_df为空，提供友好的提示
#         if len(income_json) == 0:
#             return '未找到股票 {ts_code} 的财务数据'
#
#         return income_json
#
#     except Exception as e:
#         # 异常处理，将错误信息返回为DataFrame
#         error_message = f"获取财务数据时发生错误: {str(e)}"
#         print(f"错误: {error_message}")


# 获取股票的最新日期的财务数据并持久化本地保存
@mcp.tool()
def get_income_mcp_with_download(ts_code: str = None) -> str | None:
    """
    获取某只股票最近 8 个季度的利润简报，如果没有 8 个季度，有几个季度就返回几个季度，同时对数据进行CSV格式保存
    
    Args:
        ts_code: 股票代码，例如 '300033.SZ'
        
    Returns:
        str: 最多包含 8 个季度的财务数据
    """
    try:
        if not ts_code:
            return "请提供股票代码"
        
        # 直接调用返回DataFrame的函数，避免JSON转换
        df = get_income_report_dataframe(stock=ts_code)
        
        # 检查是否获取到数据
        if df.empty:
            return f'未找到股票 {ts_code} 的财务数据'

        # 直接保存DataFrame为CSV文件
        try:
            from datetime import datetime
            import os
            
            # 使用相对路径，避免暴露隐私信息
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "data_download")
            os.makedirs(project_root, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f'{ts_code}_income_{timestamp}.csv'
            full_path = os.path.join(project_root, filename)
            
            df.to_csv(full_path, index=False, encoding='utf-8-sig')
            print(f"财务数据已保存到CSV文件: {full_path}")
            return f"已保存 income_mcp 结果到 {full_path}"
            
        except Exception as save_error:
            print(f"保存CSV文件时发生错误: {str(save_error)}")
            return f"数据获取成功，但保存失败: {str(save_error)}"
        
    except Exception as e:
        error_message = f"获取财务数据时发生错误: {str(e)}"
        print(f"错误: {error_message}")
        print(f"详细错误信息: {traceback.format_exc()}")
        return error_message

@mcp.tool()
def get_balance_sheet_mcp(ts_code: str = None) -> str | None:
    """
    获取某只股票最近 8 个季度的资产负债表简报，如果没有 8 个季度，有几个季度就返回几个季度，同时对数据进行CSV格式保存

    Args:
        ts_code: 股票代码，例如 '300033.SZ'

    Returns:
        str: 最多包含 8 个季度的资产负债表数据
    """
    try:
        if not ts_code:
            return "请提供股票代码"

        # 直接调用返回DataFrame的函数，避免JSON转换
        df = get_balance_sheet_dataframe(stock=ts_code)

        # 检查是否获取到数据
        if df.empty:
            return f'未找到股票 {ts_code} 的资产负债表数据'

        # 直接保存DataFrame为CSV文件
        try:
            from datetime import datetime
            import os
            
            # 使用相对路径，避免暴露隐私信息
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "data_download")
            os.makedirs(project_root, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f'{ts_code}_balance_sheet_{timestamp}.csv'
            full_path = os.path.join(project_root, filename)
            
            df.to_csv(full_path, index=False, encoding='utf-8-sig')
            print(f"资产负债表数据已保存到CSV文件: {full_path}")
            return f"已保存 balance_sheet_mcp 结果到 {full_path}"
            
        except Exception as save_error:
            print(f"保存CSV文件时发生错误: {str(save_error)}")
            return f"数据获取成功，但保存失败: {str(save_error)}"
        
    except Exception as e:
        error_message = f"获取资产负债表数据时发生错误: {str(e)}"
        print(f"错误: {error_message}")
        print(f"详细错误信息: {traceback.format_exc()}")
        return error_message


@mcp.tool()
def get_cashflow_report_mcp(ts_code: str = None) -> str | None:
    """
    获取某只股票最近 8 个季度的现金流量表简报，如果没有 8 个季度，有几个季度就返回几个季度，同时对数据进行CSV格式保存

    Args:
        ts_code: 股票代码，例如 '300033.SZ'

    Returns:
        str: 最多包含 8 个季度的现金流量表数据
    """
    try:
        if not ts_code:
            return "请提供股票代码"

        # 直接调用返回DataFrame的函数，避免JSON转换
        df = get_cashflow_dataframe(stock=ts_code)

        # 检查是否获取到数据
        if df.empty:
            return f'未找到股票 {ts_code} 的现金流量表数据'

        # 直接保存DataFrame为CSV文件
        try:
            from datetime import datetime
            import os
            
            # 使用相对路径，避免暴露隐私信息
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "data_download")
            os.makedirs(project_root, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f'{ts_code}_cashflow_{timestamp}.csv'
            full_path = os.path.join(project_root, filename)
            
            df.to_csv(full_path, index=False, encoding='utf-8-sig')
            print(f"现金流量表数据已保存到CSV文件: {full_path}")
            return f"已保存 cashflow_mcp 结果到 {full_path}"
            
        except Exception as save_error:
            print(f"保存CSV文件时发生错误: {str(save_error)}")
            return f"数据获取成功，但保存失败: {str(save_error)}"
        
    except Exception as e:
        error_message = f"获取现金流量表数据时发生错误: {str(e)}"
        print(f"错误: {error_message}")
        print(f"详细错误信息: {traceback.format_exc()}")
        return error_message

@mcp.tool()
def get_core_financial_indicator_mcp(ts_code: str = None) -> str | None:
    """
    获取某股票最多最近8个季度核心财务指标，同时对数据进行CSV格式保存

    Args:
        ts_code: 股票代码，例如 '300033.SZ'

    Returns:
        str: 包含以下核心财务指标的数据
        fi.ts_code AS '公司代码',
                end_date AS '报告期',
                ann_date AS '公告日',

                -- 每股指标
                eps AS '基本每股收益(元)',
                dt_eps AS '稀释每股收益(元)',
                bps AS '每股净资产(元)',

                -- 盈利能力
                roe AS '净资产收益率(%)',
                roa AS '总资产报酬率(%)',
                profit_dedt/10000 AS '扣非净利润(万元)',

                -- 偿债能力
                debt_to_assets AS '资产负债率(%)',
                interestdebt/10000 AS '带息债务总额(万元)',

                -- 流动性
                current_ratio AS '流动比率',
                quick_ratio AS '速动比率',
                cash_ratio AS '保守速动比率',

                -- 运营效率
                assets_turn AS '总资产周转率(次)',
                inv_turn AS '存货周转率(次)',
                ar_turn AS '应收账款周转率(次)',

                -- 现金流指标
                fcff/10000 AS '企业自由现金流(万元)',
                fcfe/10000 AS '股权自由现金流(万元)',

                -- 成长性
                basic_eps_yoy AS '基本每股收益同比(%)',
                netprofit_yoy AS '归母净利润同比(%)',
                or_yoy AS '营业收入同比(%)'
    """
    try:
        if not ts_code:
            return "请提供股票代码"

        # 直接调用返回DataFrame的函数，避免JSON转换
        df = core_financial_indicator_dataframe(stock=ts_code)

        # 检查是否获取到数据
        if df.empty:
            return f'未找到股票 {ts_code} 的核心财务指标数据'

        # 直接保存DataFrame为CSV文件
        try:
            from datetime import datetime
            import os
            
            # 使用相对路径，避免暴露隐私信息
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.join(current_dir, "data_download")
            os.makedirs(project_root, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f'{ts_code}_core_financial_indicator_{timestamp}.csv'
            full_path = os.path.join(project_root, filename)
            
            df.to_csv(full_path, index=False, encoding='utf-8-sig')
            print(f"核心财务指标数据已保存到CSV文件: {full_path}")
            return f"已保存 core_financial_indicator_mcp 结果到 {full_path}"
            
        except Exception as save_error:
            print(f"保存CSV文件时发生错误: {str(save_error)}")
            return f"数据获取成功，但保存失败: {str(save_error)}"
        
    except Exception as e:
        error_message = f"获取核心财务指标数据时发生错误: {str(e)}"
        print(f"错误: {error_message}")
        print(f"详细错误信息: {traceback.format_exc()}")
        return error_message

# @mcp.tool()
# def get_all_financial_indicator_mcp(ts_code: str = None) -> pd.DataFrame:
#     """
#     获取某股票最多最近4个季度的全部财务数据,如果在core_financial_indicator(stock)中没有需要的数据
#     可以尝试从这里获取。
#     Args:
#         ts_code: 股票代码，例如 '300033.SZ'
#
#     Returns:
#         pd.DataFrame: 包含财务数据的DataFrame
#         里面包含了最全的数据，如果需要可以从这里获取。但是优先使用get_core_financial_indicator_mcp
#     """
#     try:
#         if not ts_code:
#             return pd.DataFrame({"错误": ["请提供股票代码"]})
#
#         from agent.get_finanical_data import all_financial_indicator
#         all_financial_indicator = all_financial_indicator(stock=ts_code)
#
#         # 如果返回的DataFrame为空，提供友好的提示
#         if all_financial_indicator.empty:
#             return pd.DataFrame({"提示": [f"未找到股票 {ts_code} 的现金流量表数据"]})
#
#         return all_financial_indicator
#     except Exception as e:
#         # 异常处理，将错误信息返回为DataFrame
#         error_message = f"获取all_financial_indicator数据时发生错误: {str(e)}"
#         print(f"错误: {error_message}")
#         print(traceback.format_exc())
#         return pd.DataFrame({"错误": [error_message]})

if __name__ == "__main__":
    print("启动MCP服务...")
    try:
        # 使用stdio作为传输方式启动MCP服务
        mcp.run(transport="stdio")
    except Exception as e:
        print(f"MCP服务启动失败: {str(e)}")
        print(traceback.format_exc())