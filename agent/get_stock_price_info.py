"""
@Author: ji<PERSON><PERSON>
@Date: 2025/5/21 17:54
@File: get_stock_price_info.py
@Version: 1.0
@Description: 获取股票基础信息
"""

from pandas import DataFrame

from get_data.db_config import get_engine
import pandas as pd

# ================================
# 新增：返回DataFrame的函数
# ================================

def get_stock_basic_dataframe(stock) -> DataFrame:
    """
    获取股票的信息，包含：股票名称，代码，行业，上市等信息
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT 
                ts_code AS 'TS代码',
                symbol AS '股票代码',
                name AS '股票名称',
                fullname AS '公司全称',
                enname AS '英文名称',
                cnspell AS '拼音缩写',
                area AS '地域',
                industry AS '所属行业',
                market AS '市场类型',
                exchange AS '交易所',
                curr_type AS '货币类型',
                list_status AS '上市状态',
                list_date AS '上市日期',
                delist_date AS '退市日期',
                is_hs AS '是否沪深港通标的',
                act_name AS '实际控制人',
                act_ent_type AS '实际控制人企业类型'
            FROM 
                stock_basic
            WHERE 
                ts_code = %(stock)s"""

    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock})
    # 过滤掉数据为空的行
    df = df.astype(object).where(pd.notnull(df), None)
    return df

def get_daily_basic_dataframe(stock, start_date, end_date) -> DataFrame:
    """
    获取时间区间内股票每日的基础信息，包括每天的收盘价（除权后），PE,PB,换手率,市值等信息，时间格式为：日期格式必须为：YYYY-MM-DD
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT 
                ts_code AS '股票代码',
                trade_date AS '交易日期',
                close AS '收盘价',
                pe AS '市盈率',
                pb AS '市净率',
                turnover_rate AS '换手率',
                total_mv AS '总市值(万元)',
                circ_mv AS '流通市值(万元)'
            FROM daily_basic
            WHERE ts_code = %(stock)s  -- 替换为目标股票代码
              AND trade_date BETWEEN %(start_date)s AND %(end_date)s  -- 日期范围
            ORDER BY trade_date DESC"""

    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock, 'start_date': start_date, 'end_date': end_date})
    # 过滤掉数据为空的行
    df = df.astype(object).where(pd.notnull(df), None)
    return df

def get_stk_factor_dataframe(stock, start_date, end_date) -> DataFrame:
    """
    获取时间区间内股票每日的除权或后复权的开盘、收盘、最高、最低以及成交量，涨跌幅等数据。如需回测，必须要使用后复权价格，时间格式为：日期格式必须为：YYYY-MM-DD
    :return: pd.DataFrame
    """
    engine = get_engine()
    sql = """SELECT 
                ts_code AS '股票代码',
                trade_date AS '交易日期',
                close AS '收盘价',
                pre_close AS '昨收价',
                open AS '开盘价',
                high AS '最高价',
                low AS '最低价',
                `change` AS '涨跌额',
                pct_change AS '涨跌幅',
                vol AS '成交量(手)',
                amount AS '成交额(千元)',
                open_hfq AS '开盘价后复权',
                close_hfq AS '收盘价后复权',
                high_hfq AS '最高价后复权',
                low_hfq AS '最低价后复权',
                pre_close_hfq AS '昨收价后复权'
            FROM stk_factor
            WHERE ts_code = %(stock)s  -- 替换为目标股票代码
              AND trade_date BETWEEN %(start_date)s AND %(end_date)s  -- 日期范围
            ORDER BY trade_date DESC"""

    df: DataFrame = pd.read_sql(sql, engine, params={'stock': stock, 'start_date': start_date, 'end_date': end_date})
    # 过滤掉数据为空的行
    df = df.astype(object).where(pd.notnull(df), None)
    return df

# ================================
# 修改原有函数，使其调用新的DataFrame函数
# ================================

def get_stock_basic(stock) -> str | None:
    """
    获取股票的信息，包含：股票名称，代码，行业，上市等信息
    :return: json 格式的基本信息
    """
    df = get_stock_basic_dataframe(stock)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

def get_daily_basic(stock, start_date, end_date) -> str | None:
    """
    获取时间区间内股票每日的基础信息，包括每天的收盘价（除权后），PE,PB,换手率,市值等信息，时间格式为：日期格式必须为：YYYY-MM-DD
    :return: json 格式的区间收盘信息
    """
    df = get_daily_basic_dataframe(stock, start_date, end_date)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

def get_stk_factor(stock, start_date, end_date) -> str | None:
    """
    获取时间区间内股票每日的除权或后复权的开盘、收盘、最高、最低以及成交量，涨跌幅等数据。如需回测，必须要使用后复权价格，时间格式为：日期格式必须为：YYYY-MM-DD
    :return: json 格式的每日除权或后复权价格信息
    """
    df = get_stk_factor_dataframe(stock, start_date, end_date)
    json_str = df.to_json(orient='records', force_ascii=False)
    return json_str

if __name__ == '__main__':
    stock = input("请输入股票代码：")
    start_date  = input("请输入开始时间：")
    end_date = input("请输入结束时间：")
    print(get_stock_basic(stock))
    print(get_daily_basic(stock,start_date,end_date))
    print(get_stk_factor(stock,start_date,end_date))