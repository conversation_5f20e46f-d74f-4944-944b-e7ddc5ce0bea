"""
@Author: Jiang<PERSON>in
@Date: 2025/1/21 17:59
@Description: 计算 IC 因子的有效性
"""
import pandas as pd
import numpy as np
from scipy import stats
from sqlalchemy import create_engine
import warnings
warnings.filterwarnings('ignore')
from datetime import datetime


class DataLoader:
    def __init__(self):
        """
        初始化数据加载器
        """
        try:
            from get_data.db_config import get_engine
            self.engine = get_engine()
            print("数据库连接成功")
        except Exception as e:
            print(f"数据库连接失败: {str(e)}")
    
    def get_stock_list(self):
        """
        获取股票列表
        """
        query = "SELECT DISTINCT ts_code FROM stk_factor"
        try:
            df = pd.read_sql(query, self.engine)
            stock_list = df['ts_code'].tolist()
            print(f"成功获取股票列表，共 {len(stock_list)} 只股票")
            print(f"前5只股票代码: {stock_list[:5]}")
            return stock_list
        except Exception as e:
            print(f"获取股票列表时发生错误: {str(e)}")
            return []
    
    def get_stock_data(self, ts_code, start_date=None, end_date=None):
        """
        从MySQL获取股票数据
        """
        query = f"""
        SELECT 
            ts_code, trade_date, close_hfq as close, 
            open_hfq as open, high_hfq as high, 
            low_hfq as low, vol, amount,
            pct_change
        FROM stk_factor 
        WHERE ts_code = '{ts_code}'
        """
        
        if start_date:
            query += f" AND trade_date >= '{start_date}'"
        if end_date:
            query += f" AND trade_date <= '{end_date}'"
            
        query += " ORDER BY trade_date"
        
        try:
            print(f"\n执行SQL查询: {query}")
            df = pd.read_sql(query, self.engine)
            print(f"获取到 {len(df)} 条数据")
            if not df.empty:
                df.set_index('trade_date', inplace=True)
                print(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")
                print("数据示例:")
                print(df.head())
            return df
        except Exception as e:
            print(f"获取数据时发生错误: {str(e)}")
            return None

class FactorSpearmanTest:
    def __init__(self):
        """
        初始化因子测试类
        """
        self.data_loader = DataLoader()
        
    def calculate_ic(self, factor_data, forward_returns, method='spearman'):
        """
        计算因子值与未来收益的IC值
        """
        merged_data = pd.DataFrame({
            'factor': factor_data,
            'returns': forward_returns
        }).dropna()
        
        if len(merged_data) < 2:
            return np.nan, np.nan
            
        if method == 'spearman':
            ic, p_value = stats.spearmanr(merged_data['factor'], merged_data['returns'])
        else:
            ic, p_value = stats.pearsonr(merged_data['factor'], merged_data['returns'])
            
        return ic, p_value

    def calculate_rolling_ic(self, data, factor_name, forward_periods=5, min_periods=60):
        """
        计算滚动IC值
        """
        data['forward_returns'] = data['close'].pct_change(forward_periods).shift(-forward_periods)
        data = data.sort_index()
        results = []
        
        for date in data.index[min_periods:]:
            historical_data = data.loc[:date].tail(min_periods)
            ic, p_value = self.calculate_ic(
                historical_data[factor_name],
                historical_data['forward_returns']
            )
            
            results.append({
                'date': date,
                'IC': ic,
                'P_Value': p_value
            })
        
        ic_df = pd.DataFrame(results)
        if not ic_df.empty:
            ic_df.set_index('date', inplace=True)
        
        return ic_df

    def analyze_factor(self, stock_data, factor_name, forward_periods=5, min_periods=60):
        """
        分析因子有效性
        """
        if stock_data is None or stock_data.empty:
            print("输入的股票数据为空")
            return None
            
        print(f"开始分析因子 {factor_name}")
        print(f"数据条数: {len(stock_data)}")
        
        # 计算IC
        ic_df = self.calculate_rolling_ic(stock_data, factor_name, forward_periods, min_periods)
        
        if ic_df.empty:
            print("计算IC值结果为空")
            return None
            
        print(f"计算得到 {len(ic_df)} 个IC值")
        
        # 计算统计量
        analysis_result = {
            'start_date': ic_df.index[0],
            'end_date': ic_df.index[-1],
            'sample_size': len(ic_df),
            'ic_mean': ic_df['IC'].mean(),
            'ic_std': ic_df['IC'].std(),
            'ic_ir': ic_df['IC'].mean() / ic_df['IC'].std() if ic_df['IC'].std() != 0 else np.nan,
            'ic_positive_ratio': (ic_df['IC'] > 0).mean(),
            'significant_ratio': (ic_df['P_Value'] < 0.05).mean()
        }
        
        print("分析结果:")
        for key, value in analysis_result.items():
            print(f"{key}: {value}")
        
        return analysis_result

    def batch_analyze_stocks(self, start_date='2020-01-01', end_date='2023-12-31', 
                           factor_periods=20, forward_periods=5, min_periods=120):
        """
        批量分析所有股票的因子有效性
        
        参数:
        start_date: str, 开始日期
        end_date: str, 结束日期
        factor_periods: int, 因子计算周期（用于动量因子）
        forward_periods: int, 未来收益率计算周期
        min_periods: int, 最小样本数量
        """
        # 获取股票列表
        stock_list = self.data_loader.get_stock_list()
        print(f"获取到 {len(stock_list)} 只股票")
        results = []
        
        # 遍历每只股票
        for i, ts_code in enumerate(stock_list):
            print(f"\n处理进度: {i+1}/{len(stock_list)} - {ts_code}")
            
            # 获取股票数据
            stock_data = self.data_loader.get_stock_data(ts_code, start_date, end_date)
            
            if stock_data is not None and not stock_data.empty:
                # 计算动量因子
                stock_data['momentum'] = stock_data['close'].pct_change(factor_periods)
                
                # 分析因子有效性
                analysis_result = self.analyze_factor(
                    stock_data=stock_data,
                    factor_name='momentum',
                    forward_periods=forward_periods,
                    min_periods=min_periods
                )
                
                if analysis_result is not None:
                    analysis_result['ts_code'] = ts_code
                    results.append(analysis_result)
                    print(f"成功分析股票: {ts_code}")
            else:
                print(f"无法获取股票数据: {ts_code}")
        
        # 转换为DataFrame并保存结果
        if results:
            results_df = pd.DataFrame(results)
            
            # 对数值列保留4位小数
            numeric_columns = ['ic_mean', 'ic_std', 'ic_ir', 'ic_positive_ratio', 'significant_ratio']
            for col in numeric_columns:
                if col in results_df.columns:
                    results_df[col] = results_df[col].round(4)
            
            # 添加评级
            results_df['ic_abs_mean'] = results_df['ic_mean'].abs().round(4)
            results_df['rating'] = pd.qcut(
                results_df['ic_abs_mean'], 
                q=5, 
                labels=['很差', '较差', '一般', '较好', '很好']
            )
            
            # 保存结果
            try:
                # 创建保存目录
                import os
                save_dir = 'factor_analysis_results'
                if not os.path.exists(save_dir):
                    os.makedirs(save_dir)
                
                # 生成文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = os.path.join(save_dir, f'factor_analysis_{timestamp}.csv')
                
                # 保存文件，设置float_format来控制小数位数
                results_df.to_csv(filename, index=False, encoding='utf-8-sig', float_format='%.4f')
                print(f"\n分析结果已保存到: {filename}")
                
                # 打印汇总统计
                print("\n因子分析汇总:")
                print("-" * 50)
                print(f"分析股票数量: {len(results_df)}")
                print("\nIC值统计:")
                print(f"IC均值的均值: {results_df['ic_mean'].mean():.4f}")
                print(f"IC均值的中位数: {results_df['ic_mean'].median():.4f}")
                print(f"IR比率的均值: {results_df['ic_ir'].mean():.4f}")
                print(f"显著性比例的均值: {results_df['significant_ratio'].mean():.4f}")
                
                print("\n因子评级分布:")
                print(results_df['rating'].value_counts().sort_index())
                
            except Exception as e:
                print(f"保存结果时发生错误: {str(e)}")
                # 如果保存失败，打印部分结果
                print("\n前5条结果:")
                print(results_df.head())
            
            return results_df
        else:
            print("没有有效的分析结果")
            return None

if __name__ == "__main__":
    try:
        # 初始化测试类
        tester = FactorSpearmanTest()
        
        # 批量分析所有股票
        print("开始因子分析...")
        results_df = tester.batch_analyze_stocks(
            start_date='2020-01-01',
            end_date='2023-12-31',
            factor_periods=20,    # 20日动量因子
            forward_periods=5,    # 5日收益率
            min_periods=120       # 使用120个交易日的历史数据
        )
        print("因子分析完成!")
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {str(e)}")