"""
股票量化因子可视化分析模块

本模块实现了股票量化因子的计算、分析和可视化功能，主要用于量化投资研究中的因子有效性评估。

核心功能：
1. 从MySQL数据库中获取股票的后复权价格数据
2. 计算技术指标（如动量因子、移动平均线）
3. 计算因子的信息系数（IC）和信息比率（IR）
4. 生成包含股价走势、因子值和IC值的综合分析图表

技术指标说明：
- 动量因子：基于价格变化率的趋势指标，用于衡量股票的动量效应
- IC值（Information Coefficient）：因子与未来收益率的相关性，用于评估因子预测能力
- IR比率（Information Ratio）：IC均值除以IC标准差，衡量因子稳定性

数据来源：
- 使用stk_factor表的后复权价格数据进行计算
- 支持自定义时间范围的数据获取和分析

输出结果：
- 三层图表：股价走势图、因子值时序图、IC值时序图
- IC统计指标：IC均值、标准差、IR比率、显著性比例
- 自动保存高分辨率图片到factor_analysis_results目录

适用场景：
- 量化因子研究和回测
- 因子有效性评估
- 投资策略开发中的因子分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sqlalchemy import create_engine
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # Mac系统中文字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 数据库配置

class FactorVisualizer:
    """
    股票因子可视化分析器
    
    本类提供了完整的股票因子分析流水线，包括数据获取、因子计算、
    IC分析和可视化展示等功能。主要用于量化投资中的因子研究。
    
    核心分析流程：
    1. 数据获取：从MySQL数据库获取股票后复权价格数据
    2. 因子计算：计算动量因子等技术指标
    3. IC计算：评估因子与未来收益率的相关性
    4. 滚动分析：使用滑动窗口计算时序IC值
    5. 可视化：生成包含股价、因子值、IC值的综合图表
    6. 统计分析：计算IC均值、标准差、IR比率等关键指标
    
    主要特性：
    - 支持自定义时间范围的分析
    - 灵活的因子参数配置
    - 完整的统计指标计算
    - 高质量的图表输出和保存
    - 中文字体支持
    
    使用示例：
        visualizer = FactorVisualizer()
        visualizer.plot_factor_analysis(
            ts_code='000001.SZ',
            start_date='2023-01-01',
            end_date='2024-01-01',
            factor_periods=20,
            forward_periods=1,
            min_periods=20
        )
    """
    def __init__(self):
        """
        初始化可视化类
        
        建立数据库连接，为后续的数据获取做准备。
        使用项目配置的数据库连接参数。
        """
        from get_data.db_config import  get_engine
        self.engine = get_engine()

    def get_stock_data(self, ts_code, start_date=None, end_date=None):
        """
        从MySQL获取股票数据
        
        从stk_factor表中获取指定股票的后复权价格数据，包括OHLCV数据和涨跌幅。
        
        Args:
            ts_code (str): 股票代码，如'000001.SZ'
            start_date (str, optional): 开始日期，格式'YYYY-MM-DD'
            end_date (str, optional): 结束日期，格式'YYYY-MM-DD'
            
        Returns:
            pd.DataFrame: 包含股票交易数据的DataFrame，以trade_date为索引
                         列包括：ts_code, close, open, high, low, vol, amount, pct_change
                         如果获取失败返回None
                         
        数据说明：
            - 使用后复权价格（close_hfq等）进行分析
            - 数据按交易日期排序
            - 自动设置trade_date为索引
        """
        query = f"""
        SELECT 
            ts_code, trade_date, close_hfq as close, 
            open_hfq as open, high_hfq as high, 
            low_hfq as low, vol, amount,
            pct_change
        FROM stk_factor 
        WHERE ts_code = '{ts_code}'
        """
        
        if start_date:
            query += f" AND trade_date >= '{start_date}'"
        if end_date:
            query += f" AND trade_date <= '{end_date}'"
            
        query += " ORDER BY trade_date"
        
        try:
            df = pd.read_sql(query, self.engine)
            if not df.empty:
                df.set_index('trade_date', inplace=True)
            return df
        except Exception as e:
            print(f"获取数据时发生错误: {str(e)}")
            return None

    def calculate_momentum(self, data, periods=10):
        """
        计算动量因子
        
        基于价格变化率计算动量因子，用于衡量股票的价格动量效应。
        动量因子通常用于识别趋势延续的股票。
        
        Args:
            data (pd.DataFrame): 包含收盘价的股票数据
            periods (int): 计算动量的周期数，默认10个交易日
            
        Returns:
            pd.Series: 动量因子序列，表示periods天前到当前的收益率
            
        计算公式：
            momentum = (close_t - close_{t-periods}) / close_{t-periods}
            
        应用场景：
            - 趋势跟踪策略
            - 动量效应研究
            - 技术分析指标
        """
        return data['close'].pct_change(periods)

    def calculate_ic(self, factor_data, forward_returns):
        """
        计算IC值（信息系数）
        
        计算因子值与未来收益率之间的Spearman相关系数，用于评估因子的预测能力。
        IC值是量化投资中评估因子有效性的核心指标。
        
        Args:
            factor_data (pd.Series): 因子值序列
            forward_returns (pd.Series): 未来收益率序列
            
        Returns:
            tuple: (ic, p_value)
                - ic (float): Spearman相关系数，范围[-1, 1]
                - p_value (float): 显著性检验的p值
                
        IC值解释：
            - IC > 0.05: 因子具有较强预测能力
            - IC > 0.02: 因子具有一定预测能力
            - |IC| < 0.02: 因子预测能力较弱
            - p_value < 0.05: 相关性在统计上显著
            
        注意事项：
            - 使用Spearman相关系数，对异常值较为稳健
            - 样本量少于2时返回NaN
        """
        merged_data = pd.DataFrame({
            'factor': factor_data,
            'returns': forward_returns
        }).dropna()
        
        if len(merged_data) < 2:
            return np.nan, np.nan
            
        from scipy import stats
        ic, p_value = stats.spearmanr(merged_data['factor'], merged_data['returns'])
        return ic, p_value

    def calculate_rolling_ic(self, data, factor_name, forward_periods=5, min_periods=60):
        """
        计算滚动IC值
        
        使用滑动窗口计算因子的时序IC值，用于观察因子预测能力的时间稳定性。
        这是评估因子质量的重要方法，可以识别因子失效的时间段。
        
        Args:
            data (pd.DataFrame): 包含因子和价格数据的DataFrame
            factor_name (str): 因子列名
            forward_periods (int): 未来收益率计算周期，默认5天
            min_periods (int): 计算IC的最小样本量，默认60个交易日
            
        Returns:
            pd.DataFrame: 包含IC值和P值的时序数据
                         列包括：IC, P_Value
                         索引为日期
                         
        计算逻辑：
            1. 计算未来n日收益率：shift(-forward_periods)
            2. 对每个日期，使用过去min_periods天的数据计算IC
            3. 滑动窗口向前推进，生成IC时序
            
        应用价值：
            - 监控因子稳定性
            - 识别因子失效期
            - 动态调整因子权重
            - 因子择时策略
        """
        # 计算未来收益率
        data['forward_returns'] = data['close'].pct_change(forward_periods).shift(-forward_periods)
        
        data = data.sort_index()
        results = []
        
        # 如果数据量不足，直接返回空DataFrame
        if len(data) < min_periods:
            return pd.DataFrame()
            
        for date in data.index[min_periods:]:
            historical_data = data.loc[:date].tail(min_periods)
            ic, p_value = self.calculate_ic(
                historical_data[factor_name],
                historical_data['forward_returns']
            )
            
            if not np.isnan(ic):
                results.append({
                    'date': date,
                    'IC': ic,
                    'P_Value': p_value
                })
        
        ic_df = pd.DataFrame(results)
        if not ic_df.empty:
            ic_df.set_index('date', inplace=True)
        
        return ic_df

    def plot_factor_analysis(self, ts_code, start_date='2020-01-01', end_date='2023-12-31', 
                           factor_periods=20, forward_periods=1, min_periods=20):
        """
        绘制因子分析图
        
        生成包含股价走势、因子值和IC值的综合分析图表，并计算关键统计指标。
        这是整个分析流程的核心方法，提供完整的因子评估报告。
        
        Args:
            ts_code (str): 股票代码，如'000001.SZ'
            start_date (str): 开始日期，格式'YYYY-MM-DD'
            end_date (str): 结束日期，格式'YYYY-MM-DD'
            factor_periods (int): 动量因子计算周期，默认20天
            forward_periods (int): 未来收益率计算周期，默认1天
            min_periods (int): IC计算的最小样本量，默认20天
            
        功能特性：
            1. 三层图表展示：
               - 第一层：股价走势图（蓝色线）
               - 第二层：动量因子时序图（绿色线，包含零线）
               - 第三层：IC值时序图（紫色线，包含零线和统计信息）
               
            2. 关键统计指标：
               - IC均值：衡量因子平均预测能力
               - IC标准差：衡量因子稳定性
               - IR比率：IC均值/IC标准差，综合评估指标
               - 显著性比例：p值<0.05的比例
               
            3. 图表特性：
               - 中文字体支持
               - 网格线辅助
               - 图例说明
               - 自动布局优化
               - 高分辨率保存
               
            4. 文件管理：
               - 自动创建保存目录
               - 时间戳命名
               - PNG格式输出
               
        应用场景：
            - 单只股票的因子研究
            - 因子有效性评估
            - 策略开发中的因子验证
            - 研究报告的图表生成
            
        注意事项：
            - 需要足够的历史数据支持
            - 确保数据库连接正常
            - 图表会自动显示和保存
        """
        # 获取数据
        data = self.get_stock_data(ts_code, start_date, end_date)
        if data is None or data.empty:
            print("无法获取股票数据")
            return
        
        # 计算3日均线
        data['ma3'] = data['close'].rolling(window=3).mean()
        
        # 计算动量因子
        data['momentum'] = self.calculate_momentum(data, factor_periods)
        
        # 计算滚动IC值
        ic_df = self.calculate_rolling_ic(data, 'momentum', forward_periods, min_periods)
        
        # 创建图形
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), height_ratios=[2, 1, 1], sharex=True)
        fig.suptitle(f'股票{ts_code}的股价、动量因子与IC值分析\n{start_date}至{end_date}', fontsize=12)
        
        # 绘制股价走势
        ax1.plot(data.index, data['close'], label='股价', color='blue')
        ax1.set_ylabel('股价')
        ax1.legend(loc='upper left')
        ax1.grid(True)
        
        # 绘制动量因子
        ax2.plot(data.index, data['momentum'], label=f'{factor_periods}日动量因子', color='green')
        ax2.axhline(y=0, color='r', linestyle='--', alpha=0.3)  # 添加零线
        ax2.set_ylabel('动量因子值')
        ax2.legend(loc='upper left')
        ax2.grid(True)
        
        # 绘制IC值
        if not ic_df.empty:
            ax3.plot(ic_df.index, ic_df['IC'], label='IC值', color='purple')
            ax3.axhline(y=0, color='r', linestyle='--', alpha=0.3)  # 添加零线
            ax3.set_ylabel('IC值')
            ax3.legend(loc='lower left')
            ax3.grid(True)
            
            # 计算并显示IC统计值
            ic_mean = ic_df['IC'].mean()
            ic_std = ic_df['IC'].std()
            ic_ir = ic_mean / ic_std if ic_std != 0 else np.nan
            significant_ratio = (ic_df['P_Value'] < 0.05).mean()
            
            stats_text = f'IC均值: {ic_mean:.4f}\nIC标准差: {ic_std:.4f}\nIR比率: {ic_ir:.4f}\n显著性比例: {significant_ratio:.4f}'
            ax3.text(0.02, 0.98, stats_text, transform=ax3.transAxes, 
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        # 设置x轴格式
        ax3.xaxis.set_major_locator(plt.MaxNLocator(12))  # 控制x轴刻度数量
        plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45, ha='right')  # 统一设置x轴标签旋转
        
        # 调整布局，确保不会切掉x轴标签
        plt.tight_layout()
        
        # 保存图片
        save_dir = 'factor_analysis_results'
        import os
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = os.path.join(save_dir, f'{ts_code}_factor_analysis_{timestamp}.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"分析图表已保存到: {filename}")
        
        # 显示图表
        plt.show()

if __name__ == "__main__":
    # 初始化可视化类
    visualizer = FactorVisualizer()
    
    # 分析特定股票
    visualizer.plot_factor_analysis(
        ts_code='603786.SH',
        start_date='2023-07-05',
        end_date='2024-12-31',
        factor_periods=20,     # 20日动量因子
        forward_periods=1,     # 1日收益率
        min_periods=20        # 使用20个交易日的历史数据
    )
