"""
用于查询放量下跌的个股
    筛选条件：
    下跌幅度：pct_chg <= -9 (Tushare返回的是百分比，数据库中 pct_change 也是百分比形式，例如-9.5代表-9.5%)
    换手率：turnover_rate >= 5 (Tushare返回的是百分比，数据库中 turnover_rate 也是百分比形式)
    量比：vol_ratio >= 2

结果：
大概率会继续下跌。
"""


import pandas as pd
from sqlalchemy import text
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径，解决模块导入问题
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 假设 db_config.py 在 get_data 目录下，并且 get_engine 已经定义好
from get_data.db_config import get_engine # 导入您的数据库引擎

def get_recent_trade_dates(engine, current_date_str, n=6):
    """
    获取包括current_date_str在内的最近n个交易日
    current_date_str: 'YYYY-MM-DD' 格式
    """
    query = f"""
    SELECT DISTINCT trade_date
    FROM stk_factor
    WHERE trade_date <= '{current_date_str}'
    ORDER BY trade_date DESC
    LIMIT {n}
    """
    with engine.connect() as connection:
        df_dates = pd.read_sql(text(query), connection)
    return [d.strftime('%Y-%m-%d') for d in df_dates['trade_date']]

def fetch_daily_data(engine, trade_date_str):
    """
    从 stk_factor 获取指定日期的行情数据
    """
    query = f"""
    SELECT
        ts_code,
        open,
        high,
        low,
        close,
        pct_change AS pct_chg,  -- 重命名以匹配原脚本
        vol
    FROM stk_factor
    WHERE trade_date = '{trade_date_str}'
    """
    with engine.connect() as connection:
        df = pd.read_sql(text(query), connection)
    return df

def fetch_turnover_rate(engine, trade_date_str):
    """
    从 daily_basic 获取指定日期的换手率
    """
    query = f"""
    SELECT
        ts_code,
        turnover_rate
    FROM daily_basic
    WHERE trade_date = '{trade_date_str}'
    """
    with engine.connect() as connection:
        df = pd.read_sql(text(query), connection)
    return df

def fetch_stock_names(engine, ts_codes_list):
    """
    从 stock_basic 获取股票名称
    """
    if not ts_codes_list:
        return pd.DataFrame(columns=['ts_code', 'name'])
    codes_str = "','".join(ts_codes_list)
    query = f"""
    SELECT
        ts_code,
        name
    FROM stock_basic
    WHERE ts_code IN ('{codes_str}')
    """
    with engine.connect() as connection:
        df = pd.read_sql(text(query), connection)
    return df

def main():
    engine = get_engine()

    # 自动用今日日期 (YYYY-MM-DD 格式)
    today_dt = pd.Timestamp.today()
    today_str_sql = today_dt.strftime('%Y-%m-%d') # 用于SQL查询
    today_str_file = today_dt.strftime('%Y%m%d') # 用于文件名

    print(f"确定交易日期...")
    # 获取包括今天在内的最近6个交易日 (YYYY-MM-DD格式)
    # 如果今天不是交易日，recent_trade_dates[0] 将是最近的一个交易日
    # 我们假设脚本在交易日收盘后运行，因此 today_str_sql 应该是最新的交易数据日期
    # 或者，我们可以取 recent_trade_dates[0] 作为实际的 "当日"
    
    # 方案A: 假设脚本在某交易日T运行，目标是获取T日的数据和T日之前的5个交易日数据
    # 首先获取小于等于 today_str_sql 的最近6个交易日
    all_relevant_dates = get_recent_trade_dates(engine, today_str_sql, 6)
    
    if not all_relevant_dates or all_relevant_dates[0] != today_str_sql:
        # 如果今天不是交易日，或者今天的数据还没出来，则取数据库中最新的一个日期作为 "当日"
        # 并且重新获取基于这个最新日期的过去5个交易日
        if not all_relevant_dates:
            print(f"错误：无法从数据库获取任何交易日期。请检查 `stk_factor` 表是否有数据。")
            return
        actual_today_str_sql = all_relevant_dates[0]
        print(f"警告：指定的日期 {today_str_sql} 可能不是交易日或数据尚未更新。使用数据库中最新的交易日 {actual_today_str_sql} 作为当前分析日期。")
        # 重新获取基于 actual_today_str_sql 的6个日期
        all_relevant_dates = get_recent_trade_dates(engine, actual_today_str_sql, 6)
        if len(all_relevant_dates) < 6:
            print(f"错误：即使使用数据库最新日期 {actual_today_str_sql}，也无法获取足够的历史交易日（需要6个，实际获取{len(all_relevant_dates)}个）。")
            return
        today_str_sql = actual_today_str_sql # 更新当日日期
        # 注意：文件名中的日期 today_str_file 仍可使用最初的 today_dt.strftime('%Y%m%d')
        # 或者也更新为 actual_today_str_sql.replace('-', '')

    current_day_data_date = all_relevant_dates[0] # 这是我们用于获取当日行情和换手率的日期
    past_5_dates = all_relevant_dates[1:] # 这是过去5个交易日

    print(f"拉取 {current_day_data_date} 日行情...")
    df_today = fetch_daily_data(engine, current_day_data_date)
    if df_today.empty:
        print(f"错误：无法获取 {current_day_data_date} 的行情数据。")
        return

    print("拉取过去5日均量...")
    volume_list = []
    for i, date_i_str in enumerate(past_5_dates):
        print(f"  拉取 {date_i_str} 成交量 (过去第 {i+1} 天)...")
        df_i = fetch_daily_data(engine, date_i_str)[['ts_code', 'vol']]
        df_i.rename(columns={'vol': f'vol_past_{i+1}'}, inplace=True)
        volume_list.append(df_i)
        # time.sleep(0.5) # 本地数据库查询通常不需要特意sleep

    if not volume_list:
        print(f"错误：未能获取过去5日的任何成交量数据。")
        return
        
    df_vol = volume_list[0]
    for df_v in volume_list[1:]:
        df_vol = df_vol.merge(df_v, on='ts_code', how='outer') # 使用 outer merge 以保留所有股票代码

    # 计算 vol_5d_avg，确保至少有一个成交量数据才计算
    vol_cols = [f'vol_past_{i+1}' for i in range(len(past_5_dates))]
    df_vol['vol_5d_avg'] = df_vol[vol_cols].mean(axis=1, skipna=True)
    df_vol.dropna(subset=['vol_5d_avg'], inplace=True) # 移除没有足够历史数据计算均量的股票

    print(f"拉取 {current_day_data_date} 换手率...")
    df_basic = fetch_turnover_rate(engine, current_day_data_date)
    if df_basic.empty:
        print(f"警告：无法获取 {current_day_data_date} 的换手率数据。")
        # 可以选择在这里返回，或者继续处理（换手率相关筛选会失败）

    # 合并数据
    df = df_today.merge(df_vol[['ts_code', 'vol_5d_avg']], on='ts_code', how='inner')
    if not df_basic.empty:
        df = df.merge(df_basic, on='ts_code', how='inner')
    else: # 如果没有换手率数据，则添加一个空列以避免后续代码出错
        df['turnover_rate'] = pd.NA 

    # 获取股票名称
    all_ts_codes = df['ts_code'].unique().tolist()
    df_names = fetch_stock_names(engine, all_ts_codes)
    if not df_names.empty:
        df = df.merge(df_names, on='ts_code', how='left')
    else:
        df['name'] = "N/A"


    # 筛选信号
    # 确保列存在且类型正确
    df['pct_chg'] = pd.to_numeric(df['pct_chg'], errors='coerce')
    df['vol'] = pd.to_numeric(df['vol'], errors='coerce')
    df['vol_5d_avg'] = pd.to_numeric(df['vol_5d_avg'], errors='coerce')
    df['turnover_rate'] = pd.to_numeric(df['turnover_rate'], errors='coerce')

    # 避免除以零或NA的情况
    df.dropna(subset=['vol', 'vol_5d_avg', 'pct_chg', 'turnover_rate'], inplace=True)
    df = df[df['vol_5d_avg'] > 0] # 确保5日均量大于0

    df['vol_ratio'] = df['vol'] / df['vol_5d_avg']
    
    # 筛选条件
    # pct_chg <= -9 (Tushare返回的是百分比，数据库中 pct_change 也是百分比形式，例如-9.5代表-9.5%)
    # turnover_rate >= 5 (Tushare返回的是百分比，数据库中 turnover_rate 也是百分比形式)
    # vol_ratio >= 2
    signal_df = df[
        (df['pct_chg'] <= -6) & \
        (df['turnover_rate'] >= 3) & \
        (df['vol_ratio'] >= 1.7)
    ].copy() # 使用 .copy() 避免 SettingWithCopyWarning

    print(f"找到暴跌信号票数: {len(signal_df)}")

    if not signal_df.empty:
        # 输出字段
        output_columns = ['ts_code', 'name', 'pct_chg', 'turnover_rate', 'vol_ratio']
        # 确保所有输出列都存在
        for col in output_columns:
            if col not in signal_df.columns:
                signal_df[col] = pd.NA
        
        output_df = signal_df[output_columns]

        # 保存 CSV (使用最初脚本定义的 today_str_file)
        output_file = f"results/output{today_str_file}.csv"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)  # 自动创建目录（若不存在）
        output_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"✅ 信号已保存到 {output_file}")

        # 显示结果
        print("符合条件的股票列表：")
        print(output_df)
    else:
        print("未找到符合条件的信号。")

if __name__ == '__main__':
    main()
