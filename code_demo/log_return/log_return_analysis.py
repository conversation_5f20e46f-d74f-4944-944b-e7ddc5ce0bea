"""
对数收益率分析模块
主要功能：
1. 基础统计分析
2. 收益率分布分析
3. 收益率时序分析
4. 风险指标计算
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import seaborn as sns
from typing import Dict, List
import os
from datetime import datetime, timedelta
from log_return import calculate_log_returns

class LogReturnAnalyzer:
    def __init__(self, returns_df: pd.DataFrame):
        """
        初始化分析器
        
        参数：
            returns_df: 包含对数收益率的DataFrame
        """
        self.returns_df = returns_df
        self.stats_results = {}
        
    def calculate_basic_stats(self) -> Dict:
        """
        计算基础统计指标
        """
        stats_df = self.returns_df.groupby('ts_code')['log_return'].agg([
            ('样本数', 'count'),
            ('均值', 'mean'),
            ('标准差', 'std'),
            ('最小值', 'min'),
            ('最大值', 'max'),
            ('偏度', 'skew'),
            ('峰度', stats.kurtosis)
        ]).round(4)
        
        # 计算年化收益率和波动率
        trading_days = 252
        stats_df['年化收益率'] = (stats_df['均值'] * trading_days).round(4)
        stats_df['年化波动率'] = (stats_df['标准差'] * np.sqrt(trading_days)).round(4)
        
        self.stats_results = stats_df
        return stats_df
    
    def plot_return_distribution(self, save_dir: str = 'adx_output'):
        """
        绘制收益率分布图
        """
        plt.figure(figsize=(15, 10))
        
        # 所有股票的收益率分布
        plt.subplot(2, 1, 1)
        sns.histplot(data=self.returns_df, x='log_return', bins=100)
        plt.title('所有股票对数收益率分布')
        plt.xlabel('对数收益率')
        plt.ylabel('频数')
        
        # Q-Q图
        plt.subplot(2, 1, 2)
        stats.probplot(self.returns_df['log_return'], dist="norm", plot=plt)
        plt.title('对数收益率Q-Q图')
        
        # 保存图片
        os.makedirs(save_dir, exist_ok=True)
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'return_distribution.png'))
        plt.close()
    
    def plot_time_series(self, top_n: int = 5, save_dir: str = 'adx_output'):
        """
        绘制收益率时间序列图
        
        参数：
            top_n: 展示市值最大的前N只股票
        """
        plt.figure(figsize=(15, 10))
        
        # 选择要展示的股票
        selected_stocks = self.returns_df['ts_code'].unique()[:top_n]
        
        for stock in selected_stocks:
            stock_data = self.returns_df[self.returns_df['ts_code'] == stock]
            plt.plot(stock_data['trade_date'], stock_data['log_return'], label=stock)
        
        plt.title(f'Top {top_n} 股票对数收益率时序图')
        plt.xlabel('交易日期')
        plt.ylabel('对数收益率')
        plt.legend()
        plt.grid(True)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'return_time_series.png'))
        plt.close()
    
    def calculate_risk_metrics(self) -> pd.DataFrame:
        """
        计算风险指标
        """
        risk_metrics = []
        
        for stock in self.returns_df['ts_code'].unique():
            stock_returns = self.returns_df[self.returns_df['ts_code'] == stock]['log_return']
            
            # 计算VaR和CVaR (95%置信度)
            var_95 = np.percentile(stock_returns, 5)
            cvar_95 = stock_returns[stock_returns <= var_95].mean()
            
            # 计算最大回撤
            cum_returns = np.exp(stock_returns.cumsum()) - 1
            rolling_max = cum_returns.expanding().max()
            drawdowns = cum_returns - rolling_max
            max_drawdown = drawdowns.min()
            
            risk_metrics.append({
                'ts_code': stock,
                'VaR_95': round(var_95, 4),
                'CVaR_95': round(cvar_95, 4),
                'max_drawdown': round(max_drawdown, 4)
            })
        
        return pd.DataFrame(risk_metrics)

def main():
    # 设置中文显示
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 获取对数收益率数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=252)).strftime('%Y-%m-%d')
    
    returns_df = calculate_log_returns(
        start_date=start_date,
        end_date=end_date
    )
    
    # 创建分析器
    analyzer = LogReturnAnalyzer(returns_df)
    
    # 1. 计算基础统计指标
    stats_df = analyzer.calculate_basic_stats()
    print("\n基础统计指标：")
    print(stats_df)
    
    # 2. 绘制分布图
    analyzer.plot_return_distribution()
    print("\n已生成收益率分布图：adx_output/return_distribution.png")
    
    # 3. 绘制时间序列图
    analyzer.plot_time_series()
    print("\n已生成收益率时序图：adx_output/return_time_series.png")
    
    # 4. 计算风险指标
    risk_df = analyzer.calculate_risk_metrics()
    print("\n风险指标：")
    print(risk_df)
    
    # 保存结果到CSV
    output_dir = 'output_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    stats_df.to_csv(os.path.join(output_dir, 'stats_results.csv'))
    risk_df.to_csv(os.path.join(output_dir, 'risk_metrics.csv'), index=False)

if __name__ == "__main__":
    main()
