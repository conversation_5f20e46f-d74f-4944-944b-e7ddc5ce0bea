#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析股票收益率的偏离度
主要功能：
1. 计算每只股票当前收益率与历史均值的偏离程度
2. 计算标准化偏离度（相对于历史波动率）
3. 识别显著偏离的股票
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from log_return import calculate_log_returns
import os

def calculate_return_deviation(returns_df: pd.DataFrame, std_threshold: float = 2.0) -> pd.DataFrame:
    """
    计算每只股票当前收益率的偏离度
    
    参数:
        returns_df: 包含对数收益率的DataFrame
        std_threshold: 标准差阈值，用于识别显著偏离，默认为2.0
    
    返回:
        包含偏离度信息的DataFrame
    """
    # 获取最后交易日期
    last_date = returns_df['trade_date'].max()
    
    # 获取最后交易日的所有股票收益率
    last_returns = returns_df[returns_df['trade_date'] == last_date]
    
    results = []
    for stock in returns_df['ts_code'].unique():
        # 获取该股票的所有历史数据
        stock_data = returns_df[returns_df['ts_code'] == stock]
        
        # 获取最后交易日的收益率
        last_return = last_returns[last_returns['ts_code'] == stock]['log_return'].iloc[0]
        
        # 计算历史统计量
        hist_mean = stock_data['log_return'].mean()
        hist_std = stock_data['log_return'].std()
        
        # 计算偏离度
        deviation = last_return - hist_mean
        
        # 计算标准化偏离度（相对于历史波动率）
        std_deviation = deviation / hist_std if hist_std != 0 else 0
        
        # 计算历史分位数
        historical_percentile = (stock_data['log_return'] <= last_return).mean()
        
        results.append({
            'ts_code': stock,
            'trade_date': last_date,
            'last_return': round(last_return, 4),
            'hist_mean': round(hist_mean, 4),
            'hist_std': round(hist_std, 4),
            'deviation': round(deviation, 4),  # 实际偏离度
            'std_deviation': round(std_deviation, 4),  # 标准化偏离度
            'historical_percentile': round(historical_percentile, 4),
            'is_significant': abs(std_deviation) > std_threshold  # 是否显著偏离
        })
    
    # 转换为DataFrame并按标准化偏离度的绝对值排序
    results_df = pd.DataFrame(results)
    if not results_df.empty:
        results_df = results_df.sort_values('std_deviation', key=abs, ascending=False)
    
    return results_df

def analyze_deviations(deviation_df: pd.DataFrame) -> dict:
    """
    分析偏离度的分布情况
    """
    total_stocks = len(deviation_df)
    significant_positive = deviation_df[
        (deviation_df['std_deviation'] > 2.0)
    ].shape[0]
    significant_negative = deviation_df[
        (deviation_df['std_deviation'] < -2.0)
    ].shape[0]
    
    return {
        '总股票数': total_stocks,
        '显著正偏离数量': significant_positive,
        '显著负偏离数量': significant_negative,
        '显著正偏离比例': round(significant_positive / total_stocks * 100, 2),
        '显著负偏离比例': round(significant_negative / total_stocks * 100, 2)
    }

def main():
    # 设置日期范围（使用一年的数据）
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=252)).strftime('%Y-%m-%d')
    
    # 获取对数收益率数据
    returns_df = calculate_log_returns(
        start_date=start_date,
        end_date=end_date
    )
    
    # 计算偏离度
    deviation_df = calculate_return_deviation(returns_df)
    
    # 分析偏离度分布
    deviation_stats = analyze_deviations(deviation_df)
    
    # 创建输出目录
    output_dir = 'output_analysis_deviation'
    os.makedirs(output_dir, exist_ok=True)
    
    # 转换列名为中文
    column_mapping = {
        'ts_code': '股票代码',
        'trade_date': '交易日期',
        'last_return': '当日收益率',
        'hist_mean': '历史均值',
        'hist_std': '历史标准差',
        'deviation': '实际偏离度',
        'std_deviation': '标准化偏离度',
        'historical_percentile': '历史分位数',
        'is_significant': '显著偏离'
    }
    
    # 为显示和保存创建一个新的DataFrame副本
    display_df = deviation_df.copy()
    display_df.rename(columns=column_mapping, inplace=True)
    
    # 保存结果
    output_file = os.path.join(output_dir, 'return_deviations.csv')
    display_df.to_csv(output_file, index=False, encoding='utf-8')
    
    # 打印结果
    print(f"\n最后交易日期: {returns_df['trade_date'].max().strftime('%Y-%m-%d')}")
    print("\n偏离度统计信息：")
    for key, value in deviation_stats.items():
        print(f"{key}: {value}")
    
    print("\n偏离度最大的前10只股票：")
    pd.set_option('display.float_format', lambda x: '%.4f' % x)
    print(display_df.head(10))
    
    print("\n偏离度最小的前10只股票：")
    print(display_df.tail(10))
    
    print(f"\n完整结果已保存至: {output_file}")

if __name__ == "__main__":
    main()
