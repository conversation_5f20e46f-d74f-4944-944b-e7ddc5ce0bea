#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析处于历史对数收益率低位的股票
主要功能：
1. 获取最后交易日的对数收益率
2. 计算每只股票的历史分位数
3. 筛选出处于低位的股票
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from log_return import calculate_log_returns
import os

def find_low_return_stocks(returns_df: pd.DataFrame, percentile: float = 0.1) -> pd.DataFrame:
    """
    找出在最后交易日对数收益率处于历史低位的股票
    
    参数:
        returns_df: 包含对数收益率的DataFrame
        percentile: 分位数阈值，默认为0.1（10%分位）
    
    返回:
        处于低位的股票信息DataFrame
    """
    # 获取最后交易日期
    last_date = returns_df['trade_date'].max()
    
    # 获取最后交易日的所有股票收益率
    last_returns = returns_df[returns_df['trade_date'] == last_date]
    
    # 计算每只股票的历史分位数
    results = []
    for stock in returns_df['ts_code'].unique():
        # 获取该股票的所有历史数据
        stock_data = returns_df[returns_df['ts_code'] == stock]
        
        # 获取最后交易日的收益率
        last_return = last_returns[last_returns['ts_code'] == stock]['log_return'].iloc[0]
        
        # 计算历史分位数
        historical_percentile = (stock_data['log_return'] <= last_return).mean()
        
        # 如果处于历史低位，添加到结果中
        if historical_percentile <= percentile:
            results.append({
                'ts_code': stock,
                'last_date': last_date,
                'last_return': round(last_return, 4),
                'historical_percentile': round(historical_percentile, 4),
                'mean_return': round(stock_data['log_return'].mean(), 4),
                'std_return': round(stock_data['log_return'].std(), 4),
                'min_return': round(stock_data['log_return'].min(), 4),
                'max_return': round(stock_data['log_return'].max(), 4)
            })
    
    # 转换为DataFrame并排序
    results_df = pd.DataFrame(results)
    if not results_df.empty:
        results_df = results_df.sort_values('historical_percentile')
    
    return results_df

def main():
    # 设置日期范围（使用一年的数据）
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=252)).strftime('%Y-%m-%d')
    
    # 获取对数收益率数据
    returns_df = calculate_log_returns(
        start_date=start_date,
        end_date=end_date
    )
    
    # 查找处于历史低位的股票
    low_return_stocks = find_low_return_stocks(returns_df)
    
    # 创建输出目录
    output_dir = 'output_analysis'
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存结果
    output_file = os.path.join(output_dir, 'low_return_stocks.csv')
    low_return_stocks.to_csv(output_file, index=False, encoding='utf-8')
    
    # 打印结果
    print(f"\n最后交易日期: {returns_df['trade_date'].max().strftime('%Y-%m-%d')}")
    print(f"\n处于历史10%分位或以下的股票数量: {len(low_return_stocks)}")
    print("\n前10只股票的详细信息：")
    pd.set_option('display.float_format', lambda x: '%.4f' % x)
    print(low_return_stocks.head(10))
    print(f"\n完整结果已保存至: {output_file}")

if __name__ == "__main__":
    main()
