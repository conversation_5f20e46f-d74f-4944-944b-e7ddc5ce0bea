#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
from get_data.db_config import DB_CONFIG
from typing import List, Optional
from datetime import datetime, timedelta
import os

def get_stock_codes(engine, trade_date: str) -> List[str]:
    """
    获取指定交易日的股票代码列表
    
    参数:
        engine: 数据库连接
        trade_date: 交易日期，格式：YYYY-MM-DD
    
    返回:
        股票代码列表
    """
    query = f"""
    SELECT DISTINCT ts_code 
    FROM daily_basic 
    WHERE trade_date = '{trade_date}'
    AND total_mv > 0  -- 确保市值大于0
    limit 1000
    """
    
    df = pd.read_sql(query, engine)
    return df['ts_code'].tolist()

def calculate_log_returns(
    ts_codes: Optional[List[str]] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 1000
) -> pd.DataFrame:
    """
    计算股票的对数收益率
    使用后复权价格计算，可以更准确地反映股票的实际收益情况

    参数:
        ts_codes: 股票代码列表，如果为None则查询所有股票
        start_date: 开始日期，格式：'YYYY-MM-DD'
        end_date: 结束日期，格式：'YYYY-MM-DD'
        limit: 每只股票最多获取的数据条数

    返回:
        包含对数收益率的DataFrame
    """
    # 获取数据库配置
    db_config = DB_CONFIG
    
    # 创建数据库连接
    engine = create_engine(
        f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
        f"{db_config['host']}:{db_config['port']}/{db_config['database']}"
    )

    # 如果没有提供股票代码，从daily_basic表获取
    if not ts_codes and end_date:
        ts_codes = get_stock_codes(engine, end_date)

    # 构建SQL查询条件
    conditions = []
    if ts_codes:
        codes_str = "','".join(ts_codes)
        conditions.append(f"ts_code IN ('{codes_str}')")
    if start_date:
        conditions.append(f"trade_date >= '{start_date}'")
    if end_date:
        conditions.append(f"trade_date <= '{end_date}'")

    # 组装WHERE子句
    where_clause = " AND ".join(conditions) if conditions else "1=1"

    # SQL查询，获取后复权价格数据
    query = f"""
    SELECT ts_code, trade_date, close_hfq
    FROM stk_factor
    WHERE {where_clause}
    ORDER BY ts_code, trade_date
    """

    # 读取数据
    df = pd.read_sql(query, engine)
    
    # 将日期列转换为日期类型
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    
    # 计算对数收益率
    df = df.sort_values(['ts_code', 'trade_date'])  # 确保数据按股票和日期排序
    df['prev_close'] = df.groupby('ts_code')['close_hfq'].shift(1)  # 获取前一天的收盘价
    df['log_return'] = np.log(df['close_hfq'] / df['prev_close'])  # 计算对数收益率
    
    # 格式化对数收益率为4位小数的数字
    df['log_return'] = df['log_return'].round(4)
    
    # 删除不需要的列和NaN值
    df = df.drop('prev_close', axis=1)
    df = df.dropna()

    return df

if __name__ == "__main__":
    # 设置日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=252)).strftime('%Y-%m-%d')
    
    # 计算所有股票的对数收益率
    returns_df = calculate_log_returns(
        start_date=start_date,
        end_date=end_date
    )
    
    # 创建输出目录（如果不存在）
    output_dir = os.path.join(os.path.dirname(__file__), 'adx_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存到CSV文件，设置float_format确保数字格式
    output_file = os.path.join(output_dir, f'log_returns_{start_date}_{end_date}.csv')
    returns_df.to_csv(
        output_file,
        index=False,
        encoding='utf-8',
        float_format='%.4f'  # 确保所有浮点数都保留4位小数
    )
    
    print(f"数据已保存到: {output_file}")
    print("\n数据样例：")
    # 设置显示格式
    pd.set_option('display.float_format', lambda x: '%.4f' % x)
    print(returns_df.head())
    
    print("\n基本统计信息：")
    stats_df = returns_df.groupby('ts_code')['log_return'].agg([
        'count', 'mean', 'std', 'min', 'max'
    ]).round(4)
    print(stats_df.head())
