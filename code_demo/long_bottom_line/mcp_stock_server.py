# mcp_stock_server.py
from mcp.server.fastmcp import FastMCP
from lower_shadow_flags import get_stk_factor_data, add_lower_shadow_flags

mcp = FastMCP()

@mcp.tool()
def find_long_lower_shadow(trade_date: str,
                           ts_code: str = None,
                           thr_r1: float = 0.8,
                           thr_r2: float = 4.0,
                           min_amplitude: float = 0.03) -> list:
    """返回符合长下影线条件的股票列表"""
    df = get_stk_factor_data(ts_code=ts_code,
                             start_date=trade_date,
                             end_date=trade_date)
    df_flagged = add_lower_shadow_flags(df,
                                        thr_r1=thr_r1,
                                        thr_r2=thr_r2,
                                        min_amplitude=min_amplitude)
    result = df_flagged[df_flagged['long_lower_shadow']]
    return result[['ts_code','open','high','low','close','r1','r2','amplitude']]\
            .to_dict(orient='records')

@mcp.resource("config://shadow_params")
def default_shadow_params() -> dict:
    """提供默认的下影线检测阈值配置"""
    return {"thr_r1": 0.8, "thr_r2": 4.0, "min_amplitude": 0.03}

if __name__ == "__main__":
    mcp.run(transport='stdio')