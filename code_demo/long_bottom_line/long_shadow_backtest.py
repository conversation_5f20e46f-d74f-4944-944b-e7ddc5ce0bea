import datetime
import pandas as pd
from sqlalchemy import create_engine
from get_data.db_config import DB_URL
from code_demo.long_bottom_line.lower_shadow_flags import add_lower_shadow_flags, get_stk_factor_data

def get_future_trade_data(df: pd.DataFrame, current_date: str, days_ahead: int) -> tuple[str | None, float | None, float | None]:
    """
    在DataFrame中查找指定日期后的第days_ahead个交易日的日期、开盘价和收盘价。
    假设df已按日期排序。
    """
    try:
        current_date_dt = pd.to_datetime(current_date)
        # 找到当前日期在DataFrame中的位置
        trade_dates = df['trade_date'].tolist()
        if current_date_dt not in trade_dates:
            return None, None, None
        current_idx = trade_dates.index(current_date_dt)
        future_idx = current_idx + days_ahead
        if future_idx < len(trade_dates):
            future_date = trade_dates[future_idx].strftime('%Y%m%d')
            future_open = df.iloc[future_idx]['open']
            future_close = df.iloc[future_idx]['close']
            return future_date, future_open, future_close
        else:
            return None, None, None
    except Exception as e:
        print(f"查找未来交易日数据时发生错误: {e}")
        return None, None, None


def run_backtest(start_date: str, end_date: str,
                 thr_r1: float = 0.8, thr_r2: float = 4.0, min_amplitude: float = 0.03):
    """
    运行长下影线策略回测，计算买入后3、5、7个交易日收益率。
    """
    print(f"开始回测，日期范围：{start_date} 至 {end_date}")

    # 获取历史数据
    # 获取所有股票在指定日期范围内的数据，并按ts_code和trade_date排序
    all_stock_data = get_stk_factor_data(start_date=start_date, end_date=end_date)
    if all_stock_data.empty:
        print("指定日期范围内没有获取到数据，回测结束。")
        return

    all_stock_data['trade_date'] = pd.to_datetime(all_stock_data['trade_date'])
    all_stock_data = all_stock_data.sort_values(by=['ts_code', 'trade_date']).reset_index(drop=True)

    results = []

    # 按股票代码分组处理
    for ts_code, df_stock in all_stock_data.groupby('ts_code'):
        # 识别长下影线信号
        df_flagged = add_lower_shadow_flags(df_stock.copy(), thr_r1=thr_r1, thr_r2=thr_r2, min_amplitude=min_amplitude)
        signal_dates = df_flagged[df_flagged['long_lower_shadow']]['trade_date'].tolist()

        for signal_date_dt in signal_dates:
            signal_date_str = signal_date_dt.strftime('%Y%m%d')

            # 获取T+1日的开盘价作为买入价格
            future_date_1, buy_price, _ = get_future_trade_data(df_stock, signal_date_str, 1)

            # 如果T+1日数据不存在，则跳过该信号
            if buy_price is None:
                continue

            # 获取未来收益率计算所需的收盘价
            future_date_3, _, future_price_3 = get_future_trade_data(df_stock, signal_date_str, 3)
            future_date_5, _, future_price_5 = get_future_trade_data(df_stock, signal_date_str, 5)
            future_date_7, _, future_price_7 = get_future_trade_data(df_stock, signal_date_str, 7)

            # 计算收益率
            return_3 = (future_price_3 - buy_price) / buy_price if future_price_3 is not None and buy_price != 0 else None
            return_5 = (future_price_5 - buy_price) / buy_price if future_price_5 is not None and buy_price != 0 else None
            return_7 = (future_price_7 - buy_price) / buy_price if future_price_7 is not None and buy_price != 0 else None

            # 只记录有完整收益率数据的信号
            if (return_3 is not None) and (return_5 is not None) and (return_7 is not None):
                results.append({
                    'ts_code': ts_code,
                    'signal_date': signal_date_str,
                    'buy_date': future_date_1,         # 买入日期
                    'buy_price': buy_price,             # 买入价格（T+1开盘价）
                    'sell_date_3': future_date_3,      # 卖出日期（T+3收盘）
                    'sell_price_3': future_price_3,    # 卖出价格
                    'return_3': return_3,
                    'sell_date_5': future_date_5,
                    'sell_price_5': future_price_5,
                    'return_5': return_5,
                    'sell_date_7': future_date_7,
                    'sell_price_7': future_price_7,
                    'return_7': return_7,
                })

    results_df = pd.DataFrame(results)

    if results_df.empty:
        print("没有符合条件的回测信号，结果为空。")
        return

    # 按信号日期排序
    results_df['signal_date'] = pd.to_datetime(results_df['signal_date'])
    results_df = results_df.sort_values(by='signal_date').reset_index(drop=True)
    results_df['signal_date'] = results_df['signal_date'].dt.strftime('%Y%m%d')

    # 导出结果
    now = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    output_filename = f"long_shadow_backtest_results_{start_date}_{end_date}_{now}.csv"
    results_df.to_csv(output_filename, index=False, encoding='utf-8', float_format='%.4f')
    print(f"回测结果已导出至：{output_filename}")


if __name__ == "__main__":
    engine = create_engine(DB_URL)
    max_date = pd.read_sql("SELECT MAX(trade_date) as max_date FROM stk_factor", engine)['max_date'][0]
    print("数据库最大日期：", max_date)

    # 自动调整end_date，保证有未来7天数据
    max_date = pd.to_datetime(max_date)
    end_date = (max_date - pd.Timedelta(days=7)).strftime('%Y%m%d')
    start_date = (max_date - pd.DateOffset(months=6)).strftime('%Y%m%d')

    thr_r1 = 0.8
    thr_r2 = 4
    min_amplitude = 0.03

    run_backtest(start_date, end_date, thr_r1, thr_r2, min_amplitude)
