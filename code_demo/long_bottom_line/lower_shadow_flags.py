import datetime
import sys
import os
import pandas as pd
from sqlalchemy import create_engine

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.append(project_root)

from get_data.db_config import DB_URL, get_engine


def add_lower_shadow_flags(df: pd.DataFrame,
                           thr_r1: float = 0.8,
                           thr_r2: float = 4.0,
                           min_amplitude: float = 0.03) -> pd.DataFrame:
    """
    在 DataFrame 上增加下影线相关列，并标记长下影线，需同时满足：
    1. 下影占全幅 ≥ thr_r1
    2. 下影 ≥ 实体×thr_r2
    3. 振幅 > min_amplitude（小数，3%即0.03）
    要求 df 包含 ['open', 'high', 'low', 'close'] 四列。
    """
    df = df.copy()
    df['LS'] = df[['open', 'close']].min(axis=1) - df['low']
    df['B'] = (df['close'] - df['open']).abs()
    df['R'] = df['high'] - df['low']
    df['r1'] = df['LS'] / df['R']
    df['r2'] = df['LS'] / df['B']
    df['amplitude'] = (df['high'] - df['low']) / df['low']
    df['long_lower_shadow'] = (
        (df['r1'] >= thr_r1)
        & (df['r2'] >= thr_r2)
        & (df['amplitude'] > min_amplitude)
    )
    return df

def get_stk_factor_data(ts_code: str = None, start_date: str = None, end_date: str = None) -> pd.DataFrame:
    """
    从数据库读取stk_factor表数据，默认读取全部；可指定ts_code、起止日期。
    返回包含后复权open/high/low/close的DataFrame。
    """
    engine = get_engine()
    sql = """
    SELECT sf.trade_date, sf.ts_code, sf.open_hfq as open, sf.high_hfq as high, sf.low_hfq as low, sf.close_hfq as close
    FROM stk_factor sf
    LEFT JOIN stock_basic sb ON sf.ts_code = sb.ts_code
    WHERE sb.list_status = 'L'
      AND sb.name NOT LIKE '%%ST%%'
      AND sb.name NOT LIKE '%%*ST%%'
    """
    if ts_code:
        sql += f" AND sf.ts_code = '{ts_code}'"
    if start_date:
        sql += f" AND sf.trade_date >= '{start_date}'"
    if end_date:
        sql += f" AND sf.trade_date <= '{end_date}'"
    df = pd.read_sql(sql, engine)
    return df

def has_long_lower_shadow_on_date(trade_date: str, thr_r1: float = 0.6, thr_r2: float = 2.0,
                                 min_amplitude: float = 0.03) -> bool:
    """
    判断指定交易日是否有股票符合长下影线条件。
    返回True/False，并打印符合的股票列表。
    """
    df = get_stk_factor_data(start_date=trade_date, end_date=trade_date)
    if df.empty:
        print(f"{trade_date} 没有数据")
        return False
    df_flagged = add_lower_shadow_flags(df, thr_r1=thr_r1, thr_r2=thr_r2,
                                        min_amplitude=min_amplitude)

    result = df_flagged[df_flagged['long_lower_shadow']]

    if not result.empty:
        print(f"{trade_date} 符合长下影线的股票有：")
        now = datetime.date.today()
        result.to_csv(f"{thr_r1}+{thr_r2}+{min_amplitude}+{now}_list.csv", encoding='utf-8', float_format='%.4f')
        print(result[['ts_code', 'open', 'high', 'low', 'close', 'r1', 'r2', 'amplitude']])
        return True
    else:
        print(f"{trade_date} 没有股票符合长下影线条件")
        return False

if __name__ == "__main__":
    # 示例：判断今天是否有股票符合长下影线
    today = pd.Timestamp.now().strftime('%Y%m%d')
    #today = '20250703'
    thr_r1 = 0.8
    thr_r2 = 4
    min_amplitude = 0.03
    has_long_lower_shadow_on_date(today, thr_r1, thr_r2, min_amplitude)
