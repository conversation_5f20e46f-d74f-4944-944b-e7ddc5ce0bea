"""
回测脚本：放量下跌策略

策略逻辑：
1. 市场恐慌：全市场涨跌家数比 ≤ SENTIMENT_THRESH (例如0.20)
2. 巨量下跌：当日跌幅 ≤ PRICE_DROP_PCT (例如-5%)
3. 巨量放大：当日成交量 ≥ N_AVG日均量 × VOL_RATIO_MIN (例如2倍)
4. 基本面尚可：非ST股

回测逻辑：
- 回测周期：最近一年
- 入场：T日收盘产生信号，T+1日开盘价买入
- 出场：分别统计T+3, T+5, T+7, T+10日收盘价卖出的收益情况
- 输出：CSV文件，包含每笔交易详情和汇总统计
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import text
import os

# 导入数据库连接引擎
# 确保 get_data/db_config.py 文件存在且配置正确
try:
    from get_data.db_config import get_engine
except ImportError:
    print("错误：无法导入数据库配置。请确保 get_data/db_config.py 文件存在且路径正确。")
    exit()

# === 回测参数 ===
START_DATE_OFFSET_YEARS = 1 # 回测时长，例如1年
N_AVG = 20  # 计算均量的周期
PRICE_DROP_PCT = -5.0  # 价格下跌百分比阈值
VOL_RATIO_MIN = 2.0  # 成交量放大倍数阈值
SENTIMENT_THRESH = 0.20  # 市场情绪阈值 (上涨家数 / (上涨+下跌家数))
# 注意：原策略中的止损在此回测中不直接作为退出条件，而是按固定持有期退出

# 持有周期列表 (交易日)
HOLDING_PERIODS = [3, 5, 7, 10]

OUTPUT_CSV_FILE = "backtest_results_fear_drop.csv"

# === 辅助函数：获取交易日历 ===
def get_trading_calendar(engine, start_date_str, end_date_str):
    """
    从数据库获取指定日期范围内的交易日历
    返回一个排序好的交易日列表 (YYYYMMDD格式字符串)
    """
    query = text("""
        SELECT cal_date FROM trading_calendar
        WHERE exchange = 'SSE' AND is_open = 1 
        AND cal_date BETWEEN :start_date AND :end_date
        ORDER BY cal_date ASC
    """)
    # 假设上海证券交易所的日历代表了主要交易日
    # 日期格式在数据库中是 YYYYMMDD
    df = pd.read_sql(query, engine, params={'start_date': start_date_str, 'end_date': end_date_str})
    return df['cal_date'].tolist()

# === 辅助函数：获取股票日线数据 ===
def get_stock_daily_data(engine, ts_code, start_date_str, end_date_str):
    """
    获取单只股票在指定日期范围内的日线数据 (stk_factor表)
    返回DataFrame，包含 trade_date (YYYYMMDD), open, close, vol, pct_chg
    """
    query = text("""
        SELECT trade_date, open, close, vol, pct_change AS pct_chg
        FROM stk_factor
        WHERE ts_code = :ts_code AND trade_date BETWEEN :start_date AND :end_date
        ORDER BY trade_date ASC
    """)
    df = pd.read_sql(query, engine, params={
        'ts_code': ts_code,
        'start_date': start_date_str, # YYYYMMDD
        'end_date': end_date_str    # YYYYMMDD
    })
    if not df.empty:
        df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y%m%d')
    return df
    
# === 辅助函数：获取多只股票指定交易日的数据 ===
def get_stocks_data_on_date(engine, ts_codes_list, trade_date_str):
    """
    获取多只股票在指定单个交易日的开盘价、收盘价等数据
    """
    if not ts_codes_list:
        return pd.DataFrame()
    
    ts_codes_tuple = tuple(ts_codes_list)
    query = text(f"""
        SELECT ts_code, trade_date, open, close, pct_change AS pct_chg, vol
        FROM stk_factor
        WHERE ts_code IN :ts_codes AND trade_date = :trade_date
    """)
    df = pd.read_sql(query, engine, params={'ts_codes': ts_codes_tuple, 'trade_date': trade_date_str})
    if not df.empty:
        df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y%m%d')
    return df
    
# === 辅助函数：获取股票基本信息 (判断是否ST) ===
def get_stock_basic_info(engine, ts_codes_list):
    """
    获取股票基本信息，主要用于判断是否ST
    返回一个字典 {ts_code: name}
    """
    if not ts_codes_list:
        return {}
    ts_codes_tuple = tuple(ts_codes_list)
    query = text(f"""
        SELECT ts_code, name FROM stock_basic
        WHERE ts_code IN :ts_codes
    """)
    df = pd.read_sql(query, engine, params={'ts_codes': ts_codes_tuple})
    return pd.Series(df.name.values, index=df.ts_code).to_dict()

# === 策略核心逻辑函数 (与原脚本类似，但适配回测) ===
def check_market_sentiment(engine, trade_date_str):
    """计算指定交易日的市场情绪"""
    query = text("""
        SELECT pct_change AS pct_chg FROM stk_factor
        WHERE trade_date = :trade_date
    """)
    # trade_date_str 应为 YYYYMMDD
    mkt_df = pd.read_sql(query, engine, params={'trade_date': trade_date_str})
    if mkt_df.empty:
        return 1.0 # 如果当天没有数据，默认市场不恐慌
    
    adv = (mkt_df["pct_chg"] > 0).sum()
    dec = (mkt_df["pct_chg"] < 0).sum()
    sentiment = adv / (adv + dec) if (adv + dec) > 0 else 1.0
    # print(f"Debug: {trade_date_str} Adv: {adv}, Dec: {dec}, Sentiment: {sentiment}")
    return sentiment

def check_giant_volume_drop(daily_df_stock):
    """检查单只股票是否满足巨量下跌条件 (传入的是该股票的多日行情)"""
    if len(daily_df_stock) < N_AVG + 1:
        return False
    
    # 确保按日期排序
    daily_df_stock = daily_df_stock.sort_values('trade_date')
    
    today = daily_df_stock.iloc[-1] # 当期数据
    # 计算N_AVG周期的均量，不包括当天
    vol_mean = daily_df_stock["vol"].iloc[-(N_AVG + 1):-1].mean()
    
    if vol_mean == 0 or pd.isna(vol_mean): # 避免除以0或NaN
        return False
        
    vol_ratio = today["vol"] / vol_mean
    
    # pct_chg 已经是百分比，比如-5表示-5%
    price_dropped = today["pct_chg"] <= PRICE_DROP_PCT 
    volume_surged = vol_ratio >= VOL_RATIO_MIN
    
    return price_dropped and volume_surged

def get_all_listed_stocks(engine, trade_date_str):
    """获取指定日期仍在上市的股票列表"""
    query = text("""
        SELECT ts_code FROM stock_basic 
        WHERE list_status = 'L' 
        AND list_date <= :trade_date 
        AND (delist_date IS NULL OR delist_date > :trade_date)
    """)
    # trade_date_str 应为 YYYYMMDD
    df = pd.read_sql(query, engine, params={'trade_date': trade_date_str})
    return df['ts_code'].tolist()

# === 主回测逻辑 ===
def run_backtest():
    print("开始执行回测...")
    engine = get_engine

    # 1. 确定回测日期范围
    end_date_dt = datetime.now()
    start_date_dt = end_date_dt - timedelta(days=365 * START_DATE_OFFSET_YEARS)
    
    # 转换为YYYYMMDD字符串格式给数据库查询
    end_date_str_db = end_date_dt.strftime('%Y%m%d')
    # 为了获取足够的历史数据计算均线等，实际查询数据的开始日期要更早
    # 例如，均线需要N_AVG天，再往前多取一些buffer
    query_data_start_dt = start_date_dt - timedelta(days=N_AVG + 30) # 多30天buffer
    query_data_start_str_db = query_data_start_dt.strftime('%Y%m%d')
    
    print(f"回测用行情数据范围: {query_data_start_str_db} 到 {end_date_str_db}")

    # 2. 获取此范围内的所有交易日 (用于迭代)
    # 回测的迭代应该从我们定义的 start_date_dt 开始，而不是 query_data_start_dt
    backtest_iter_start_date_str = start_date_dt.strftime('%Y%m%d')
    trading_days = get_trading_calendar(engine, backtest_iter_start_date_str, end_date_str_db)
    if not trading_days:
        print("错误：未能获取到交易日历，请检查数据库 `trading_calendar` 表。")
        return
    print(f"共获取到 {len(trading_days)} 个交易日进行回测迭代。")

    all_trades_results = [] # 存储每笔模拟交易的结果

    # 3. 迭代每一个交易日 (T日)
    for i, t_date_str in enumerate(trading_days):
        if i < N_AVG : # 需要至少N_AVG天的历史数据来计算均量等指标，所以跳过前面几天
            # print(f"跳过早期日期 {t_date_str}，数据不足。")
            continue

        print(f"正在处理 T日 = {t_date_str} ({i+1}/{len(trading_days)})")

        # 3.1 检查市场情绪
        current_market_sentiment = check_market_sentiment(engine, t_date_str)
        if current_market_sentiment > SENTIMENT_THRESH:
            print(f"  市场情绪 {current_market_sentiment:.2f} > {SENTIMENT_THRESH}，跳过当日。")
            continue
        print(f"  市场情绪 {current_market_sentiment:.2f} ≤ {SENTIMENT_THRESH}，符合条件。")

        # 3.2 获取T日所有上市股票
        # 我们需要T日及之前的历史数据来判断信号，所以获取股票列表时用T日
        candidate_stocks = get_all_listed_stocks(engine, t_date_str)
        if not candidate_stocks:
            print(f"  T日 {t_date_str} 未获取到上市股票列表。")
            continue
        
        # 获取这些股票的基本信息（判断ST）
        stock_names_map = get_stock_basic_info(engine, candidate_stocks)

        # 3.3 筛选符合策略的股票
        buy_signals = [] # (ts_code, t_date_str)

        # 为了判断个股信号，需要T日及之前的行情数据
        # 计算每只股票判断信号所需的最早日期
        # 例如，T日是20230105，需要N_AVG=20天数据，则需要从大约20221205开始的数据
        # 我们在前面获取交易日历时，迭代的 trading_days 是从 start_date_dt 开始的
        # 对于每只股票，我们需要从 trading_days[0] （或者更早的 query_data_start_str_db）到 t_date_str 的数据
        
        # 获取判断信号所需历史数据的起始日期 (对于每只股票)
        # 这个日期应该是固定的，即 query_data_start_str_db，或者至少是 t_date_str 前推 N_AVG + buffer 天
        # 为了简化，并且之前已经获取了 query_data_start_str_db 到 end_date_str_db 的数据
        # 我们可以假设在后续获取个股数据时，会取到足够长的历史。
        # 或者更精确地：对于t_date_str, 需要它之前的N_AVG天的交易日数据
        
        idx_t_date = trading_days.index(t_date_str)
        if idx_t_date < N_AVG: # 确保有N_AVG个历史交易日
            # print(f"  T日 {t_date_str} 前历史交易日不足 {N_AVG} 天，无法计算指标。")
            continue
            
        # 个股历史数据查询的开始日期应该是 T日 前推 N_AVG 个交易日
        # 但为了获取数据方便，直接使用一个较早的固定开始日期 query_data_start_str_db
        # 然后在 check_giant_volume_drop 中再做长度校验
        
        print(f"  开始筛选 {len(candidate_stocks)} 只候选股票...")
        processed_count = 0
        for ts_code in candidate_stocks:
            processed_count += 1
            if processed_count % 200 == 0:
                 print(f"    已处理 {processed_count}/{len(candidate_stocks)}...")

            # 排除ST股
            stock_name = stock_names_map.get(ts_code, "")
            if "ST" in stock_name or "st" in stock_name: # 简单判断
                # print(f"    {ts_code} ({stock_name}) 是ST股，跳过。")
                continue

            # 获取该股票到T日为止的历史日线数据
            # 需要从一个足够早的日期开始，以包含N_AVG天的历史
            # 实际上，应该获取 t_date_str 往前 N_AVG+1 天的数据
            # 为了简化，我们先假设 get_stock_daily_data 会配合全局的 query_data_start_str_db
            # 但更精确的做法是动态确定每只股票的查询开始日期
            
            # 找到T日之前 N_AVG 个交易日的那一天作为历史数据获取的起点
            # 这是为了确保 check_giant_volume_drop 有足够的数据
            history_start_index = max(0, idx_t_date - (N_AVG + 5)) # 加一点buffer
            stock_hist_query_start_date = trading_days[history_start_index]

            daily_df_stock = get_stock_daily_data(engine, ts_code, stock_hist_query_start_date, t_date_str)
            
            if daily_df_stock.empty or len(daily_df_stock) < N_AVG + 1:
                # print(f"    {ts_code} 在 {t_date_str} 数据不足，跳过。")
                continue
            
            # 确保最后一条数据是 T 日的
            if daily_df_stock.iloc[-1]['trade_date'] != t_date_str:
                # print(f"    {ts_code} 在 {t_date_str} 当日无数据，跳过。")
                continue

            if check_giant_volume_drop(daily_df_stock):
                print(f"    信号: {ts_code} 在 {t_date_str} 满足巨量下跌条件。")
                buy_signals.append(ts_code)
        
        if not buy_signals:
            print(f"  T日 {t_date_str}: 未发现买入信号。")
            continue
        
        print(f"  T日 {t_date_str}: 发现 {len(buy_signals)} 个买入信号: {buy_signals}")

        # 3.4 模拟交易 (T+1日开盘买入, T+N日收盘卖出)
        # 找到T+1日
        idx_t_plus_1 = idx_t_date + 1
        if idx_t_plus_1 >= len(trading_days):
            print(f"  T日 {t_date_str} 是最后可获取的交易日或接近最后，无法进行T+1买入。")
            continue
        t_plus_1_date_str = trading_days[idx_t_plus_1]
        
        # 获取T+1日这些信号股票的开盘价
        buy_prices_df = get_stocks_data_on_date(engine, buy_signals, t_plus_1_date_str)
        if buy_prices_df.empty:
            print(f"  未能获取到 {t_plus_1_date_str} (T+1) 的开盘价数据。")
            continue

        for signal_ts_code in buy_signals:
            buy_info = buy_prices_df[buy_prices_df['ts_code'] == signal_ts_code]
            if buy_info.empty or pd.isna(buy_info.iloc[0]['open']):
                print(f"    {signal_ts_code} 在 {t_plus_1_date_str} (T+1) 无开盘价或停牌，无法买入。")
                continue
            
            buy_price = buy_info.iloc[0]['open']
            buy_date = t_plus_1_date_str # T+1日

            trade_summary = {
                'ts_code': signal_ts_code,
                'signal_date_T': t_date_str,
                'buy_date_T+1': buy_date,
                'buy_price_T+1_open': buy_price
            }
            print(f"    模拟买入: {signal_ts_code} at {buy_price} on {buy_date}")

            # 对每个持有周期进行计算
            for hold_period in HOLDING_PERIODS:
                idx_sell_date = idx_t_plus_1 + hold_period -1 # T+1是第1天，所以T+3是idx_t_plus_1 + 2
                
                if idx_sell_date >= len(trading_days):
                    print(f"      {signal_ts_code} 持有 {hold_period} 天后超出回测期。")
                    trade_summary[f'sell_date_T+{hold_period}'] = None
                    trade_summary[f'sell_price_T+{hold_period}_close'] = None
                    trade_summary[f'return_T+{hold_period}'] = None
                    continue

                sell_date_candidate = trading_days[idx_sell_date]
                
                # 获取卖出日的收盘价，需要处理股票当天是否存在数据（例如停牌）
                # 简单处理：如果当天无数据，则收益无法计算或顺延（当前未实现顺延）
                sell_price_data = get_stocks_data_on_date(engine, [signal_ts_code], sell_date_candidate)
                
                sell_price = np.nan
                actual_sell_date = sell_date_candidate # 初始假设卖出日就是候选日

                if not sell_price_data.empty and not pd.isna(sell_price_data.iloc[0]['close']):
                    sell_price = sell_price_data.iloc[0]['close']
                else:
                    # 尝试顺延卖出日，如果当天停牌或无数据
                    print(f"      {signal_ts_code} 在预定卖出日 {sell_date_candidate} (T+{hold_period}) 可能停牌或无数据，尝试顺延...")
                    found_sell_price = False
                    for k_delay in range(1, 6): # 最多顺延5个交易日
                        idx_delayed_sell_date = idx_sell_date + k_delay
                        if idx_delayed_sell_date >= len(trading_days):
                            break 
                        delayed_sell_date_candidate = trading_days[idx_delayed_sell_date]
                        delayed_sell_price_data = get_stocks_data_on_date(engine, [signal_ts_code], delayed_sell_date_candidate)
                        if not delayed_sell_price_data.empty and not pd.isna(delayed_sell_price_data.iloc[0]['close']):
                            sell_price = delayed_sell_price_data.iloc[0]['close']
                            actual_sell_date = delayed_sell_date_candidate
                            print(f"        成功顺延至 {actual_sell_date}，卖出价 {sell_price}")
                            found_sell_price = True
                            break
                    if not found_sell_price:
                         print(f"        顺延失败，无法找到 {signal_ts_code} 的有效卖出价格。")


                trade_summary[f'sell_date_T+{hold_period}'] = actual_sell_date if not pd.isna(sell_price) else sell_date_candidate
                trade_summary[f'sell_price_T+{hold_period}_close'] = sell_price
                
                if not pd.isna(sell_price) and buy_price != 0:
                    trade_return = (sell_price - buy_price) / buy_price
                    trade_summary[f'return_T+{hold_period}'] = trade_return
                else:
                    trade_summary[f'return_T+{hold_period}'] = np.nan
                
                print(f"      T+{hold_period}: SellDate={actual_sell_date}, SellPrice={sell_price}, Return={trade_summary.get(f'return_T+{hold_period}', 'N/A')}")
            
            all_trades_results.append(trade_summary)

    # 4. 保存结果到CSV
    if not all_trades_results:
        print("回测完成，但没有产生任何有效交易。")
        return

    results_df = pd.DataFrame(all_trades_results)
    
    # 计算汇总统计
    summary_stats = {}
    for period in HOLDING_PERIODS:
        col_name = f'return_T+{period}'
        if col_name in results_df:
            valid_returns = results_df[col_name].dropna()
            if not valid_returns.empty:
                summary_stats[f'Avg_Return_T+{period}'] = valid_returns.mean()
                summary_stats[f'Std_Return_T+{period}'] = valid_returns.std()
                summary_stats[f'Win_Rate_T+{period}'] = (valid_returns > 0).mean() # 胜率
                summary_stats[f'Median_Return_T+{period}'] = valid_returns.median()
                summary_stats[f'Num_Trades_T+{period}'] = len(valid_returns)
            else:
                summary_stats[f'Avg_Return_T+{period}'] = np.nan
                summary_stats[f'Std_Return_T+{period}'] = np.nan
                summary_stats[f'Win_Rate_T+{period}'] = np.nan
                summary_stats[f'Median_Return_T+{period}'] = np.nan
                summary_stats[f'Num_Trades_T+{period}'] = 0
                
    print("=== 回测汇总统计 ===")
    for key, value in summary_stats.items():
        print(f"{key}: {value:.4f}" if isinstance(value, float) else f"{key}: {value}")

    # 将汇总统计也追加到CSV或单独保存
    # 为了简单，这里只保存详细交易
    results_df.to_csv(OUTPUT_CSV_FILE, index=False, encoding='utf-8-sig')
    print(f"详细回测结果已保存到: {OUTPUT_CSV_FILE}")

    # 也可以将汇总统计保存
    summary_df = pd.DataFrame([summary_stats])
    summary_output_file = "backtest_summary_stats_fear_drop.csv"
    summary_df.to_csv(summary_output_file, index=False, encoding='utf-8-sig')
    print(f"汇总统计结果已保存到: {summary_output_file}")


if __name__ == "__main__":
    # 确保在运行脚本的目录下有 get_data 文件夹，并且里面有 db_config.py
    # 例如，如果脚本在 /Users/<USER>/project/backtest_fear_drop_strategy.py
    # 则 db_config.py 应该在 /Users/<USER>/project/get_data/db_config.py
    
    # 调整Python的模块搜索路径，以便能找到 get_data.db_config
    # 这通常在项目根目录下运行脚本时不需要，但如果直接运行此文件且get_data不在标准路径中则可能需要
    # import sys
    # script_dir = os.path.dirname(os.path.abspath(__file__))
    # project_root = os.path.dirname(script_dir) # 假设get_data在项目根目录的上一级
    # if project_root not in sys.path:
    #    sys.path.insert(0, project_root)
    # print(f"Project root (for db_config import): {project_root}")

    run_backtest() 