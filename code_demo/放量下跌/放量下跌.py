"""
@Author: ji<PERSON><PERSON>
@Date: 2025/5/14 14:03
@File: 放量下跌.py
@Version: 1.0
@Description:目前看回测效果，出信号后，第二天买入，持股 10天收益最高。
四个硬条件
	•	巨量下跌：当日跌幅 ≤ -5 %。
	•	巨量放大：当日成交量 ≥ 20 日均量 × 2。
	•	市场恐慌：全市场涨跌家数比 ≤ 0.20。
	•	基本面没塌：不是 *ST，最近一季净利 YoY > -20 %。
"""
"""
放量下跌买入策略
=================================================
策略说明: 当股票在巨量下跌日出现放量、整体市场恐慌情绪极端、且公司基本面未明显恶化时进行买入。
使用严格止损来防止进一步下跌。

核心筛选条件（默认值 - 可根据回测调整）:
    • PRICE_DROP_PCT   : -5   -> 当日跌幅必须 ≤ -5%（"巨量下跌"）
    • VOL_RATIO_MIN    :  2   -> 当日成交量 ÷ 20日平均成交量 ≥ 2（"巨量放大"）
    • SENTIMENT_THRESH : 0.20 -> 市场上涨家数/(上涨+下跌家数) ≤ 0.20（极端恐慌）
    • FUNDAMENTAL_OK   : 最新季度同比净利润 > -20% 且股票不是 *ST/ST（基本面尚可）
    • STOPLOSS_PCT     : 0.08 -> 设置入场价格8%以下的保护性止损（可根据需要自定义）

依赖项
------------
    pip install pandas numpy sqlalchemy pymysql

需要配置本地数据库连接信息，参见 get_data/db_config.py

本脚本设计为每日收盘后执行，用于扫描整个A股市场。筛选结果以字典格式输出，可以连接到您的交易执行层
（xtquant / PyBroker / 券商API等）。回测和参数优化请使用您自己的框架（Backtrader、pybroker、
zipline-reloaded等）。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import multiprocessing as mp
from sqlalchemy import text

# 导入数据库连接配置
from get_data.db_config import get_engine

# === 配置参数 ==========================================================
N_AVG = 20           # 均量回溯窗口
PRICE_DROP_PCT = -5  # 价格下跌阈值百分比
VOL_RATIO_MIN = 2    # 成交量放大倍数
SENTIMENT_THRESH = 0.20  # 市场恐慌阈值（涨跌比）
STOPLOSS_PCT = 0.08      # 保护性止损8%

# === 数据处理函数 ===========================================================

def get_daily(ts_code: str, start: str, end: str) -> pd.DataFrame:
    """获取指定股票在[start, end]期间的日线数据（日期格式YYYYMMDD）"""
    engine = get_engine
    
    # 转换日期格式为数据库格式
    start_date = pd.to_datetime(start).strftime("%Y-%m-%d")
    end_date = pd.to_datetime(end).strftime("%Y-%m-%d")
    
    query = text(f"""
        SELECT trade_date, close, pct_change as pct_chg, vol
        FROM stk_factor
        WHERE ts_code = :ts_code 
        AND trade_date BETWEEN :start_date AND :end_date
        ORDER BY trade_date
    """)
    
    df = pd.read_sql(query, engine, params={"ts_code": ts_code, 
                                           "start_date": start_date, 
                                           "end_date": end_date})
    
    if df.empty:
        return df
        
    # 转换日期列格式以与原代码兼容
    df['trade_date'] = pd.to_datetime(df['trade_date']).dt.strftime('%Y%m%d')
    return df


def giant_volume_drop(df: pd.DataFrame) -> bool:
    """判断最新一行是否满足价格暴跌+成交量暴增条件"""
    if len(df) < N_AVG + 1:
        return False
    today = df.iloc[-1]
    vol_mean = df["vol"].iloc[-N_AVG-1:-1].mean()
    vol_ratio = today["vol"] / vol_mean if vol_mean else 0
    return today["pct_chg"] <= PRICE_DROP_PCT and vol_ratio >= VOL_RATIO_MIN


def market_sentiment(trade_date: str) -> float:
    """简单市场情绪指标：上涨家数/(上涨+下跌家数)"""
    engine = get_engine
    
    # 转换日期格式
    date_fmt = pd.to_datetime(trade_date).strftime("%Y-%m-%d")
    
    query = text("""
        SELECT pct_change as pct_chg
        FROM stk_factor
        WHERE trade_date = :trade_date
    """)
    
    mkt = pd.read_sql(query, engine, params={"trade_date": date_fmt})
    
    if mkt.empty:
        return 1.0
        
    adv = (mkt["pct_chg"] > 0).sum()
    dec = (mkt["pct_chg"] < 0).sum()
    return adv / (adv + dec) if (adv + dec) else 1.0


def fundamentals_ok(ts_code: str, end_date: str) -> bool:
    """基本面简单检查：只排除*ST股票"""
    engine = get_engine
    
    # 查询股票名称
    query_basic = text("""
        SELECT name
        FROM stock_basic
        WHERE ts_code = :ts_code
    """)
    
    base = pd.read_sql(query_basic, engine, params={"ts_code": ts_code})
    
    # 只检查是否为ST股票，其他财务判断暂时去掉
    if base.empty or base.iloc[0]["name"].startswith("ST"):
        return False
        
    # 不再进行财务指标检查，直接返回True
    return True

# === 扫描器 ===============================================================

def process_stock(args):
    """处理单只股票的函数，便于多进程处理"""
    ts_code, trade_date, start = args
    daily = get_daily(ts_code, start, trade_date)
    if daily.empty:
        return None
    if not giant_volume_drop(daily):
        return None
        
    # 基本面检查简化为只检查是否为ST股票
    if not fundamentals_ok(ts_code, trade_date):
        return None
        
    close_px = daily.iloc[-1]["close"]
    return {
        "ts_code": ts_code,
        "date": trade_date,
        "close": close_px,
        "stoploss": round(close_px * (1 - STOPLOSS_PCT), 3),
    }

def scan(trade_date: str, universe: list[str]) -> list[dict]:
    """返回满足买入条件的股票列表，并附加止损价格"""
    # 检查市场恐慌程度
    if market_sentiment(trade_date) > SENTIMENT_THRESH:
        return []  # 市场恐慌程度不够

    start = (pd.to_datetime(trade_date) - timedelta(days=N_AVG + 5)).strftime("%Y%m%d")
    
    # 使用多进程加速处理
    cpu_count = mp.cpu_count()
    with mp.Pool(processes=cpu_count) as pool:
        args = [(ts_code, trade_date, start) for ts_code in universe]
        results = pool.map(process_stock, args)
    
    # 过滤掉None结果
    ideas = [r for r in results if r is not None]
    return ideas

# === 入口函数 ===========================================================

def main():
    """主函数：扫描市场并输出买入信号"""
    trade_date = datetime.now().strftime("%Y%m%d")  # 收盘后运行
    print(trade_date)

    # 从数据库获取股票列表
    engine = get_engine
    query = text("""
        SELECT ts_code 
        FROM stock_basic
        WHERE list_status = 'L'
    """)
    
    universe_df = pd.read_sql(query, engine)
    universe = universe_df["ts_code"].tolist()
    
    picks = scan(trade_date, universe)
    if not picks:
        print("[信息] 今日没有买入信号 - 可能是市场恐慌程度不够或没有股票满足筛选条件。")
        return

    print("=== 买入候选股票 ===")
    for p in picks:
        print(p)

    # === 可选：自动执行交易 ==========================================
    # 与您的券商/xtquant/pybroker集成。
    # 示例（伪代码）:
    # for idea in picks:
    #     xtquant.place_order(
    #         code=idea["ts_code"],
    #         price=idea["close"],
    #         qty=calc_qty(capital, idea["close"]),
    #         stoploss=idea["stoploss"],
    #     )

if __name__ == "__main__":
    main()
