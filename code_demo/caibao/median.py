import tushare as ts
import pandas as pd
import time

# 初始化
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

# 拉股票基础信息（包括行业）
stock_basic = pro.stock_basic(exchange='', list_status='L', fields='ts_code,industry')

# 准备存储
industry_medians = []

#拉过去两年每季度业绩快报
# periods = ['20220331', '20220630', '20220930', '20221231',
#            '20230331', '20230630', '20230930', '20231231']

periods = ['20231231']

for period in periods:
    print(f"处理 {period}")
    express = pro.query('express', period=period, fields='ts_code,yoy_eps')
    print(express.head())
    print(express.shape)
    print(express.isnull().sum())
    print(express.columns)
    merged = express.merge(stock_basic, on='ts_code')
    grouped = merged.groupby('industry')['yoy_eps'].median().reset_index()
    grouped['period'] = period
    industry_medians.append(grouped)
    time.sleep(1)  # 防止接口频率限制

# 合并所有季度
all_medians = pd.concat(industry_medians)

# 取最近季度的中位数（你也可以改成平均过去几个季度）
latest_period = periods[-1]
latest_medians = all_medians[all_medians['period'] == latest_period][['industry', 'yoy_eps']].rename(columns={'yoy_eps': 'industry_median'})

# 映射到每个股票
final_df = stock_basic.merge(latest_medians, on='industry', how='left')[['ts_code', 'industry_median']]

# 导出 CSV
final_df.to_csv('industry_median_eps.csv', index=False)

print("✅ 已生成 industry_median.csv")