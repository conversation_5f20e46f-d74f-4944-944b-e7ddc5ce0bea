"""
@Author: ji<PERSON><PERSON>
@Date: 2025/5/6 14:20
@File: caibao_v1.0.py
@Version: 1.0
@Description: 
"""
import tushare as ts
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 初始化
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

# 拉业绩快报（过去两年）
express = pro.query('express', start_date='20220101', end_date='20241231',
                    fields='ts_code,ann_date,net_profit_yoy')

# 拉行业中位数（提前准备好）
industry_median = pd.read_csv('industry_median.csv')

# 合并 & 筛选超预期
merged = express.merge(industry_median, on='ts_code')
merged['超预期'] = merged['net_profit_yoy'] > merged['industry_median'] + 20
signals = merged[merged['超预期']]

print(f"筛选出 {len(signals)} 条超预期信号")

# 定义获取未来收益
def get_forward_returns(ts_code, start_date, hold_days, stop_loss=0.05, take_profit=0.10):
    end_date = (datetime.strptime(start_date, '%Y%m%d') + timedelta(days=hold_days*2)).strftime('%Y%m%d')
    df = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
    if df.empty or len(df) < 2:
        return None
    df = df.sort_values('trade_date')
    entry_price = df.iloc[0]['close']
    for i in range(1, min(hold_days + 1, len(df))):
        high = df.iloc[i]['high']
        low = df.iloc[i]['low']
        # 止盈止损判断
        if high >= entry_price * (1 + take_profit):
            return take_profit
        if low <= entry_price * (1 - stop_loss):
            return -stop_loss
    exit_price = df.iloc[min(hold_days, len(df)-1)]['close']
    return (exit_price - entry_price) / entry_price

results = []
for idx, row in signals.iterrows():
    for hold_days in [3, 5, 10]:
        ret = get_forward_returns(row['ts_code'], row['ann_date'], hold_days)
        if ret is not None:
            results.append({'ts_code': row['ts_code'], 'ann_date': row['ann_date'],
                            'hold_days': hold_days, 'ret': ret})

result_df = pd.DataFrame(results)

# 汇总统计
summary = result_df.groupby('hold_days')['ret'].agg(['mean', 'count', lambda x: (x > 0).mean(), 'min'])
summary.columns = ['平均收益', '样本数', '胜率', '最大回撤']
print(summary)