"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/1/6 20:46
@Description: 
"""
import pandas as pd
import numpy as np

def calculate_pearson_correlation(df, col1, col2):
  """
  计算 DataFrame 中两列的皮尔逊相关系数。

  Args:
    df: 包含数据的 pandas DataFrame。
    col1: 第一列的列名。
    col2: 第二列的列名。

  Returns:
    皮尔逊相关系数, 如果出现除零错误返回 NaN。
  """
  # 方法一：使用 pandas 的 .corr() 方法
  # pearson_corr = df[col1].corr(df[col2], method='pearson')

  # 方法二：使用 numpy 的 .corrcoef() 方法
  # correlation_matrix = np.corrcoef(df[col1], df[col2])
  # pearson_corr = correlation_matrix[0, 1]

  # 方法三：手动计算
  x = df[col1]
  y = df[col2]

  # 处理缺失值：删除含有缺失值的行
  mask = ~np.isnan(x) & ~np.isnan(y)
  x = x[mask]
  y = y[mask]

  if len(x) < 2 or len(y) < 2:  # 检查是否有足够的有效数据点
      return np.nan

  x_mean = np.mean(x)
  y_mean = np.mean(y)

  numerator = np.sum((x - x_mean) * (y - y_mean))
  denominator = np.sqrt(np.sum((x - x_mean)**2) * np.sum((y - y_mean)**2))

  if denominator == 0:
    return np.nan  # 如果标准差为零，返回 NaN
  else:
    pearson_corr = numerator / denominator
    return pearson_corr

# 创建示例 DataFrame
data = {
    'trade_date': ['2024/1/30', '2024/1/31', '2024/2/1', '2024/2/2', '2024/2/5', '2024/2/6', '2024/2/7', '2024/2/8', '2024/2/19', '2024/2/20', '2024/2/21', '2024/2/22', '2024/2/23', '2024/2/26', '2024/2/27', '2024/2/28', '2024/2/29', '2024/3/1', '2024/3/4', '2024/3/5', '2024/3/6', '2024/3/7', '2024/3/8', '2024/3/11', '2024/3/12', '2024/3/13'],
    'close_hfq': [1108.77, 1104.1, 1098.27, 1081.93, 1094.77, 1129.78, 1121.61, 1128.61, 1144.95, 1146.12, 1260.5, 1272.17, 1266.34, 1228.99, 1225.49, 1224.32, 1235.99, 1224.32, 1205.65, 1217.32, 1205.65, 1211.48, 1211.48, 1221.99, 1232.49, 1205.65],
    'alpha51': [23.34999999999991, 4.670000000000073, 5.829999999999927, 16.339999999999918, -12.8400, -35.0100, 8.170000000000073, 1.0000, 1.0000, 1.0000, -114.3800, -11.6700, 5.830000000000155, 37.34999999999991, 3.5000, 1.1700000000000728, -11.6700, 11.670000000000073, 18.669999999999845, -11.6700, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000]
}
df = pd.DataFrame(data)

# 计算 'close_hfq' 和 'alpha51' 的皮尔逊相关系数
pearson_correlation = calculate_pearson_correlation(df, 'close_hfq', 'alpha51')

print(f"'close_hfq' 和 'alpha51' 的皮尔逊相关系数为: {pearson_correlation}")
