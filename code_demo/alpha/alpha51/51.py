"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/1/6 20:15
@Description: 
"""
import pandas as pd
import numpy as np


def calculate_alpha_improved(close_prices: pd.Series, threshold: float = -0.05) -> pd.Series:
    """
    计算 Alpha#51 指标。

    参数:
        close_prices: 一个 Pandas Series，代表收盘价数据。
        threshold:    用于比较的阈值，默认为 -0.05。

    返回值:
        一个 Pandas Series，包含计算出的 Alpha#51 值。前 20 个值为 NaN，因为需要至少 21 个数据点来计算。
    """
    if len(close_prices) < 21:
        return pd.Series(np.full_like(close_prices, np.nan), index=close_prices.index)

    close_20: pd.Series = close_prices.shift(20)
    close_10: pd.Series = close_prices.shift(10)
    close_1: pd.Series = close_prices.shift(1)

    # 计算条件
    condition: pd.Series = ((close_20 - close_10) / 10 - (close_10 - close_prices) / 10) < threshold

    # 计算结果
    result: pd.Series = pd.Series(-1 * (close_prices - close_1), index=close_prices.index)
    result[condition] = 1

    # 前 20 个值设为 NaN
    result.iloc[:20] = np.nan

    return result
