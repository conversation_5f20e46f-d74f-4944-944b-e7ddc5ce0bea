"""
@Author: JiangXin
@Date: 2025/1/6 20:48
@Description: 
"""
import pandas as pd
import statsmodels.api as sm
import statsmodels.formula.api as smf

# 从CSV文件读取数据
data = pd.read_csv('alpha51_results.csv')

# 方法一：使用 statsmodels.formula.api (smf) - 更方便，推荐

# 构建回归模型公式（类似于 R 语言的 formula）
formula = 'close_hfq ~ alpha51'

# 拟合线性回归模型
model = smf.ols(formula, data=data).fit()

# 打印回归结果
print(model.summary())

# 方法二：使用 statsmodels.api (sm) - 更底层

# 添加常数项到自变量
X = sm.add_constant(data['alpha51'])

# 构建模型
model_sm = sm.OLS(data['close_hfq'], X)

# 拟合模型
results_sm = model_sm.fit()

# 打印回归结果
print(results_sm.summary())

# 获取模型参数
print("\n模型参数:")
print(model.params)

# 获取 R-squared
print(f"\nR-squared: {model.rsquared}")

# 进行预测 (使用 alpha51 的值)
# 创建新的预测数据DataFrame
predict_data = pd.DataFrame({
    'alpha51': [10, 20, 30]  # 假设的 alpha51 新值
})

# 使用模型进行预测
predictions = model.predict(predict_data)
print(f"\n预测值:\n{predictions}")

# 手动去除异常值后再次进行回归分析
print("\n去除异常值后的回归分析：")
data_cleaned = data[data['alpha51'] > -50]
model_cleaned = smf.ols('close_hfq ~ alpha51', data=data_cleaned).fit()
print(model_cleaned.summary())
