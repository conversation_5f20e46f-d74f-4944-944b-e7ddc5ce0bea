"""
@Author: Jiang<PERSON>in
@Date: 2025/1/6
@Description: 从MySQL数据库中获取数据并计算Alpha#51因子
"""
import pandas as pd
import numpy as np
import pymysql
from sqlalchemy import create_engine
from typing import Optional

class Alpha51Calculator:
    def __init__(self, host: str, user: str, password: str, database: str, port: int = 3306):
        """
        初始化数据库连接
        
        参数:
            host: 数据库主机地址
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名
            port: 数据库端口号
        """
        self.engine = create_engine(f'mysql+pymysql://{user}:{password}@{host}:{port}/{database}')
    
    def get_stock_data(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        从数据库获取股票数据
        
        参数:
            ts_code: 股票代码
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            
        返回:
            包含股票数据的DataFrame
        """
        query = f"""
        SELECT trade_date, close_hfq
        FROM stk_factor
        WHERE ts_code = '{ts_code}'
        AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        
        df = pd.read_sql(query, self.engine)
        df.set_index('trade_date', inplace=True)
        return df

    def calculate_alpha51(self, close_prices: pd.Series, threshold: float = -0.05) -> pd.Series:
        """
        计算 Alpha#51 指标
        
        参数:
            close_prices: 收盘价数据Series
            threshold: 阈值，默认为-0.05
            
        返回:
            Alpha#51因子值
        """
        if len(close_prices) < 21:
            return pd.Series(np.full_like(close_prices, np.nan), index=close_prices.index)

        close_20 = close_prices.shift(20)
        close_10 = close_prices.shift(10)
        close_1 = close_prices.shift(1)

        # 计算条件
        condition = ((close_20 - close_10) / 10 - (close_10 - close_prices) / 10) < threshold

        # 计算结果
        result = pd.Series(-1 * (close_prices - close_1), index=close_prices.index)
        result[condition] = 1

        # 前20个值设为NaN
        result.iloc[:20] = np.nan

        return result

    def run_calculation(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        运行完整的计算流程
        
        参数:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        返回:
            包含原始数据和Alpha51因子值的DataFrame
        """
        # 获取数据
        df = self.get_stock_data(ts_code, start_date, end_date)
        
        # 计算Alpha51
        df['alpha51'] = self.calculate_alpha51(df['close_hfq'])
        
        return df

def main():
    # 示例使用
    calculator = Alpha51Calculator(
        host='localhost',
        user='root',
        password='123456',  
        database='tushare'
    )
    
    # 计算示例：计算某只股票最近一年的Alpha51因子
    result = calculator.run_calculation(
        ts_code='000001.SZ',  # 示例股票代码
        start_date='2024-01-01',
        end_date='2025-01-06'
    )
    
    print("数据总行数:", len(result))
    print("\n前10行数据：")
    print(result.head(10))
    print("\n后10行数据：")
    print(result.tail(10))
    
    # 保存结果到CSV文件
    result.to_csv('alpha51_results.csv')
    print("\n结果已保存到 alpha51_results.csv")

if __name__ == "__main__":
    main()
