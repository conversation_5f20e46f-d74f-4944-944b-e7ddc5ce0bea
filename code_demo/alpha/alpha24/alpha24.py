import numpy as np
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
import sys
import os
from statsmodels.regression.linear_model import OLS
import statsmodels.api as sm
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from get_data.db_config import DB_URL

def get_hfq_close_prices(ts_code, start_date=None, end_date=None):
    """
    从数据库获取指定股票的后复权收盘价
    
    参数:
    ts_code: str, 股票代码
    start_date: str, 开始日期，格式：'YYYY-MM-DD'
    end_date: str, 结束日期，格式：'YYYY-MM-DD'
    
    返回:
    pandas.Series: 后复权收盘价序列，索引为交易日期
    """
    # 创建数据库引擎
    engine = create_engine(DB_URL)
    
    # 构建SQL查询
    query = """
        SELECT trade_date, close_hfq 
        FROM stk_factor 
        WHERE ts_code = :ts_code 
    """
    
    if start_date:
        query += " AND trade_date >= :start_date"
    if end_date:
        query += " AND trade_date <= :end_date"
        
    query += " ORDER BY trade_date"
    
    # 准备查询参数
    params = {'ts_code': ts_code}
    if start_date:
        params['start_date'] = start_date
    if end_date:
        params['end_date'] = end_date
    
    # 执行查询
    with engine.connect() as conn:
        result = pd.read_sql(text(query), conn, params=params)
    
    # 将结果转换为Series
    if not result.empty:
        result.set_index('trade_date', inplace=True)
        return result['close_hfq']
    else:
        return pd.Series()

def alpha24(close):
    """
    实现 Alpha#24 因子
    
    参数:
    close: pandas.Series, 收盘价序列数据
    
    公式:
    Alpha#24 = IF (((delta((sum(close, 100) / 100), 100) / delay(close, 100)) < 0.05) ||
               ((delta((sum(close, 100) / 100), 100) / delay(close, 100)) == 0.05))
               THEN (-1 * (close - ts_min(close, 100)))
               ELSE (-1 * delta(close, 3))
    
    返回:
    pandas.Series: Alpha#24 因子值
    """
    # 计算 100 日移动平均
    ma100 = close.rolling(window=100).mean()
    
    # 计算移动平均的 100 日差分
    delta_ma100 = ma100.diff(100)
    
    # 计算收盘价的 100 日延迟
    delay_close_100 = close.shift(100)
    
    # 计算条件部分：delta_ma100 / delay_close_100
    condition_value = delta_ma100 / delay_close_100
    
    # 计算收盘价的 100 日最小值
    min_close_100 = close.rolling(window=100).min()
    
    # 计算收盘价的 3 日差分
    delta_close_3 = close.diff(3)
    
    # 根据条件计算最终结果
    result = pd.Series(index=close.index)
    
    # 条件判断：< 0.05 或 == 0.05
    condition = (condition_value <= 0.05)
    
    # 根据条件选择不同的计算结果
    result[condition] = -1 * (close[condition] - min_close_100[condition])
    result[~condition] = -1 * delta_close_3[~condition]
    
    return result

def calculate_returns(close, periods):
    """
    计算多个周期的普通收益率和对数收益率
    
    参数:
    close: pandas.Series, 收盘价序列数据
    periods: list, 周期列表
    
    返回:
    pandas.DataFrame: 包含普通收益率和对数收益率的数据框
    """
    returns = pd.DataFrame()
    
    for period in periods:
        # 计算普通收益率
        returns[f'return_{period}d'] = close.pct_change(period)
        
        # 计算对数收益率
        returns[f'log_return_{period}d'] = np.log(close/close.shift(period))
    
    return returns

def perform_regression_analysis(y, X, period_name, return_type='普通收益率'):
    """
    执行回归分析并返回结果
    
    参数:
    y: pandas.Series, 因变量（收益率）
    X: pandas.Series, 自变量（因子值）
    period_name: str, 周期名称，用于输出
    return_type: str, 收益率类型（普通收益率或对数收益率）
    
    返回:
    statsmodels.regression.linear_model.RegressionResultsWrapper: 回归结果
    """
    # 添加常数项
    X = sm.add_constant(X)
    
    # 执行回归
    model = OLS(y, X)
    results = model.fit()
    
    # 打印回归结果
    print(f"\n{period_name} {return_type}的回归分析结果：")
    print("=" * 50)
    print(results.summary().tables[1])
    
    # 绘制回归图
    plt.figure(figsize=(10, 6))
    plt.scatter(X.iloc[:, 1], y, alpha=0.5)
    plt.plot(X.iloc[:, 1], results.fittedvalues, 'r', linewidth=2)
    plt.xlabel('Alpha#24 因子值')
    plt.ylabel(f'{return_type} ({period_name})')
    plt.title(f'Alpha#24 因子与{period_name}{return_type}回归分析')
    plt.grid(True)
    
    # 保存图片
    plt.savefig(f'regression_plot_{return_type}_{period_name}.png')
    plt.close()
    
    return results

if __name__ == "__main__":
    # 示例：获取某只股票的后复权收盘价并计算 Alpha#24
    ts_code = "300750.SZ"  # 以平安银行为例
    start_date = "2020-01-01"
    end_date = "2025-06-30"
    
    # 获取后复权收盘价数据
    close_prices = get_hfq_close_prices(ts_code, start_date, end_date)
    
    if not close_prices.empty:
        # 计算 Alpha#24
        alpha24_values = alpha24(close_prices)
        
        # 计算多个周期的普通收益率和对数收益率
        returns = calculate_returns(close_prices, periods=[1, 5, 10, 20])
        
        # 将因子值和收益率合并到一个 DataFrame 中
        analysis_df = pd.DataFrame({
            'alpha24': alpha24_values,
            'close_price': close_prices
        })
        analysis_df = pd.concat([analysis_df, returns], axis=1)
        
        # 删除包含 NaN 的行
        analysis_df = analysis_df.dropna()
        
        print(f"\n{ts_code} 的数据分析结果：")
        print("\n1. 因子值统计信息：")
        print(alpha24_values.describe())
        
        print("\n2. 各周期收益率统计信息：")
        print("\n普通收益率统计：")
        print(returns[[col for col in returns.columns if 'log' not in col]].describe())
        print("\n对数收益率统计：")
        print(returns[[col for col in returns.columns if 'log' in col]].describe())
        
        print("\n3. 因子值与未来收益率的相关性：")
        correlations = pd.DataFrame()
        regression_results = {}
        
        # 对每个周期进行回归分析
        for period in [1, 5, 10, 20]:
            # 普通收益率分析
            period_name = f'return_{period}d'
            log_period_name = f'log_return_{period}d'
            
            # 计算普通收益率相关性
            corr = analysis_df['alpha24'].corr(analysis_df[period_name])
            correlations.loc['普通收益率相关性', period_name] = corr
            
            # 计算对数收益率相关性
            log_corr = analysis_df['alpha24'].corr(analysis_df[log_period_name])
            correlations.loc['对数收益率相关性', period_name] = log_corr
            
            # 执行普通收益率回归分析
            regression_results[f'普通_{period_name}'] = perform_regression_analysis(
                analysis_df[period_name],
                analysis_df['alpha24'],
                f'{period}日收益率',
                '普通收益率'
            )
            
            # 执行对数收益率回归分析
            regression_results[f'对数_{period_name}'] = perform_regression_analysis(
                analysis_df[log_period_name],
                analysis_df['alpha24'],
                f'{period}日收益率',
                '对数收益率'
            )
        
        print("\n相关性分析结果：")
        print(correlations)
        
        # 保存结果到 CSV 文件
        output_file = f"alpha24_analysis_{ts_code.replace('.', '_')}.csv"
        analysis_df.to_csv(output_file)
        print(f"\n分析结果已保存到文件：{output_file}")
        
        # 输出回归分析的关键指标汇总
        print("\n回归分析关键指标汇总：")
        summary_df = pd.DataFrame(
            index=['R-squared', 'Adj. R-squared', 'F-statistic', 'P-value (F-stat)']
        )
        
        for period_name, results in regression_results.items():
            summary_df[period_name] = [
                results.rsquared,
                results.rsquared_adj,
                results.fvalue,
                results.f_pvalue
            ]
        
        print("\n普通收益率回归结果：")
        print(summary_df[[col for col in summary_df.columns if '普通' in col]])
        print("\n对数收益率回归结果：")
        print(summary_df[[col for col in summary_df.columns if '对数' in col]])
        
        # 绘制收益率对比图
        plt.figure(figsize=(12, 6))
        for period in [1, 5, 10, 20]:
            plt.subplot(2, 2, (period-1)//5 + 1)
            plt.hist(analysis_df[f'return_{period}d'], bins=50, alpha=0.5, label='普通收益率')
            plt.hist(analysis_df[f'log_return_{period}d'], bins=50, alpha=0.5, label='对数收益率')
            plt.title(f'{period}日收益率分布对比')
            plt.legend()
        plt.tight_layout()
        plt.savefig('returns_distribution_comparison.png')
        plt.close()
        
    else:
        print("未获取到数据，请检查股票代码和日期范围。")
