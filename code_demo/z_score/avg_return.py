"""
@Author: JiangXin
@Date: 2025/1/16 17:36
@Description: 计算股票Z分数并进行排名
"""

import pandas as pd
from sqlalchemy import create_engine, text

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'oyvla6qe',
    'database': 'test',
    'port': 3306,
    'charset': 'utf8mb4'
}

def create_engine_conn(db_config: dict = DB_CONFIG):
    """创建数据库连接引擎"""
    return create_engine(
        f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
        f"{db_config['host']}:{db_config['port']}/{db_config['database']}?"
        f"charset={db_config['charset']}"
    )

def get_stock_data(engine, ts_code: str, days: int = 252) -> pd.DataFrame:
    """
    获取股票数据
    
    参数:
        engine: 数据库连接引擎
        ts_code (str): 股票代码
        days (int): 获取的天数
        
    返回:
        pd.DataFrame: 股票数据
    """
    query = text("""
        SELECT 
            trade_date,
            close_hfq as close
        FROM stk_factor
        WHERE ts_code = :ts_code COLLATE utf8mb4_general_ci
        ORDER BY trade_date DESC
        LIMIT :days
    """)
    
    with engine.connect() as conn:
        df = pd.read_sql(query, conn, params={'ts_code': ts_code, 'days': days})
    
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df.set_index('trade_date', inplace=True)
    df.sort_index(inplace=True)
    return df

def calculate_zscore(df: pd.DataFrame) -> float:
    """
    计算Z分数
    
    参数:
        df (pd.DataFrame): 股票数据
        
    返回:
        float: Z分数
    """
    if len(df) < 20:
        return None
    
    latest_price = df['close'].iloc[-1]
    mean_price = df['close'].mean()
    std_price = df['close'].std()
    
    return (latest_price - mean_price) / std_price if std_price != 0 else 0

def get_stock_zscore_ranking(engine) -> pd.DataFrame:
    """
    获取所有股票的Z分数排名
    
    参数:
        engine: 数据库连接引擎
    
    返回:
        pd.DataFrame: Z分数排名
    """
    # 获取所有上市股票
    query = text("""
        SELECT DISTINCT
            a.ts_code,
            b.name,
            b.industry,
            a.close_hfq as latest_price,
            a.trade_date as latest_date
        FROM stk_factor a
        JOIN stock_basic b ON a.ts_code  = b.ts_code
        WHERE a.trade_date = (
            SELECT MAX(trade_date) 
            FROM stk_factor
        )
        AND b.list_status = 'L'
    """)
    
    with engine.connect() as conn:
        stocks = pd.read_sql(query, conn)
    
    # 计算每只股票的Z分数
    results = []
    total_stocks = len(stocks)
    
    for idx, stock in stocks.iterrows():
        print(f"\r处理进度: {idx+1}/{total_stocks}", end="")
        
        df = get_stock_data(engine, stock['ts_code'])
        zscore = calculate_zscore(df)
        
        if zscore is not None:
            results.append({
                'ts_code': stock['ts_code'],
                'name': stock['name'],
                'industry': stock['industry'],
                'latest_price': stock['latest_price'],
                'latest_date': stock['latest_date'],
                'zscore': zscore
            })
    
    print("\n计算完成！")
    
    # 转换为DataFrame并排序
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('zscore', ascending=False)
    
    # 格式化输出
    results_df['zscore'] = results_df['zscore'].round(4)
    results_df['latest_price'] = results_df['latest_price'].round(2)
    
    return results_df

def main():
    """主函数"""
    # 创建数据库连接
    engine = create_engine_conn()
    
    # 获取排名
    rankings = get_stock_zscore_ranking(engine)
    
    # 显示结果
    print("\n====== Z分数排名（前20名） ======")
    print(rankings.head(20).to_string(index=False))


    # 保存结果到CSV
    rankings.to_csv('stock_zscore_rankings.csv', index=False, encoding='utf-8-sig')
    print("\n结果已保存到 stock_zscore_rankings.csv")

if __name__ == "__main__":
    main()