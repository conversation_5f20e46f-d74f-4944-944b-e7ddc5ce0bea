"""
@Author: <PERSON><PERSON>in
@Date: 2025/1/16 18:39
@Description: 基于Z-score的最优入场时机分析（M4 Pro高性能版 v2）
修复了多进程序列化问题，使用更稳定的并行方法。
主要优化：
1. 批量SQL查询，一次性获取所有数据
2. NumPy向量化计算，充分利用SIMD指令
3. 简化的多进程并行处理
4. 内存优化，减少数据复制
5. 预计算常用值，避免重复计算
"""
import numpy as np
import pandas as pd
from sqlalchemy import create_engine, text
import multiprocessing as mp
import time
import psutil
from itertools import islice

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'oyvla6qe',
    'database': 'test',
    'port': 3306,
    'charset': 'utf8mb4'
}

def create_db_engine():
    """创建数据库连接引擎（用于多进程）"""
    return create_engine(
        f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
        f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?"
        f"charset={DB_CONFIG['charset']}&autocommit=true",
        pool_size=10,
        max_overflow=20,
        pool_recycle=3600
    )

def process_stock_group(args):
    """
    处理单个股票组的函数（用于多进程）
    """
    stock_data, zscore_window = args
    
    if len(stock_data) < 20:
        return None
    
    # 按日期排序并取最近的数据
    stock_data = stock_data.sort_values('trade_date').tail(zscore_window)
    
    if len(stock_data) < 20:
        return None
    
    prices = stock_data['close'].values
    
    # 向量化计算
    latest_price = prices[-1]
    mean_price = np.mean(prices)
    std_price = np.std(prices, ddof=1)
    
    if std_price > 0:
        zscore = (latest_price - mean_price) / std_price
        
        return {
            'ts_code': stock_data['ts_code'].iloc[0],
            'name': stock_data['name'].iloc[0],
            'latest_date': stock_data['trade_date'].iloc[-1].strftime('%Y-%m-%d'),
            'latest_price': round(float(latest_price), 2),
            'zscore': float(zscore)
        }
    
    return None

def analyze_single_stock(args):
    """
    分析单只股票的函数（用于多进程）
    """
    stock_data, optimal_threshold, c, L = args
    
    zscore = stock_data['zscore']
    
    # 计算价值和成本
    value = np.exp(-zscore**2/2)
    cost = abs(zscore) * c
    net_value = value - cost
    
    # 生成建议
    abs_zscore = abs(zscore)
    if abs_zscore <= optimal_threshold:
        recommendation = "适合入场"
    elif abs_zscore <= L:
        recommendation = "观望等待"
    else:
        recommendation = "风险较高"
    
    return {
        'ts_code': stock_data['ts_code'],
        'name': stock_data['name'],
        'latest_date': stock_data['latest_date'],
        'latest_price': stock_data['latest_price'],
        'zscore': round(zscore, 4),
        'threshold': round(optimal_threshold, 4),
        'net_value': round(net_value, 4),
        'recommendation': recommendation
    }

class HighPerformanceEntryAnalyzer:
    """
    高性能股票入场时机分析器（M4 Pro优化版 v2）
    """
    
    def __init__(self, 
                 r: float = 0.05,
                 c: float = 0.02,
                 L: float = 3.0,
                 zscore_window: int = 252,
                 db_config: dict = DB_CONFIG):
        """初始化高性能分析器"""
        self.r = r
        self.c = c
        self.L = L
        self.zscore_window = zscore_window
        self.engine = create_db_engine()
        
        # M4 Pro优化：使用10个进程（留2个给系统）
        self.n_processes = min(10, mp.cpu_count() - 2)
        
        # 预计算最优阈值
        self.optimal_threshold = self._precompute_optimal_threshold()
        
        print(f"🚀 初始化完成 - 使用{self.n_processes}个进程，最优阈值: {self.optimal_threshold:.4f}")

    def _precompute_optimal_threshold(self) -> float:
        """预计算最优入场阈值"""
        x_values = np.arange(0.01, self.L, 0.001)
        h_values = np.exp(-x_values**2/2) - x_values * self.c
        return x_values[np.argmax(h_values)]

    def get_all_stock_data_optimized(self) -> pd.DataFrame:
        """批量获取所有股票数据"""
        print("📊 开始批量获取股票数据...")
        start_time = time.time()
        
        query = text("""
            SELECT 
                sf.ts_code,
                sb.name,
                sf.trade_date,
                sf.close_hfq as close
            FROM stk_factor sf
            JOIN stock_basic sb ON sf.ts_code = sb.ts_code
            WHERE sb.list_status = 'L'
            AND sf.ts_code NOT LIKE '%BJ'
            AND sf.trade_date >= (
                SELECT DATE_SUB(MAX(trade_date), INTERVAL :days DAY)
                FROM stk_factor
                ORDER BY trade_date DESC
                LIMIT 1
            )
            ORDER BY sf.ts_code, sf.trade_date
        """)
        
        with self.engine.connect() as conn:
            df = pd.read_sql(query, conn, params={'days': self.zscore_window + 10})
        
        if not df.empty:
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df['close'] = pd.to_numeric(df['close'], errors='coerce')
            df = df.dropna(subset=['close'])
            df = df[df['close'] > 0]
        
        end_time = time.time()
        print(f"✅ 数据获取完成 - 耗时: {end_time - start_time:.2f}秒")
        if not df.empty:
            print(f"📈 获取 {len(df):,} 条记录，涉及 {df['ts_code'].nunique():,} 只股票")
        
        return df

    def calculate_zscore_parallel(self, df: pd.DataFrame) -> pd.DataFrame:
        """并行计算Z-score"""
        print("⚡ 开始并行Z-score计算...")
        start_time = time.time()
        
        if df.empty:
            return pd.DataFrame()
        
        # 按股票分组并准备数据
        stock_groups = []
        for ts_code, group in df.groupby('ts_code'):
            stock_groups.append((group, self.zscore_window))
        
        print(f"📊 准备处理 {len(stock_groups):,} 只股票")
        
        # 并行处理
        with mp.Pool(processes=self.n_processes) as pool:
            results = pool.map(process_stock_group, stock_groups)
        
        # 过滤有效结果
        valid_results = [r for r in results if r is not None]
        result_df = pd.DataFrame(valid_results) if valid_results else pd.DataFrame()
        
        end_time = time.time()
        print(f"✅ Z-score计算完成 - 耗时: {end_time - start_time:.2f}秒")
        if not result_df.empty:
            print(f"🎯 成功计算 {len(result_df):,} 只股票的Z-score")
        
        return result_df

    def analyze_stocks_parallel(self, zscore_df: pd.DataFrame) -> pd.DataFrame:
        """并行分析所有股票"""
        print(f"🔄 开始并行投资分析...")
        start_time = time.time()
        
        if zscore_df.empty:
            return pd.DataFrame()
        
        # 准备分析数据
        analysis_data = []
        for _, row in zscore_df.iterrows():
            analysis_data.append((row, self.optimal_threshold, self.c, self.L))
        
        # 并行分析
        with mp.Pool(processes=self.n_processes) as pool:
            results = pool.map(analyze_single_stock, analysis_data)
        
        result_df = pd.DataFrame(results)
        result_df = result_df.sort_values('net_value', ascending=False)
        
        end_time = time.time()
        print(f"✅ 投资分析完成 - 耗时: {end_time - start_time:.2f}秒")
        print(f"🏆 成功分析 {len(result_df):,} 只股票")
        
        return result_df

def monitor_performance():
    """监控系统性能"""
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    print(f"💻 系统状态 - CPU: {cpu_percent}%, 内存: {memory.percent}%")

def main():
    """
    主函数（M4 Pro高性能版 v2）
    """
    print("🚀 ====== 股票入场时机分析系统（M4 Pro高性能版 v2） ======")
    print(f"🖥️  检测到系统: {mp.cpu_count()}核心，内存: {psutil.virtual_memory().total // (1024**3)}GB")
    
    overall_start = time.time()
    
    # 初始化分析器
    analyzer = HighPerformanceEntryAnalyzer(
        r=0.05,
        c=0.02,
        L=3.0,
        zscore_window=252
    )
    
    try:
        # 步骤1: 批量获取数据
        all_data = analyzer.get_all_stock_data_optimized()
        
        if all_data.empty:
            print("❌ 没有有效的股票数据")
            return
        
        # 步骤2: 并行计算Z-score
        zscore_results = analyzer.calculate_zscore_parallel(all_data)
        
        if zscore_results.empty:
            print("❌ Z-score计算失败")
            return
        
        # 步骤3: 并行分析
        final_results = analyzer.analyze_stocks_parallel(zscore_results)
        
        if not final_results.empty:
            # 显示结果
            print("\n🏆 ====== 股票分析结果（前20名） ======")
            top_20 = final_results.head(20)
            
            # 格式化显示
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            print(top_20.to_string(index=False))
            
            # 显示一些统计信息
            print(f"\n📊 ====== 统计信息 ======")
            print(f"🟢 适合入场: {len(final_results[final_results['recommendation'] == '适合入场'])} 只")
            print(f"🟡 观望等待: {len(final_results[final_results['recommendation'] == '观望等待'])} 只")
            print(f"🔴 风险较高: {len(final_results[final_results['recommendation'] == '风险较高'])} 只")
            
            # 保存结果
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_file = f'high_performance_analysis_{timestamp}.csv'
            final_results.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            # 性能统计
            overall_end = time.time()
            total_time = overall_end - overall_start
            
            print(f"\n⚡ ====== 性能统计 ======")
            print(f"⏱️  总耗时: {total_time:.2f}秒")
            print(f"📈 分析股票数: {len(final_results):,}")
            print(f"🚀 平均每只股票耗时: {total_time/len(final_results)*1000:.2f}毫秒")
            print(f"🎯 处理速度: {len(final_results)/total_time:.0f} 股票/秒")
            print(f"💾 结果已保存到: {output_file}")
            
            # 系统性能监控
            monitor_performance()
            
        else:
            print("❌ 没有有效的分析结果")
            
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # M4 Pro优化：设置最佳的多进程启动方式
    mp.set_start_method('spawn', force=True)
    
    # 设置NumPy线程数（避免与多进程冲突）
    import os
    os.environ['OMP_NUM_THREADS'] = '1'
    os.environ['MKL_NUM_THREADS'] = '1'
    os.environ['NUMEXPR_NUM_THREADS'] = '1'
    
    main() 