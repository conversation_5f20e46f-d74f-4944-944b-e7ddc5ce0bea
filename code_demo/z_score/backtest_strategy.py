"""
@Author: JiangXin
@Date: 2025/1/16
@Description: 使用 backtrader 框架实现 Z-score 选股策略回测
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from entry_timing import EntryTimingAnalyzer, DB_CONFIG

class ZScoreData(bt.feeds.PandasData):
    """自定义数据源"""
    lines = ('zscore', 'net_value')  # 添加自定义数据列
    params = (
        ('zscore', -1),      # zscore 列的索引
        ('net_value', -1),   # net_value 列的索引
    )

class ZScoreStrategy(bt.Strategy):
    """Z-score选股策略"""
    
    params = (
        ('top_n', 5),           # 持有前5只股票
        ('position_size', 0.3),  # 单个持仓比例30%
        ('stop_loss', 0.15),    # 止损线15%
    )
    
    def __init__(self):
        """初始化策略"""
        self.orders = {}  # 跟踪订单
        self.positions = {}  # 跟踪持仓
        
        # 记录所有股票的数据
        self.stocks = {}
        for d in self.datas:
            self.stocks[d._name] = d
            
        # 计算净值排名
        self.rank = bt.indicators.Rank(self.data.net_value)
        
    def notify_order(self, order):
        """订单状态更新"""
        if order.status in [order.Submitted, order.Accepted]:
            return
            
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(f'买入 {order.data._name}: 价格={order.executed.price:.2f}, '
                        f'数量={order.executed.size}, 佣金={order.executed.comm:.2f}')
            else:
                self.log(f'卖出 {order.data._name}: 价格={order.executed.price:.2f}, '
                        f'数量={order.executed.size}, 佣金={order.executed.comm:.2f}')
                
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单取消/保证金不足/拒绝')
            
        # 移除已完成的订单
        if order.data._name in self.orders:
            self.orders.pop(order.data._name)
            
    def notify_trade(self, trade):
        """交易完成更新"""
        if not trade.isclosed:
            return
            
        self.log(f'交易利润: 毛利润={trade.pnl:.2f}, 净利润={trade.pnlcomm:.2f}')
        
    def log(self, txt, dt=None):
        """日志函数"""
        dt = dt or self.datas[0].datetime.date(0)
        print(f'{dt.isoformat()}: {txt}')
        
    def next(self):
        """
        主策略逻辑
        1. 计算所有股票的排名
        2. 选择排名靠前的股票
        3. 执行交易
        """
        # 检查是否有未完成的订单
        if self.orders:
            return
            
        # 获取当前日期的所有股票净值排名
        current_stocks = []
        for stock in self.stocks.values():
            if len(stock) > 0:  # 确保有数据
                current_stocks.append({
                    'name': stock._name,
                    'net_value': stock.net_value[0],
                    'data': stock
                })
                
        # 按净值排序
        ranked_stocks = sorted(current_stocks, 
                             key=lambda x: x['net_value'], 
                             reverse=True)
                             
        # 选择前N只股票
        target_stocks = ranked_stocks[:self.params.top_n]
        target_names = set(stock['name'] for stock in target_stocks)
        
        # 检查现有持仓
        for data in self.datas:
            pos = self.getposition(data)
            
            # 如果持有但不在目标中，则卖出
            if pos.size > 0 and data._name not in target_names:
                self.log(f'清仓 {data._name}')
                self.orders[data._name] = self.close(data)
                
            # 检查止损
            elif pos.size > 0:
                cost_price = pos.price
                current_price = data.close[0]
                if current_price / cost_price - 1 <= -self.params.stop_loss:
                    self.log(f'止损 {data._name}')
                    self.orders[data._name] = self.close(data)
        
        # 计算每只股票的目标金额
        portfolio_value = self.broker.getvalue()
        target_value = portfolio_value * self.params.position_size
        
        # 买入新的目标股票
        for stock in target_stocks:
            data = stock['data']
            pos = self.getposition(data)
            
            if pos.size == 0 and data._name not in self.orders:
                # 计算购买数量（考虑整手）
                price = data.close[0]
                if price > 0:
                    size = int(target_value / price / 100) * 100
                    if size > 0:
                        self.log(f'买入 {data._name}')
                        self.orders[data._name] = self.buy(data, size=size)

class ZScoreBacktest:
    """回测框架"""
    
    def __init__(self,
                 initial_capital: float = 100000,
                 backtest_days: int = 252,
                 commission_rate: float = 0.00025,
                 db_config: dict = DB_CONFIG):
        """
        初始化回测器
        
        参数:
            initial_capital: 初始资金
            backtest_days: 回测天数
            commission_rate: 佣金费率
            db_config: 数据库配置
        """
        self.initial_capital = initial_capital
        self.backtest_days = backtest_days
        self.commission_rate = commission_rate
        self.db_config = db_config
        
        # 创建数据库连接
        self.engine = create_engine(
            f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
            f"{db_config['host']}:{db_config['port']}/{db_config['database']}?"
            f"charset={db_config['charset']}"
        )
        
        # 创建分析器
        self.analyzer = EntryTimingAnalyzer(db_config=db_config)
        
    def prepare_data(self):
        """准备回测数据"""
        print("准备回测数据...")
        
        # 获取最新的交易日期
        end_date_query = text("""
            SELECT trade_date
            FROM stk_factor
            ORDER BY trade_date DESC
            LIMIT 1
        """)
        
        with self.engine.connect() as conn:
            end_date = pd.read_sql(end_date_query, conn).iloc[0]['trade_date']
            
        # 获取历史数据
        data_query = text("""
            WITH date_range AS (
                SELECT trade_date
                FROM stk_factor
                WHERE trade_date <= :end_date
                ORDER BY trade_date DESC
                LIMIT :days
            )
            SELECT 
                a.ts_code,
                a.trade_date,
                b.name,
                a.open_hfq as open,
                a.high_hfq as high,
                a.low_hfq as low,
                a.close_hfq as close,
                a.vol as volume,
                a.amount
            FROM stk_factor a
            JOIN date_range d ON a.trade_date = d.trade_date
            JOIN stock_basic b ON CAST(a.ts_code AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_general_ci = 
                                CAST(b.ts_code AS CHAR CHARACTER SET utf8mb4) COLLATE utf8mb4_general_ci
            WHERE b.list_status = 'L'
            AND a.ts_code NOT LIKE '%BJ'
        """)
        
        with self.engine.connect() as conn:
            df = pd.read_sql(data_query, conn, 
                           params={'days': self.backtest_days,
                                  'end_date': end_date})
                                  
        if df.empty:
            raise ValueError("没有获取到任何数据")
            
        print(f"获取到 {len(df['ts_code'].unique())} 只股票的数据")
        
        # 计算Z-score和净值
        print("计算技术指标...")
        results = []
        stock_count = len(df['ts_code'].unique())
        processed_count = 0
        
        for code in df['ts_code'].unique():
            stock_data = df[df['ts_code'] == code].copy()
            if len(stock_data) >= self.backtest_days * 0.8:  # 至少有80%的数据
                # 计算Z-score
                stock_data['zscore'] = (
                    (stock_data['close'] - stock_data['close'].mean()) / 
                    stock_data['close'].std()
                )
                # 计算净值
                stock_data['value'] = np.exp(-stock_data['zscore']**2/2)
                stock_data['cost'] = abs(stock_data['zscore']) * self.analyzer.c
                stock_data['net_value'] = stock_data['value'] - stock_data['cost']
                results.append(stock_data)
            
            processed_count += 1
            if processed_count % 100 == 0:
                print(f"处理进度: {processed_count}/{stock_count}")
                
        if not results:
            raise ValueError("没有找到符合条件的股票数据")
                
        # 合并所有数据
        df = pd.concat(results, ignore_index=True)
        print(f"处理完成，共有 {len(df['ts_code'].unique())} 只股票的有效数据")
        
        # 按股票分组，创建数据源
        data_feeds = {}
        for code in df['ts_code'].unique():
            stock_data = df[df['ts_code'] == code].copy()
            stock_data = stock_data.sort_values('trade_date')
            stock_data.set_index('trade_date', inplace=True)
            
            # 转换日期格式
            fromdate = pd.to_datetime(stock_data.index[0], format='%Y%m%d')
            todate = pd.to_datetime(stock_data.index[-1], format='%Y%m%d')
            
            data_feeds[code] = ZScoreData(
                dataname=stock_data,
                name=code,
                fromdate=fromdate,
                todate=todate,
                plot=False
            )
            
        return data_feeds
        
    def run(self):
        """运行回测"""
        # 创建回测引擎
        cerebro = bt.Cerebro()
        
        # 设置初始资金
        cerebro.broker.setcash(self.initial_capital)
        
        # 设置手续费
        cerebro.broker.setcommission(commission=self.commission_rate)
        
        # 准备数据
        data_feeds = self.prepare_data()
        for data in data_feeds.values():
            cerebro.adddata(data)
            
        # 添加策略
        cerebro.addstrategy(ZScoreStrategy)
        
        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        
        print("开始回测...")
        results = cerebro.run()
        strat = results[0]
        
        # 输出回测结果
        print("\n====== 回测报告 ======")
        print(f"初始资金: {self.initial_capital:,.2f}")
        print(f"最终资金: {cerebro.broker.getvalue():,.2f}")
        print(f"总收益率: {(cerebro.broker.getvalue() / self.initial_capital - 1) * 100:.2f}%")
        print(f"夏普比率: {strat.analyzers.sharpe.get_analysis()['sharperatio']:.2f}")
        print(f"最大回撤: {strat.analyzers.drawdown.get_analysis()['max']['drawdown']:.2f}%")
        
        # 交易统计
        trade_analysis = strat.analyzers.trades.get_analysis()
        print("\n交易统计:")
        print(f"总交易次数: {trade_analysis.total.total}")
        if trade_analysis.total.total > 0:
            print(f"盈利交易: {trade_analysis.won.total}")
            print(f"亏损交易: {trade_analysis.lost.total}")
            if trade_analysis.won.total > 0:
                print(f"平均盈利: {trade_analysis.won.pnl.average:.2f}")
            if trade_analysis.lost.total > 0:
                print(f"平均亏损: {trade_analysis.lost.pnl.average:.2f}")
            print(f"胜率: {trade_analysis.won.total / trade_analysis.total.total * 100:.2f}%")

def main():
    """主函数"""
    # 创建回测器
    backtester = ZScoreBacktest(
        initial_capital=100000,  # 初始资金10万
        backtest_days=252,       # 回测周期252天
        commission_rate=0.00025  # 佣金费率万分之2.5
    )
    
    # 运行回测
    backtester.run()

if __name__ == "__main__":
    main()
