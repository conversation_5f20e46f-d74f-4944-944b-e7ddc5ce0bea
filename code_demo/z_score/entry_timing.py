"""
@Author: <PERSON><PERSON>in
@Date: 2025/1/16 18:39
@Description: 基于Z-score的最优入场时机分析
该模块实现了一个基于Z-score的股票入场时机分析系统。
主要功能：
1. 计算股票的Z-score值
2. 基于价值函数和交易成本确定最优入场点
3. 生成交易建议
4. 批量分析股票并输出排名结果
"""
import numpy as np
import pandas as pd
from scipy.optimize import root_scalar
from sqlalchemy import create_engine, text

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'oyvla6qe',
    'database': 'test',
    'port': 3306,
    'charset': 'utf8mb4'
}

class EntryTimingAnalyzer:
    """
    股票入场时机分析器
    
    主要功能：
    1. 计算股票Z-score
    2. 评估最优入场时机
    3. 生成交易建议
    
    参数：
        r (float): 贴现率，默认0.05
        c (float): 交易成本，默认0.02（2%）
        L (float): Z-score上限阈值，默认3.0
        zscore_window (int): Z-score计算窗口，默认252天
        db_config (dict): 数据库配置信息
    """
    def __init__(self, 
                 r: float = 0.05,      # 贴现率
                 c: float = 0.02,      # 交易成本
                 L: float = 3.0,       # Z-score上限
                 zscore_window: int = 252,  # Z-score计算窗口
                 db_config: dict = DB_CONFIG):
        """
        初始化分析器
        
        参数：
            r (float): 贴现率
            c (float): 交易成本
            L (float): Z-score上限阈值
            zscore_window (int): Z-score计算窗口
            db_config (dict): 数据库配置信息
        """
        self.r = r
        self.c = c
        self.L = L
        self.zscore_window = zscore_window
        self.engine = self._create_engine(db_config)

    def _create_engine(self, db_config: dict):
        """
        创建数据库连接引擎
        
        参数：
            db_config (dict): 数据库配置信息
            
        返回：
            engine: 数据库连接引擎
        """
        return create_engine(
            f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
            f"{db_config['host']}:{db_config['port']}/{db_config['database']}?"
            f"charset={db_config['charset']}"
        )

    def get_stock_data(self, ts_code: str) -> pd.DataFrame:
        """
        获取股票历史数据
        
        参数：
            ts_code (str): 股票代码
            
        返回：
            pd.DataFrame: 包含交易日期和后复权收盘价的数据框
        """
        query = text("""
            SELECT 
                trade_date,
                close_hfq as close
            FROM stk_factor
            WHERE ts_code = :ts_code COLLATE utf8mb4_general_ci
            ORDER BY trade_date DESC
            LIMIT :days
        """)
        
        with self.engine.connect() as conn:
            df = pd.read_sql(query, conn, params={
                'ts_code': ts_code, 
                'days': self.zscore_window
            })
        
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)
        df.sort_index(inplace=True)
        return df

    def calculate_zscore(self, df: pd.DataFrame) -> float:
        """
        计算股票的Z-score值
        
        参数：
            df (pd.DataFrame): 股票历史数据
            
        返回：
            float: Z-score值，如果数据不足返回None
        """
        if len(df) < 20:  # 至少需要20个交易日的数据
            return None
        
        latest_price = df['close'].iloc[-1]
        mean_price = df['close'].mean()
        std_price = df['close'].std()
        
        return (latest_price - mean_price) / std_price if std_price != 0 else 0

    def value_function(self, zscore: float) -> float:
        """
        计算Z-score对应的价值
        使用正态分布密度函数形式，当Z-score接近0时价值最大
        
        参数：
            zscore (float): Z-score值
            
        返回：
            float: 价值评分
        """
        return np.exp(-zscore**2/2)

    def h_hat(self, zscore: float) -> float:
        """
        计算净收益（价值减去成本）
        
        参数：
            zscore (float): Z-score值
            
        返回：
            float: 净收益值
        """
        return self.value_function(zscore) - abs(zscore) * self.c

    def find_threshold(self, start: float = 0.1, max_iter: int = 100) -> float:
        """
        查找最优入场阈值
        使用迭代方法找到函数值最大的点
        
        参数：
            start (float): 起始点，默认0.1
            max_iter (int): 最大迭代次数，默认100
            
        返回：
            float: 最优入场阈值
        """
        x = start
        step = 0.1
        max_value = float('-inf')
        threshold = None
        
        for _ in range(max_iter):
            value = self.h_hat(x)
            if value > max_value:
                max_value = value
                threshold = x
            x += step
            if x > self.L:
                break
        
        return threshold if threshold is not None else self.L/2

    def analyze_entry_timing(self, ts_code: str, stock_name: str) -> dict:
        """
        分析股票入场时机
        
        参数：
            ts_code (str): 股票代码
            stock_name (str): 股票名称
            
        返回：
            dict: 分析结果字典，包含以下字段：
                - ts_code: 股票代码
                - name: 股票名称
                - latest_date: 最新交易日期
                - latest_price: 最新价格
                - zscore: Z-score值
                - threshold: 入场阈值
                - net_value: 净价值
                - recommendation: 交易建议
        """
        df = self.get_stock_data(ts_code)
        current_zscore = self.calculate_zscore(df)
        
        if current_zscore is None:
            return None
        
        try:
            entry_threshold = self.find_threshold()
            current_value = self.value_function(current_zscore)
            current_cost = abs(current_zscore) * self.c
            current_net_value = current_value - current_cost
            latest_price = df['close'].iloc[-1]
            latest_date = df.index[-1].strftime('%Y-%m-%d')
            
            return {
                'ts_code': ts_code,
                'name': stock_name,
                'latest_date': latest_date,
                'latest_price': round(latest_price, 2),
                'zscore': round(current_zscore, 4),
                'threshold': round(entry_threshold, 4),
                'net_value': round(current_net_value, 4),
                'recommendation': self._generate_recommendation(
                    current_zscore, 
                    entry_threshold
                )
            }
            
        except Exception as e:
            print(f"分析错误 {ts_code}: {e}")
            return None

    def _generate_recommendation(self, current_zscore: float, entry_threshold: float) -> str:
        """
        生成交易建议
        
        参数：
            current_zscore (float): 当前Z-score值
            entry_threshold (float): 入场阈值
            
        返回：
            str: 交易建议
        """
        if entry_threshold is None:
            return "无法评估"
        
        abs_zscore = abs(current_zscore)
        if abs_zscore <= entry_threshold:
            return "适合入场"
        elif abs_zscore <= self.L:
            return "观望等待"
        else:
            return "风险较高"

def main():
    """
    主函数
    执行股票分析并输出结果
    """
    # 初始化分析器
    analyzer = EntryTimingAnalyzer(
        r=0.05,      # 贴现率
        c=0.02,      # 交易成本
        L=3.0,       # Z-score上限
        zscore_window=252  # Z-score计算窗口
    )
    
    # 获取要分析的股票代码
    query = text("""
        SELECT DISTINCT ts_code, name
        FROM stock_basic
        WHERE list_status = 'L'
        AND ts_code NOT LIKE '%BJ'   # 排除北交所
        ORDER BY ts_code
    """)
    
    with analyzer.engine.connect() as conn:
        stocks = pd.read_sql(query, conn)
    
    # 分析结果列表
    results = []
    total_stocks = len(stocks)
    
    # 分析每只股票
    for idx, stock in stocks.iterrows():
        print(f"\r分析进度: {idx+1}/{total_stocks}", end="")
        result = analyzer.analyze_entry_timing(stock['ts_code'], stock['name'])
        if result:
            results.append(result)
    
    # 转换为DataFrame并排序
    df_results = pd.DataFrame(results)
    if not df_results.empty:
        df_results = df_results.sort_values('net_value', ascending=False)
        
        # 显示结果
        print("\n\n====== 股票分析结果 ======")
        print(df_results.to_string(index=False))
        
        # 保存到CSV
        output_file = 'stock_analysis_results.csv'
        df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到: {output_file}")
    else:
        print("\n没有有效的分析结果")

if __name__ == "__main__":
    main()
