"""
@Author: Jiang<PERSON>in
@Date: 2025/1/16 19:00
@Description: Z-score最优入场阈值分析可视化
展示为什么最优阈值是0.01的数学原理
"""
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['STHeiti', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def analyze_optimal_threshold():
    """分析最优入场阈值的计算逻辑"""
    
    # 参数设置
    c = 0.02  # 交易成本 2%
    L = 3.0   # Z-score上限
    
    # 生成Z-score范围
    z_values = np.arange(-3, 3.01, 0.001)
    abs_z_values = np.arange(0.001, 3.01, 0.001)
    
    # 计算各个函数值
    value_function = np.exp(-z_values**2/2)  # 价值函数
    cost_function = np.abs(z_values) * c     # 成本函数
    net_value = value_function - cost_function  # 净价值
    
    # 计算最优阈值
    abs_value_function = np.exp(-abs_z_values**2/2)
    abs_cost_function = abs_z_values * c
    abs_net_value = abs_value_function - abs_cost_function
    optimal_idx = np.argmax(abs_net_value)
    optimal_threshold = abs_z_values[optimal_idx]
    
    print(f"最优入场阈值: {optimal_threshold:.4f}")
    print(f"最大净价值: {abs_net_value[optimal_idx]:.6f}")
    
    # 创建可视化
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 图1: 价值函数 V(z)
    ax1.plot(z_values, value_function, 'b-', linewidth=2, label='价值函数 V(z) = e^(-z²/2)')
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.3)
    ax1.axvline(x=0, color='k', linestyle='--', alpha=0.3)
    ax1.set_xlabel('Z-score')
    ax1.set_ylabel('价值')
    ax1.set_title('价值函数：当Z-score接近0时价值最高')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 图2: 成本函数 C(z)
    ax2.plot(z_values, cost_function, 'r-', linewidth=2, label=f'成本函数 C(z) = |z| × {c}')
    ax2.axhline(y=0, color='k', linestyle='--', alpha=0.3)
    ax2.axvline(x=0, color='k', linestyle='--', alpha=0.3)
    ax2.set_xlabel('Z-score')
    ax2.set_ylabel('成本')
    ax2.set_title('成本函数：Z-score越极端，成本越高')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 图3: 净价值函数 H(z) = V(z) - C(z)
    ax3.plot(z_values, net_value, 'g-', linewidth=2, label='净价值 H(z) = V(z) - C(z)')
    ax3.plot(z_values, value_function, 'b--', alpha=0.5, label='价值函数')
    ax3.plot(z_values, cost_function, 'r--', alpha=0.5, label='成本函数')
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.3)
    ax3.axvline(x=0, color='k', linestyle='--', alpha=0.3)
    ax3.axvline(x=optimal_threshold, color='orange', linestyle=':', linewidth=2, label=f'最优阈值 = {optimal_threshold:.3f}')
    ax3.axvline(x=-optimal_threshold, color='orange', linestyle=':', linewidth=2)
    ax3.set_xlabel('Z-score')
    ax3.set_ylabel('净价值')
    ax3.set_title('净价值函数：最优入场区间')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 图4: 最优阈值的确定
    ax4.plot(abs_z_values, abs_net_value, 'purple', linewidth=2, label='净价值函数 |z|')
    ax4.axvline(x=optimal_threshold, color='orange', linestyle='--', linewidth=2, 
                label=f'最优阈值 = {optimal_threshold:.3f}')
    ax4.scatter([optimal_threshold], [abs_net_value[optimal_idx]], 
                color='red', s=100, zorder=5, label=f'最大值点')
    ax4.set_xlabel('|Z-score|')
    ax4.set_ylabel('净价值')
    ax4.set_title('最优阈值计算：净价值最大化')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('threshold_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return optimal_threshold, abs_net_value[optimal_idx]

def analyze_entry_logic():
    """分析入场逻辑的具体应用"""
    
    # 从你的结果表格中取一些实际例子
    examples = [
        {'name': '华大基因', 'zscore': 0.0020, 'net_value': 1.0000},
        {'name': '康希诺', 'zscore': 0.0003, 'net_value': 1.0000},
        {'name': '华海清科', 'zscore': -0.0015, 'net_value': 1.0000},
        {'name': '金圆股份', 'zscore': 0.0054, 'net_value': 0.9999},
        {'name': '朗玛信息', 'zscore': 0.0047, 'net_value': 0.9999}
    ]
    
    print("\n" + "="*60)
    print("实际案例分析：为什么这些股票'适合入场'？")
    print("="*60)
    
    optimal_threshold = 0.01
    c = 0.02
    
    for example in examples:
        zscore = example['zscore']
        abs_zscore = abs(zscore)
        
        # 计算价值和成本
        value = np.exp(-zscore**2/2)
        cost = abs_zscore * c
        net_value = value - cost
        
        print(f"\n股票：{example['name']}")
        print(f"Z-score: {zscore:.4f}")
        print(f"|Z-score|: {abs_zscore:.4f}")
        print(f"价值函数 V(z): {value:.6f}")
        print(f"交易成本 C(z): {cost:.6f}")
        print(f"净价值 H(z): {net_value:.6f}")
        print(f"是否 ≤ 阈值({optimal_threshold}): {'是' if abs_zscore <= optimal_threshold else '否'}")
        print(f"投资建议: {'适合入场' if abs_zscore <= optimal_threshold else '观望等待'}")

def create_summary_table():
    """创建判断逻辑总结表"""
    
    print("\n" + "="*80)
    print("投资决策逻辑总结")
    print("="*80)
    
    decision_table = pd.DataFrame({
        '判断条件': [
            '|Z-score| ≤ 0.01',
            '0.01 < |Z-score| ≤ 3.0',
            '|Z-score| > 3.0'
        ],
        '投资建议': [
            '适合入场',
            '观望等待', 
            '风险较高'
        ],
        '理论依据': [
            '接近历史均值，风险收益比最佳',
            '偏离均值但在可接受范围内',
            '严重偏离，市场冲击成本过高'
        ],
        '预期收益': [
            '最高（净价值接近1.0）',
            '中等（净价值0.8-0.99）',
            '较低或负值'
        ]
    })
    
    print(decision_table.to_string(index=False))

def main():
    """主函数"""
    print("Z-score最优入场阈值分析")
    print("="*50)
    
    # 分析最优阈值
    optimal_threshold, max_net_value = analyze_optimal_threshold()
    
    # 分析具体案例
    analyze_entry_logic()
    
    # 创建总结表
    create_summary_table()
    
    print(f"\n总结：")
    print(f"• 最优入场阈值: {optimal_threshold:.4f}")
    print(f"• 最大净价值: {max_net_value:.6f}")
    print(f"• 核心原理: 在均值回归理论下，寻找价值最大化的入场时机")
    print(f"• 实际应用: 选择Z-score接近0（即价格接近历史均值）的股票")

if __name__ == "__main__":
    main() 