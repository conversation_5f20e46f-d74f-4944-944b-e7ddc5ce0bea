"""
@Author: JiangXin
@Date: 2025/1/16 19:09
@Description: Z-score策略回测框架（性能优化版）
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from entry_timing import EntryTimingAnalyzer, DB_CONFIG
from typing import Dict, List, Tuple

class ZScoreBacktester:
    def __init__(self,
                 initial_capital: float = 100000,
                 position_size: float = 0.3,
                 stop_loss: float = 0.15,
                 backtest_days: int = 252,
                 top_n: int = 5,
                 commission_rate: float = 0.00025,
                 stamp_duty: float = 0.001,
                 db_config: dict = DB_CONFIG):
        
        self.initial_capital = initial_capital
        self.position_size = position_size
        self.stop_loss = stop_loss
        self.backtest_days = backtest_days
        self.top_n = top_n
        self.commission_rate = commission_rate
        self.stamp_duty = stamp_duty
        
        self.analyzer = EntryTimingAnalyzer(db_config=db_config)
        self.engine = create_engine(
            f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
            f"{db_config['host']}:{db_config['port']}/{db_config['database']}?"
            f"charset={db_config['charset']}"
        )
        
        # 初始化状态
        self.current_positions = {}
        self.cash = initial_capital
        self.trade_history = []
        self.daily_stats = []
        
        # 数据缓存
        self.stock_data_cache = None
        self.trading_dates = None
        self.stock_list = None

    def _load_all_data(self):
        """一次性加载所有需要的数据"""
        print("加载历史数据...")
        
        # 获取交易日期
        date_query = text("""
            SELECT DISTINCT trade_date
            FROM stk_factor
            ORDER BY trade_date DESC
            LIMIT :days
        """)
        
        # 获取股票列表
        stock_query = text("""
            SELECT DISTINCT ts_code, name
            FROM stock_basic
            WHERE list_status = 'L'
            AND ts_code NOT LIKE '%BJ'
        """)
        
        # 获取历史数据
        data_query = text("""
            SELECT 
                a.ts_code,
                a.trade_date,
                a.open,
                a.close,
                a.high,
                a.low,
                a.open_hfq as open_adjusted,
                a.close_hfq as close_adjusted,
                a.vol,
                a.amount
            FROM stk_factor a
            WHERE a.trade_date >= (
                SELECT trade_date 
                FROM stk_factor 
                ORDER BY trade_date DESC 
                LIMIT :days,1
            )
        """)
        
        with self.engine.connect() as conn:
            # 加载日期
            self.trading_dates = pd.read_sql(date_query, conn, 
                                           params={'days': self.backtest_days})['trade_date'].tolist()
            self.trading_dates.reverse()
            
            # 加载股票列表
            self.stock_list = pd.read_sql(stock_query, conn)
            
            # 加载历史数据
            print("加载股票数据...")
            stock_data = pd.read_sql(data_query, conn, 
                                   params={'days': self.backtest_days})
            
            # 数据透视
            self.stock_data_cache = stock_data.set_index(['trade_date', 'ts_code']).sort_index()
            
        print(f"数据加载完成，共 {len(self.trading_dates)} 个交易日，{len(self.stock_list)} 只股票")

    def get_daily_data(self, date: str) -> pd.DataFrame:
        """从缓存中获取每日数据"""
        try:
            daily_data = self.stock_data_cache.loc[date].reset_index()
            daily_data = daily_data.merge(self.stock_list, on='ts_code', how='left')
            return daily_data
        except KeyError:
            return pd.DataFrame()

    def calculate_zscore_batch(self, date: str, window: int = 252) -> pd.DataFrame:
        """批量计算Z-score"""
        # 获取当前日期的索引
        try:
            end_idx = self.trading_dates.index(date)
        except ValueError:
            print(f"日期{date}不在交易日历中")
            return pd.DataFrame()
            
        # 确保有足够的历史数据
        if end_idx < window:
            print(f"历史数据不足{window}天")
            return pd.DataFrame()
        
        # 获取时间窗口
        start_date = self.trading_dates[end_idx - window + 1]
        
        # 获取时间窗口内的数据
        mask = (self.stock_data_cache.index.get_level_values(0) >= start_date) & \
               (self.stock_data_cache.index.get_level_values(0) <= date)
        window_data = self.stock_data_cache[mask]
        
        # 检查是否有足够的数据
        stock_counts = window_data.groupby(level=1).size()
        valid_stocks = stock_counts[stock_counts >= window * 0.8].index  # 至少有80%的数据
        
        if len(valid_stocks) == 0:
            print(f"没有找到有效的股票数据")
            return pd.DataFrame()
        
        # 只处理有效的股票
        window_data = window_data[window_data.index.get_level_values(1).isin(valid_stocks)]
        
        # 计算统计值
        stats = window_data.groupby(level=1).agg({
            'close_adjusted': ['mean', 'std', 'last', 'count']
        })
        stats.columns = ['mean', 'std', 'last', 'count']
        
        # 计算Z-score
        stats['zscore'] = (stats['last'] - stats['mean']) / stats['std'].replace(0, np.inf)
        
        # 过滤无效的Z-score
        stats = stats[stats['zscore'].notna() & (stats['std'] > 0)]
        
        if stats.empty:
            print(f"没有有效的Z-score数据")
            return pd.DataFrame()
        
        # 计算价值函数
        stats['value'] = np.exp(-stats['zscore']**2/2)
        stats['cost'] = abs(stats['zscore']) * self.analyzer.c
        stats['net_value'] = stats['value'] - stats['cost']
        
        # 重置索引并返回
        return stats.reset_index()

    def select_stocks(self, date: str) -> pd.DataFrame:
        """选择股票（优化版）"""
        # 获取当日数据
        daily_data = self.get_daily_data(date)
        if daily_data.empty:
            print(f"日期{date}没有交易数据")
            return pd.DataFrame()
            
        # 过滤掉停牌的股票（成交量为0）
        daily_data = daily_data[daily_data['vol'] > 0]
        if daily_data.empty:
            print(f"日期{date}所有股票都停牌")
            return pd.DataFrame()
        
        # 批量计算Z-score和评分
        scores = self.calculate_zscore_batch(date)
        if scores.empty:
            return pd.DataFrame()
        
        # 合并数据
        result = daily_data.merge(scores, on='ts_code', how='inner')
        if result.empty:
            print(f"日期{date}没有符合条件的股票")
            return pd.DataFrame()
            
        # 过滤条件：
        # 1. Z-score不能为nan
        # 2. 收盘价和开盘价不能为0
        # 3. 成交量不能为0
        result = result[
            result['zscore'].notna() & 
            (result['close_adjusted'] > 0) & 
            (result['open_adjusted'] > 0) &
            (result['vol'] > 0) &
            (result['std'] > 0)  # 确保波动率大于0
        ]
        
        if result.empty:
            print(f"日期{date}没有满足交易条件的股票")
            return pd.DataFrame()
            
        # 打印调试信息
        print(f"\n日期{date}选股结果:")
        print(f"符合条件的股票数: {len(result)}")
        
        # 选择前N只股票
        selected = result.nlargest(self.top_n, 'net_value')
        
        # 打印选中的股票
        print("\n选中的股票:")
        for _, row in selected.iterrows():
            print(f"{row['name']}({row['ts_code']}): Z-score={row['zscore']:.2f}, "
                  f"净值={row['net_value']:.4f}")
        
        return selected

    def execute_trades(self, date: str, selected_stocks: pd.DataFrame):
        """
        执行交易
        
        参数：
            date: 交易日期
            selected_stocks: 选中的股票
        """
        if selected_stocks.empty:
            return
            
        # 检查止损
        self._check_stop_loss(date)
        
        # 获取当前持仓的股票代码
        current_codes = set(self.current_positions.keys())
        new_codes = set(selected_stocks['ts_code'])
        
        # 需要卖出的股票
        to_sell = current_codes - new_codes
        for code in to_sell:
            self._sell_stock(date, code)
        
        # 需要买入的股票
        to_buy = new_codes - current_codes
        for code in to_buy:
            stock_data = selected_stocks[selected_stocks['ts_code'] == code].iloc[0]
            self._buy_stock(date, stock_data)

    def _check_stop_loss(self, date: str):
        """检查止损"""
        if not self.current_positions:
            return
            
        daily_data = self.get_daily_data(date)
        for code in list(self.current_positions.keys()):
            position = self.current_positions[code]
            current_data = daily_data[daily_data['ts_code'] == code]
            
            if current_data.empty:
                continue
                
            current_price = current_data.iloc[0]['open_adjusted']
            cost_price = position['cost_price']
            
            if current_price / cost_price - 1 <= -self.stop_loss:
                self._sell_stock(date, code, stop_loss=True)

    def _buy_stock(self, date: str, stock_data: pd.Series):
        """买入股票"""
        if 'open_adjusted' not in stock_data:
            return
            
        price = stock_data['open_adjusted']
        available_cash = min(self.cash, self.initial_capital * self.position_size)
        shares = self.calculate_tradable_amount(price, available_cash)
        
        if shares == 0:
            return
            
        cost = shares * price
        commission = cost * self.commission_rate
        total_cost = cost + commission
        
        if total_cost <= self.cash:
            self.current_positions[stock_data['ts_code']] = {
                'shares': shares,
                'cost_price': price,
                'name': stock_data['name']
            }
            self.cash -= total_cost
            
            # 记录交易
            self.trade_history.append({
                'date': date,
                'code': stock_data['ts_code'],
                'name': stock_data['name'],
                'action': 'buy',
                'shares': shares,
                'price': price,
                'cost': total_cost,
                'commission': commission
            })

    def _sell_stock(self, date: str, code: str, stop_loss: bool = False):
        """卖出股票"""
        if code not in self.current_positions:
            return
            
        position = self.current_positions[code]
        daily_data = self.get_daily_data(date)
        stock_data = daily_data[daily_data['ts_code'] == code]
        
        if stock_data.empty:
            return
            
        price = stock_data.iloc[0]['open_adjusted']
        shares = position['shares']
        value = shares * price
        commission = value * self.commission_rate
        stamp_duty = value * self.stamp_duty
        total_value = value - commission - stamp_duty
        
        self.cash += total_value
        
        # 记录交易
        self.trade_history.append({
            'date': date,
            'code': code,
            'name': position['name'],
            'action': 'sell',
            'shares': shares,
            'price': price,
            'value': total_value,
            'commission': commission,
            'stamp_duty': stamp_duty,
            'stop_loss': stop_loss
        })
        
        del self.current_positions[code]

    def calculate_daily_stats(self, date: str):
        """计算每日统计数据"""
        total_value = self.cash
        positions_value = 0
        
        if self.current_positions:
            daily_data = self.get_daily_data(date)
            for code, position in self.current_positions.items():
                stock_data = daily_data[daily_data['ts_code'] == code]
                if not stock_data.empty:
                    price = stock_data.iloc[0]['close_adjusted']
                    value = position['shares'] * price
                    positions_value += value
                    
        total_value += positions_value
        
        self.daily_stats.append({
            'date': date,
            'cash': self.cash,
            'positions_value': positions_value,
            'total_value': total_value,
            'return_rate': (total_value / self.initial_capital - 1) * 100,
            'position_count': len(self.current_positions)
        })

    def calculate_tradable_amount(self, price: float, cash: float) -> int:
        """
        计算可交易的股数（考虑100股的整数倍）
        """
        max_shares = int(cash / (price * (1 + self.commission_rate)) / 100) * 100
        return max_shares

    def run_backtest(self):
        """运行回测（优化版）"""
        print("开始回测...")
        
        # 加载所有数据
        self._load_all_data()
        
        total_days = len(self.trading_dates)
        
        # 逐日回测
        for i, date in enumerate(self.trading_dates):
            print(f"\r回测进度: {i+1}/{total_days}", end="")
            
            # 选股
            selected_stocks = self.select_stocks(date)
            
            # 执行交易
            if not selected_stocks.empty:
                self.execute_trades(date, selected_stocks)
            
            # 计算每日统计
            self.calculate_daily_stats(date)
        
        print("\n回测完成!")
        self.generate_report()

    def _calculate_max_drawdown(self, values: pd.Series) -> float:
        """计算最大回撤"""
        cummax = values.cummax()
        drawdown = (values - cummax) / cummax * 100
        return abs(drawdown.min()) if not drawdown.empty else 0.0

    def _calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """计算夏普比率"""
        if len(returns) < 2:  # 至少需要两个数据点
            return 0.0
        daily_returns = returns.pct_change().dropna()
        if len(daily_returns) == 0:
            return 0.0
        return np.sqrt(252) * daily_returns.mean() / (daily_returns.std() + 1e-6)  # 添加小量防止除零

    def generate_report(self):
        """生成回测报告"""
        # 转换交易历史为DataFrame
        trades_df = pd.DataFrame(self.trade_history)
        stats_df = pd.DataFrame(self.daily_stats)
        
        # 计算关键指标
        if not stats_df.empty:
            total_return = stats_df['return_rate'].iloc[-1]
            max_drawdown = self._calculate_max_drawdown(stats_df['total_value'])
            annual_return = total_return * (252 / len(stats_df))
            sharpe_ratio = self._calculate_sharpe_ratio(stats_df['return_rate'])
            
            print("\n====== 回测报告 ======")
            print(f"总收益率: {total_return:.2f}%")
            print(f"年化收益率: {annual_return:.2f}%")
            print(f"最大回撤: {max_drawdown:.2f}%")
            print(f"夏普比率: {sharpe_ratio:.2f}")
            print(f"总交易次数: {len(trades_df)}")
            
            # 计算持仓信息
            avg_position_count = stats_df['position_count'].mean()
            max_position_count = stats_df['position_count'].max()
            print(f"平均持仓数: {avg_position_count:.1f}")
            print(f"最大持仓数: {max_position_count}")
            
            # 保存交易记录
            trades_df.to_csv('backtest_trades.csv', index=False, encoding='utf-8-sig')
            stats_df.to_csv('backtest_stats.csv', index=False, encoding='utf-8-sig')
            print("\n交易记录已保存到: backtest_trades.csv")
            print("每日统计数据已保存到: backtest_stats.csv")
            
            # 输出最后一天的持仓情况
            if self.current_positions:
                print("\n最终持仓:")
                for code, pos in self.current_positions.items():
                    print(f"{pos['name']}({code}): {pos['shares']}股, 成本价: {pos['cost_price']:.2f}")
        else:
            print("\n没有有效的回测数据")

def main():
    """主函数"""
    # 创建回测器
    backtester = ZScoreBacktester(
        initial_capital=100000,  # 初始资金10万
        position_size=0.3,       # 单个持仓比例30%
        stop_loss=0.15,         # 止损线15%
        backtest_days=252,      # 回测天数
        top_n=5                 # 选择前5只股票
    )
    
    # 运行回测
    backtester.run_backtest()

if __name__ == "__main__":
    main()
