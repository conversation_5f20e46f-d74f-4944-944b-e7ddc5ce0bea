"""
@Author: JiangXin
@Description: 投资组合优化API接口
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, validator
from typing import Optional, Tuple
from optimation import PortfolioOptimizer
from datetime import datetime

app = FastAPI(
    title="投资组合优化API",
    description="提供投资组合优化的HTTP接口服务"
)
origins = [
    "http://localhost:5173",  # 允许你的前端域名
    # 如果还有其他允许的域名，可以继续添加到这里
]
# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)

class OptimizationRequest(BaseModel):
    start_date: str
    end_date: str
    sample_size: Optional[int] = 20
    risk_free_rate: Optional[float] = 0.03
    weight_bounds: Optional[list] = [0, 0.35]  # 修改为list类型

    @validator('start_date', 'end_date')
    def validate_date(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('日期格式必须为YYYY-MM-DD')
    
    @validator('weight_bounds')
    def validate_weight_bounds(cls, v):
        if not isinstance(v, list) or len(v) != 2:
            raise ValueError('weight_bounds必须是包含两个元素的列表')
        return tuple(v)  # 转换为tuple类型

@app.post("/optimize")
async def optimize_portfolio(request: OptimizationRequest):
    try:
        print("接收到的请求参数：", request.dict())

        # 检查日期范围
        start_date = datetime.strptime(request.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(request.end_date, '%Y-%m-%d')

        if start_date >= end_date:
            raise ValueError("开始日期必须早于结束日期")

        # 检查日期范围是否过短
        date_diff = (end_date - start_date).days
        if date_diff < 10:  # 至少需要10个交易日
            raise ValueError("日期范围太短，至少需要10个交易日的数据")

        if start_date > datetime.now() or end_date > datetime.now():
            raise ValueError("日期不能超过当前日期")

        # 检查其他参数
        if request.sample_size < 1:
            raise ValueError("样本数量必须大于0")

        if request.risk_free_rate < 0:
            raise ValueError("无风险利率不能为负")

        weight_bounds = tuple(request.weight_bounds)
        if weight_bounds[0] < 0 or weight_bounds[1] > 1 or weight_bounds[0] >= weight_bounds[1]:
            raise ValueError("权重范围无效")

        optimizer = PortfolioOptimizer(
            start_date=request.start_date,
            end_date=request.end_date,
            sample_size=request.sample_size,
            risk_free_rate=request.risk_free_rate,
            weight_bounds=weight_bounds
        )

        print("开始获取价格数据...")
        price_data = optimizer.fetch_price_data()
        print(f"价格数据获取成功，形状: {price_data.shape}")
        
        print("开始优化组合...")
        result = optimizer.optimize_portfolio()
        print(f"优化完成{result}")

        
        # 确保返回的是可JSON序列化的数据
        return {
            "status": "success",
            "data": result,
            "message": "投资组合优化完成"
        }

    except ValueError as e:
        print(f"参数验证错误: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "error_type": "ValidationError"
        }
    except Exception as e:
        print(f"服务器错误: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "error_type": "ServerError"
        }

@app.get("/")
async def root():
    """
    API根路径，返回使用说明
    """
    return {
        "message": "欢迎使用投资组合优化API",
        "endpoints": {
            "/optimize": "POST - 执行投资组合优化",
            "/": "GET - 获取API说明"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8010)
