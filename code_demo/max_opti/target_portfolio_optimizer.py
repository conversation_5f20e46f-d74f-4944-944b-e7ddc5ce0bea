"""
@Author: JiangXin
@Date: 2025/3/10
@Description: 针对目标股票池计算最优组合权重
"""

import numpy as np
import pandas as pd
from pypfopt import EfficientFrontier, risk_models, expected_returns
from sqlalchemy import create_engine

# 从本地配置导入数据库连接
from get_data.db_config import DB_URL

class TargetPortfolioOptimizer:
    """
    目标投资组合优化器
    用于计算指定股票池的最优投资组合权重
    """
    
    def __init__(self, start_date, end_date, stock_pool, risk_free_rate=0.03, weight_bounds=(0, 0.35)):
        """
        初始化投资组合优化器
        
        参数：
            start_date: str 起始日期 'YYYY-MM-DD'
            end_date: str 结束日期 'YYYY-MM-DD'
            stock_pool: list 目标股票池列表
            risk_free_rate: float 无风险利率
            weight_bounds: tuple 投资权重范围，形如(最小权重, 最大权重)
        """
        self.start_date = start_date
        self.end_date = end_date
        self.stock_pool = stock_pool
        self.risk_free_rate = risk_free_rate
        self.weight_bounds = weight_bounds
        self.price_data = None
        self.optimization_result = None
        
    def fetch_price_data(self):
        """
        从stk_factor表获取目标股票池的后复权价格
        """
        engine = create_engine(DB_URL)
        
        try:
            # 获取目标股票池的后复权价格
            stock_codes_str = ','.join([f"'{code}'" for code in self.stock_pool])
            price_query = f"""
            SELECT trade_date, ts_code, close_hfq 
            FROM stk_factor 
            WHERE ts_code IN ({stock_codes_str})
              AND trade_date BETWEEN '{self.start_date}' AND '{self.end_date}'
              AND close_hfq IS NOT NULL
            ORDER BY trade_date
            """
            
            print(f"执行价格查询SQL: {price_query}")
            raw_data = pd.read_sql(price_query, engine)
            
            if raw_data.empty:
                raise ValueError(f"在{self.start_date}到{self.end_date}期间未获取到价格数据")
            
            # 检查数据完整性
            data_completeness = raw_data.groupby('ts_code').size()
            expected_days = len(pd.date_range(self.start_date, self.end_date, freq='B'))
            incomplete_stocks = data_completeness[data_completeness < expected_days * 0.95].index.tolist()
            
            if incomplete_stocks:
                print(f"警告：以下股票数据不完整（少于95%的交易日）：{incomplete_stocks}")
            
            # 转换为宽表格式并处理缺失值
            self.price_data = raw_data.pivot(index='trade_date', columns='ts_code', values='close_hfq')
            self.price_data = self.price_data.ffill()  # 向前填充缺失值
            
            print(f"获取到的数据形状: {self.price_data.shape}")
            return self.price_data
            
        except Exception as e:
            print(f"错误详情: {str(e)}")
            raise Exception(f"获取数据失败: {str(e)}")
    
    def optimize_portfolio(self):
        """
        执行投资组合优化
        
        返回：
            dict 包含最优权重、预期收益和风险指标
        """
        if self.price_data is None:
            self.fetch_price_data()
            
        # 数据验证
        if len(self.price_data) < 10:
            raise ValueError(f"数据量不足，至少需要10个交易日的数据，当前只有{len(self.price_data)}个交易日")
            
        # 计算预期收益和风险
        try:
            mu = expected_returns.mean_historical_return(self.price_data)
            S = risk_models.sample_cov(self.price_data)
            
            # 创建优化器实例
            ef = EfficientFrontier(mu, S, weight_bounds=self.weight_bounds)
            
            try:
                # 最大化夏普比率
                weights = ef.max_sharpe(risk_free_rate=self.risk_free_rate)
                cleaned_weights = ef.clean_weights()
                
                # 获取优化结果
                portfolio_performance = ef.portfolio_performance(risk_free_rate=self.risk_free_rate)
                expected_return, volatility, sharpe_ratio = portfolio_performance
                
                # 转换权重为普通的Python字典
                weights_dict = {k: float(v) for k, v in cleaned_weights.items()}
                
                # 构建返回结果
                result = {
                    "weights": weights_dict,
                    "metrics": {
                        "expected_annual_return": float(expected_return),
                        "annual_volatility": float(volatility),
                        "sharpe_ratio": float(sharpe_ratio)
                    },
                    "parameters": {
                        "stock_pool_size": len(self.stock_pool),
                        "weight_bounds": [float(self.weight_bounds[0]), float(self.weight_bounds[1])],
                        "price_data_shape": [int(x) for x in self.price_data.shape],
                        "missing_values": int(self.price_data.isna().sum().sum())
                    }
                }
                
                self.optimization_result = result
                return result
                
            except Exception as e:
                raise Exception(f"优化失败: {str(e)}")
                
        except Exception as e:
            raise Exception(f"计算收益和风险失败: {str(e)}")
    
    def get_results(self):
        """
        获取优化结果
        """
        if self.optimization_result is None:
            return self.optimize_portfolio()
        return self.optimization_result

if __name__ == "__main__":
    # 目标股票池
    target_stocks = [
        '000506.SZ', '300056.SZ', '301018.SZ', '300681.SZ', '300145.SZ',
        '603322.SH', '300098.SZ', '603319.SH', '301220.SZ', '300680.SZ',
        '300980.SZ', '301550.SZ', '301486.SZ', '002630.SZ', '300520.SZ',
        '002575.SZ', '688048.SH', '002965.SZ'
    ]
    
    # 创建优化器实例
    optimizer = TargetPortfolioOptimizer(
        start_date='2024-12-01',  # 使用YYYY-MM-DD格式
        end_date='2025-03-10',    # 使用YYYY-MM-DD格式
        stock_pool=target_stocks,
        risk_free_rate=0.03,
        weight_bounds=(0, 0.35)    # 单只股票最大权重不超过35%
    )
    
    try:
        # 执行优化
        result = optimizer.optimize_portfolio()
        
        # 打印结果
        print("\n=== 投资组合优化结果 ===")
        print("\n最优权重分配：")
        for stock, weight in result['weights'].items():
            if weight > 0.01:  # 只显示权重大于1%的股票
                print(f"{stock}: {weight:.2%}")
        
        print("\n组合指标：")
        metrics = result['metrics']
        print(f"预期年化收益率: {metrics['expected_annual_return']:.2%}")
        print(f"年化波动率: {metrics['annual_volatility']:.2%}")
        print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
        
    except Exception as e:
        print(f"优化过程中出现错误: {str(e)}")
