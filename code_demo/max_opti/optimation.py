"""
@Author: Jiang<PERSON>in
@Date: 2025/2/11 19:51
@Description: 随机抽取一定周期内的股票，计算最优组合权重
"""
"""
投资组合优化模块
实现马科维茨有效前沿计算与最优组合选择
"""

# 导入核心库
import numpy as np
import pandas as pd
from pypfopt import EfficientFrontier, risk_models, expected_returns
from sqlalchemy import create_engine
import osqp

# 从本地配置导入数据库连接
from get_data.db_config import DB_URL

class PortfolioOptimizer:
    """
    投资组合优化器类
    用于计算最优投资组合权重和相关指标
    """
    
    def __init__(self, start_date, end_date, sample_size=20, risk_free_rate=0.03, weight_bounds=(0, 0.35)):
        """
        初始化投资组合优化器
        
        参数：
            start_date: str 起始日期 'YYYYMMDD'
            end_date: str 结束日期 'YYYYMMDD'
            sample_size: int 随机抽样股票数量
            risk_free_rate: float 无风险利率
            weight_bounds: tuple 投资权重范围，形如(最小权重, 最大权重)
        """
        self.start_date = start_date
        self.end_date = end_date
        self.sample_size = sample_size
        self.risk_free_rate = risk_free_rate
        self.weight_bounds = weight_bounds
        self.price_data = None
        self.optimization_result = None
        
    def fetch_price_data(self):
        """
        从stk_factor表获取指定数量的后复权价格
        """
        engine = create_engine(DB_URL)
        
        try:
            # 获取数据质量好的股票样本
            sample_query = f"""
            WITH date_count AS (
                SELECT FLOOR(COUNT(DISTINCT trade_date) * 0.95) as min_required
                FROM stk_factor 
                WHERE trade_date BETWEEN '{self.start_date}' AND '{self.end_date}'
            )
            SELECT DISTINCT s.ts_code
            FROM stk_factor s
            CROSS JOIN date_count
            WHERE s.trade_date BETWEEN '{self.start_date}' AND '{self.end_date}'
            GROUP BY s.ts_code
            HAVING COUNT(*) >= (SELECT min_required FROM date_count)
            ORDER BY RAND()
            LIMIT {self.sample_size}
            """
            
            print(f"执行样本查询SQL: {sample_query}")  # 添加调试信息
            stock_sample = pd.read_sql(sample_query, engine)
            
            if stock_sample.empty:
                raise ValueError(f"在{self.start_date}到{self.end_date}期间未找到符合条件的股票样本")
            
            stock_codes = stock_sample['ts_code'].tolist()
            print(f"获取到的股票样本: {stock_codes}")  # 添加调试信息
            
            # 获取后复权价格
            stock_codes_str = ','.join([f"'{code}'" for code in stock_codes])
            price_query = f"""
            SELECT trade_date, ts_code, close_hfq 
            FROM stk_factor 
            WHERE ts_code IN ({stock_codes_str})
              AND trade_date BETWEEN '{self.start_date}' AND '{self.end_date}'
              AND close_hfq IS NOT NULL
            ORDER BY trade_date
            """
            
            print(f"执行价格查询SQL: {price_query}")  # 添加调试信息
            raw_data = pd.read_sql(price_query, engine)
            
            if raw_data.empty:
                raise ValueError(f"在{self.start_date}到{self.end_date}期间未获取到价格数据")
            
            # 转换为宽表格式并处理缺失值
            self.price_data = raw_data.pivot(index='trade_date', columns='ts_code', values='close_hfq')
            self.price_data = self.price_data.ffill()
            
            print(f"获取到的数据形状: {self.price_data.shape}")  # 添加调试信息
            return self.price_data
            
        except Exception as e:
            print(f"错误详情: {str(e)}")  # 添加错误详情
            raise Exception(f"获取数据失败: {str(e)}")
    
    def optimize_portfolio(self):
        """
        执行投资组合优化
        
        返回：
            dict 包含最优权重、预期收益和风险指标
        """
        if self.price_data is None:
            self.fetch_price_data()
            
        # 数据验证
        if len(self.price_data) < 10:  # 至少需要10个交易日的数据
            raise ValueError(f"数据量不足，至少需要10个交易日的数据，当前只有{len(self.price_data)}个交易日")
            
        # 计算预期收益和风险
        try:
            mu = expected_returns.mean_historical_return(self.price_data)
            S = risk_models.sample_cov(self.price_data)
            
            # 创建优化器实例
            ef = EfficientFrontier(mu, S, weight_bounds=self.weight_bounds)
            
            try:
                # 最大化夏普比率
                weights = ef.max_sharpe(risk_free_rate=self.risk_free_rate)
                cleaned_weights = ef.clean_weights()
                
                # 获取优化结果
                portfolio_performance = ef.portfolio_performance(risk_free_rate=self.risk_free_rate)
                expected_return, volatility, sharpe_ratio = portfolio_performance
                
                # 转换权重为普通的Python字典，确保值是float类型
                weights_dict = {k: float(v) for k, v in cleaned_weights.items()}
                
                # 构建返回结果，确保所有数值都是Python原生类型
                result = {
                    "weights": weights_dict,
                    "metrics": {
                        "expected_annual_return": float(expected_return),
                        "annual_volatility": float(volatility),
                        "sharpe_ratio": float(sharpe_ratio)
                    },
                    "parameters": {
                        "sample_size": int(self.sample_size),
                        "weight_bounds": [float(self.weight_bounds[0]), float(self.weight_bounds[1])],
                        "price_data_shape": [int(x) for x in self.price_data.shape],
                        "missing_values": int(self.price_data.isna().sum().sum())
                    }
                }
                
                return result
                
            except Exception as e:
                raise Exception(f"优化失败: {str(e)}")
                
        except Exception as e:
            raise Exception(f"计算收益和风险失败: {str(e)}")
    
    def get_results(self):
        """
        获取优化结果
        """
        if self.optimization_result is None:
            return self.optimize_portfolio()
        return self.optimization_result

if __name__ == "__main__":
    # 示例用法
    optimizer = PortfolioOptimizer(
        start_date='2024-12-01',  # 使用YYYY-MM-DD格式
        end_date='2025-03-10',    # 使用YYYY-MM-DD格式
        sample_size=20,
        risk_free_rate=0.03,
        weight_bounds=(0, 0.35)
    )
    result = optimizer.optimize_portfolio()
    print("优化结果：", result)