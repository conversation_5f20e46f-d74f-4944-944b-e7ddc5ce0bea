"""
@Author: Jiang<PERSON>in
@Description: 测试投资组合优化API的脚本
"""

import requests
import json
from datetime import datetime, timedelta

def test_portfolio_optimization():
    """测试投资组合优化API"""
    # API地址
    url = "http://127.0.0.1:8010/optimize"
    
    # 构造请求参数
    # 默认使用最近一年的数据
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")
    
    data = {
        "start_date": start_date,
        "end_date": end_date,
        "sample_size": 20,
        "risk_free_rate": 0.03,
        "weight_bounds": [0, 0.35]
    }
    
    try:
        # 发送POST请求
        print("正在发送请求...")
        print(f"请求参数: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=data)
        
        # 检查响应状态
        response.raise_for_status()
        
        # 解析响应结果
        result = response.json()
        
        print("\n优化结果:")
        print("-" * 50)
        
        if result.get("status") == "success":
            # 提取投资组合信息
            portfolio = result["data"]
            
            # 打印权重分配
            print("\n投资权重分配:")
            weights = portfolio["weights"]
            for stock, weight in weights.items():
                print(f"{stock}: {weight:.2%}")
            
            # 打印组合表现
            performance = portfolio["performance"]
            print("\n组合表现指标:")
            print(f"预期年化收益率: {performance['expected_annual_return']:.2%}")
            print(f"年化波动率: {performance['annual_volatility']:.2%}")
            print(f"夏普比率: {performance['sharpe_ratio']:.2f}")
            
            # 打印使用的求解器
            if "solver_used" in portfolio:
                print(f"\n使用的求解器: {portfolio['solver_used']}")
        else:
            print("优化失败:")
            print(result.get("detail", "未知错误"))
            
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {str(e)}")
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    # 确保API服务已经启动
    print("开始测试投资组合优化API...")
    test_portfolio_optimization()
