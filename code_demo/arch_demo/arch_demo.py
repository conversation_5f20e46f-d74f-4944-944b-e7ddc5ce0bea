"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/1/10 19:29
@Description: GARCH模型实现及优化版本 - 使用实际股票数据
"""
import logging
from typing import Optional, Tuple, Union
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import arch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sqlalchemy import create_engine, text
from get_data.db_config import DB_URL

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataLoader:
    """数据加载器类"""
    
    def __init__(self):
        """初始化数据库连接"""
        self.engine = create_engine(DB_URL)
        
    def get_stock_data(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取股票数据
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            包含股票数据的DataFrame
        """
        query = text("""
            SELECT trade_date, close_hfq
            FROM stk_factor
            WHERE ts_code = :ts_code
            AND trade_date BETWEEN :start_date AND :end_date
            ORDER BY trade_date
        """)
        
        try:
            with self.engine.connect() as conn:
                df = pd.read_sql_query(
                    query,
                    conn,
                    params={'ts_code': ts_code, 'start_date': start_date, 'end_date': end_date}
                )
            logger.info(f"成功获取股票 {ts_code} 的数据，共 {len(df)} 条记录")
            return df
        except Exception as e:
            logger.error(f"获取股票数据失败: {str(e)}")
            raise

class GarchModelAnalyzer:
    """GARCH模型分析器类"""
    
    def __init__(self, data: Union[pd.Series, np.ndarray], p: int = 1, q: int = 1):
        """
        初始化GARCH模型分析器
        
        Args:
            data: 收益率数据
            p: GARCH模型的p参数
            q: GARCH模型的q参数
        """
        self.data = pd.Series(data) if isinstance(data, np.ndarray) else data
        self.p = p
        self.q = q
        self.model = None
        self.results = None
        self.scale_factor = 100  # 缩放因子
        
    @staticmethod
    def calculate_log_returns(prices: pd.Series) -> pd.Series:
        """
        计算对数收益率并进行缩放
        
        Args:
            prices: 价格序列
            
        Returns:
            缩放后的对数收益率序列
        """
        # 计算对数收益率并放大100倍
        log_returns = np.log(prices / prices.shift(1)).dropna()
        return log_returns * 100  # 放大100倍以优化模型拟合
        
    def prepare_data(self) -> None:
        """数据预处理"""
        if self.data is None or len(self.data) == 0:
            raise ValueError("数据不能为空")
        logger.info(f"数据样本数量: {len(self.data)}")
        logger.info(f"数据规模: {self.data.std():.4f}")
        
    def plot_diagnostics(self) -> None:
        """绘制诊断图"""
        try:
            # 收益率时间序列图
            plt.figure(figsize=(12, 8))
            plt.subplot(311)
            plt.plot(self.data)
            plt.title('对数收益率时间序列 (放大100倍)')
            
            # 平方收益率的自相关图
            plt.subplot(312)
            plot_acf(self.data**2, lags=20, ax=plt.gca())
            plt.title('平方对数收益率自相关图')
            
            # 平方收益率的偏自相关图
            plt.subplot(313)
            plot_pacf(self.data**2, lags=20, ax=plt.gca())
            plt.title('平方对数收益率偏自相关图')
            
            plt.tight_layout()
            plt.show()
        except Exception as e:
            logger.error(f"绘图过程出错: {str(e)}")
            
    def fit_model(self) -> None:
        """拟合GARCH模型"""
        try:
            self.model = arch.arch_model(self.data, vol='Garch', p=self.p, q=self.q)
            self.results = self.model.fit(disp='off')
            logger.info("模型拟合完成")
            logger.info("\n" + str(self.results.summary()))
        except Exception as e:
            logger.error(f"模型拟合失败: {str(e)}")
            raise
            
    def forecast_volatility(self, horizon: int = 5) -> pd.Series:
        """
        预测未来波动率
        
        Args:
            horizon: 预测步数
            
        Returns:
            预测的波动率序列（已还原缩放）
        """
        if self.results is None:
            raise ValueError("请先拟合模型")
            
        try:
            forecasts = self.results.forecast(horizon=horizon, reindex=False)
            forecast_variance = forecasts.variance.iloc[-1, :]
            # 还原缩放，将方差除以scale_factor的平方
            forecast_volatility = np.sqrt(forecast_variance) / self.scale_factor
            
            logger.info(f"未来{horizon}步的波动率预测值（已还原缩放）：\n{forecast_volatility}")
            return forecast_volatility
        except Exception as e:
            logger.error(f"波动率预测失败: {str(e)}")
            raise
            
    def rolling_forecast(self, window_size: int) -> pd.Series:
        """
        使用滚动窗口进行预测
        
        Args:
            window_size: 滚动窗口大小
            
        Returns:
            滚动预测的波动率序列（已还原缩放）
        """
        predictions = []
        data_length = len(self.data)
        
        try:
            # 使用向量化操作优化性能
            for i in range(window_size, data_length):
                train = self.data[i-window_size:i]
                model = arch.arch_model(train, vol='Garch', p=self.p, q=self.q)
                results = model.fit(disp='off', show_warning=False)
                pred = results.forecast(horizon=1, reindex=False)
                # 还原缩放
                predictions.append(np.sqrt(pred.variance.iloc[-1, 0]) / self.scale_factor)
                
            predictions = pd.Series(
                predictions,
                index=self.data.index[window_size:]
            )
            return predictions
        except Exception as e:
            logger.error(f"滚动预测失败: {str(e)}")
            raise

def main():
    """主函数"""
    # 设置股票代码和时间范围
    ts_code = '835184.BJ'  # 以平安银行为例
    start_date = '2024-01-01'
    end_date = '2024-12-31'
    
    try:
        # 获取股票数据
        data_loader = DataLoader()
        stock_data = data_loader.get_stock_data(ts_code, start_date, end_date)
        
        # 计算对数收益率（已包含缩放）
        log_returns = GarchModelAnalyzer.calculate_log_returns(stock_data['close_hfq'])
        
        # 创建分析器实例
        analyzer = GarchModelAnalyzer(log_returns)
        
        # 数据预处理
        analyzer.prepare_data()
        
        # 绘制诊断图
        analyzer.plot_diagnostics()
        
        # 拟合模型
        analyzer.fit_model()
        
        # 预测波动率
        volatility_forecast = analyzer.forecast_volatility(horizon=5)
        
        # 滚动预测
        rolling_predictions = analyzer.rolling_forecast(window_size=60)  # 使用60个交易日作为滚动窗口
        
        # 绘制结果
        plt.figure(figsize=(12, 6))
        plt.plot(rolling_predictions, label='预测波动率')
        plt.plot(analyzer.data / analyzer.scale_factor, label='实际对数收益率', alpha=0.5)
        plt.title(f'{ts_code} 滚动预测波动率 vs 实际对数收益率')
        plt.legend()
        plt.show()
        
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()
