"""
@Author: jiang<PERSON>
@Date: 2025/5/2 20:07
@File: multi_2.py
@Version: 1.0
@Description: 
"""

import os
import pandas as pd
import numpy as np
import talib
from sqlalchemy import create_engine
from get_data.db_config import DB_URL
from get_data.stock_data import StockDataLoader
from multiprocessing import Pool, cpu_count
from tqdm import tqdm

# 回测参数
INIT_CASH = 100000
PROFIT_TARGET = 0.10
LOSS_LIMIT = 0.05
HOLDING_PERIOD = 5
RESULT_DIR = os.path.join(os.path.dirname(__file__), 'backtest_results')

# 获取股票列表
engine = create_engine(DB_URL)
stock_list = pd.read_sql("SELECT ts_code FROM stock_basic WHERE exchange in ('SSE','SZSE') and list_status='L'", engine)['ts_code'].tolist()

# 回测主函数（单只股票）
def backtest_single(ts_code, start_date, end_date):
    loader = StockDataLoader()
    df = loader.get_stock_factors(
        ts_code=ts_code,
        start_date=start_date,
        end_date=end_date,
        factors=[
            'trade_date', 'close_hfq', 'open_hfq', 'high_hfq', 'low_hfq', 'vol',
            'rsi_6', 'boll_upper', 'boll_mid', 'boll_lower'
        ]
    )
    if df is None or df.empty or len(df) < 30:
        return None, None
    df = df.sort_values('trade_date').reset_index(drop=True)
    # 计算3日、5日均线
    df['ma3'] = talib.SMA(df['close_hfq'], timeperiod=3)
    df['ma5'] = talib.SMA(df['close_hfq'], timeperiod=5)
    # 计算RSI（优先用rsi_6字段，否则用talib）
    if 'rsi_6' not in df or df['rsi_6'].isnull().all():
        df['rsi_6'] = talib.RSI(df['close_hfq'], timeperiod=6)
    # 计算布林带（优先用字段，否则用talib）
    if 'boll_upper' not in df or df['boll_upper'].isnull().all():
        up, mid, low = talib.BBANDS(df['close_hfq'], timeperiod=20, nbdevup=2, nbdevdn=2)
        df['boll_upper'] = up
        df['boll_mid'] = mid
        df['boll_lower'] = low
    # 回测逻辑
    cash = INIT_CASH
    position = 0
    buy_price = 0
    hold_days = 0
    trade_records = []
    for i in range(5, len(df)):
        # 买入条件
        cond1 = df.loc[i-1, 'ma3'] <= df.loc[i-1, 'ma5'] and df.loc[i, 'ma3'] > df.loc[i, 'ma5']
        cond2 = df.loc[i, 'rsi_6'] < 45
        boll_low = df.loc[i, 'boll_lower']
        ma3 = df.loc[i, 'ma3']
        fifty = boll_low + (ma3 - boll_low) * 0.5
        cond3 = (df.loc[i, 'close_hfq'] > fifty) and (df.loc[i, 'close_hfq'] < ma3)
        date = df.loc[i, 'trade_date']
        price = round(float(df.loc[i, 'close_hfq']), 2)
        # 买入
        if position == 0 and cond1 and cond2 and cond3:
            position = int(cash // price)
            buy_price = price
            cash = round(cash - position * price, 2)
            hold_days = 0
            trade_records.append({
                'ts_code': ts_code, 'trade_date': date, 'action': 'buy', 'price': price, 'volume': position, 'cash': cash
            })
        # 卖出条件
        if position > 0:
            hold_days += 1
            profit = round((price - buy_price) / buy_price, 4)
            if profit >= PROFIT_TARGET or profit <= -LOSS_LIMIT or hold_days >= HOLDING_PERIOD:
                cash = round(cash + position * price, 2)
                trade_records.append({
                    'ts_code': ts_code, 'trade_date': date, 'action': 'sell', 'price': price, 'volume': position, 'cash': cash
                })
                position = 0
                buy_price = 0
                hold_days = 0
    # 期末清仓
    if position > 0:
        price = round(float(df.loc[len(df)-1, 'close_hfq']), 2)
        cash = round(cash + position * price, 2)
        trade_records.append({
            'ts_code': ts_code, 'trade_date': df.loc[len(df)-1, 'trade_date'], 'action': 'sell', 'price': price, 'volume': position, 'cash': cash
        })
    # 汇总
    final_value = round(cash, 2)
    if position > 0:
        final_value = round(final_value + position * round(float(df.loc[len(df)-1, 'close_hfq']), 2), 2)
    summary = {
        'ts_code': ts_code,
        'init_cash': round(INIT_CASH, 2),
        'final_value': final_value,
        'return': round((final_value - INIT_CASH) / INIT_CASH, 4),
        'trade_count': len([x for x in trade_records if x['action']=='buy'])
    }
    return summary, trade_records

def run_multi_backtest(start_date, end_date, n_jobs=None):
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    args = [(code, start_date, end_date) for code in stock_list]
    results = []
    trade_details = []
    # 单进程顺序回测
    for arg in tqdm(args, total=len(args)):
        summary, trades = backtest_single(*arg)
        if summary is not None:
            results.append(summary)
            if trades:
                trade_details.extend(trades)
    # 汇总保存
    df_results = pd.DataFrame(results)
    df_trades = pd.DataFrame(trade_details)
    float_cols_results = [col for col in df_results.columns if df_results[col].dtype in [float, np.float64, np.float32]]
    float_cols_trades = [col for col in df_trades.columns if df_trades[col].dtype in [float, np.float64, np.float32]]
    df_results[float_cols_results] = df_results[float_cols_results].round(2)
    df_trades[float_cols_trades] = df_trades[float_cols_trades].round(2)
    df_results.to_csv(os.path.join(RESULT_DIR, 'multi2_backtest_summary.csv'), index=False, float_format='%.2f')
    df_trades.to_csv(os.path.join(RESULT_DIR, 'multi2_trade_details.csv'), index=False, float_format='%.2f')
    print(f"回测完成，汇总结果保存在: {RESULT_DIR}")

if __name__ == '__main__':
    # 示例回测区间
    run_multi_backtest('2024-01-01', '2024-12-31')
