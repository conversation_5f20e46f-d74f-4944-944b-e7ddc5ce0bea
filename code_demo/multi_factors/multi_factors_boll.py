"""
@Author: jiang<PERSON>
@Date: 2025/5/2 19:03
@File: multi_factors_boll.py
@Version: 1.0
@Description:目前有问题，不能发生交易

    买入逻辑：
    同时满足以下三个条件时，进行买入：
    3 日均线突破 5 日均线： 短期（3日）简单移动平均线从下方穿过长期（5日）简单移动平均线。
    RSI 小于 45： 相对强弱指数 (RSI) 的值低于 45。
    当前股价处于 3 日均线与布林线下轨 50% 分位： 当前的收盘价高于布林带下轨和 3 日均线之间 50% 的位置，并且低于 3 日均线。
    
    卖出逻辑：
    当持有仓位时，满足以下任一条件时，进行卖出：
    盈利达到 10%： 当前仓位的盈利达到或超过 10%。
    持股超过 5 天： 从买入之日起，持股时间达到或超过 5 个交易日。
    亏损超过 5%： 当前仓位的亏损达到或超过 5%。
"""
import backtrader as bt
import backtrader.indicators as btind
import pandas as pd
import os
from sqlalchemy import create_engine
from get_data.db_config import DB_URL
from get_data.stock_data import StockDataLoader

# 自定义PandasData，显式忽略openinterest，防止int类型下标报错
class MyPandasData(bt.feeds.PandasData):
    lines = ('open', 'high', 'low', 'close', 'volume')
    params = (
        ('datetime', None),
        ('open', 'open'),
        ('high', 'high'),
        ('low', 'low'),
        ('close', 'close'),
        ('volume', 'volume'),
        ('openinterest', None),
    )

class BuyStrategy(bt.Strategy):
    """买入策略：3日均线突破5日均线，RSI小于45，当前股价处于3日均线与布林线下轨50%分位"""
    params = (
        ('sma_short_period', 3),  # 短期均线周期
        ('sma_long_period', 5),   # 长期均线周期
        ('rsi_period', 14),       # RSI周期
        ('boll_period', 20),      # 布林带周期
        ('boll_dev', 2),          # 布林带标准差倍数
        ('profit_target', 0.10),  # 盈利目标
        ('loss_limit', 0.05),     # 亏损限制
        ('holding_period', 5),    # 最大持股周期
        ('printlog', False),      # 是否打印交易日志
    )

    def __init__(self):
        self.dataclose = self.datas[0].close
        self.order = None
        self.buyprice = None
        self.buycomm = None
        self.bar_entered = None # 记录入场bar
        self.trade_history = [] # 存储交易记录

        # 添加指标
        self.sma_short = btind.SimpleMovingAverage(
            self.datas[0], period=self.params.sma_short_period)
        self.sma_long = btind.SimpleMovingAverage(
            self.datas[0], period=self.params.sma_long_period)
        self.rsi = btind.RSI(self.datas[0], period=self.params.rsi_period)
        self.boll = btind.BollingerBands(
            self.datas[0], period=self.params.boll_period, devfactor=self.params.boll_dev)

    def log(self, txt, dt=None, trade_info=None):
        """
        策略日志记录
        :param txt: 日志文本
        :param dt: 日期时间
        :param trade_info: 交易信息字典(可选)
        """
        if self.params.printlog:
            dt = dt or self.datas[0].datetime.date(0)
            log_msg = f'{dt.isoformat()}, {txt}'
            print(log_msg)
            
            # 如果有交易信息，添加到交易历史
            if trade_info:
                trade_info['log'] = log_msg
                self.trade_history.append(trade_info)

    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            return
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(
                    f'BUY EXECUTED, Price: {order.executed.price:.2f}, Cost: {order.executed.value:.2f}, Comm {order.executed.comm:.2f}'
                )
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
                self.bar_entered = int(len(self)) # 记录入场bar并确保为整数
            elif order.issell():
                self.log(
                    f'SELL EXECUTED, Price: {order.executed.price:.2f}, Cost: {order.executed.value:.2f}, Comm {order.executed.comm:.2f}'
                )
            self.bar_executed = len(self)
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')
        self.order = None

    def notify_trade(self, trade):
        """交易状态通知"""
        if not trade.isclosed:
            return
            
        # 安全地获取交易对象的属性
        try:
            data_name = trade.data._name if hasattr(trade.data, '_name') else str(trade.data)
        except:
            data_name = 'unknown'
            
        try:
            broker_name = trade.broker.p.name if hasattr(trade.broker, 'p') and hasattr(trade.broker.p, 'name') else 'unknown'
        except:
            broker_name = 'unknown'
            
        dt_entered = None
        try:
            if hasattr(trade, 'dtopen'):
                dt_entered = bt.num2date(trade.dtopen)
        except:
            pass
            
        dt_closed = None
        try:
            if hasattr(trade, 'dtclose'):
                dt_closed = bt.num2date(trade.dtclose)
        except:
            pass
            
        # 确保self.bar_entered不为None
        # 确保bar_entered为整数
        bar_entered = int(self.bar_entered) if self.bar_entered is not None else 0
        current_bar = int(len(self))
        
        trade_details = {
            'ref': getattr(trade, 'ref', 0),
            'id': getattr(trade, 'id', 0),
            'status': getattr(trade, 'status', 0),
            'pnl': getattr(trade, 'pnl', 0.0),
            'pnlcomm': getattr(trade, 'pnlcomm', 0.0),
            'value': getattr(trade, 'value', 0.0),
            'commission': getattr(trade, 'commission', 0.0),
            'price': getattr(trade, 'price', 0.0),
            'size': int(getattr(trade, 'size', 0)),  # 强制转换为整数
            'isclosed': getattr(trade, 'isclosed', False),
            'justopened': getattr(trade, 'justopened', False),
            'justclosed': getattr(trade, 'justclosed', False),
            'bar_entered': bar_entered,
            'dt_entered': dt_entered,
            'bar_closed': current_bar,
            'dt_closed': dt_closed,
            'data': data_name,
            'broker_name': broker_name
        }
        self.trade_history.append(trade_details)
        self.log(f'OPERATION PROFIT, GROSS {trade.pnl:.2f}, NET {trade.pnlcomm:.2f}')

    def next(self):
        """策略核心逻辑 - 每个bar执行一次"""
        if self.order:
            return

        # 确保所有指标都已计算就绪
        if not self.sma_short or not self.sma_long or not self.rsi or not self.boll:
             return
             
        # 确保有足够的bar来计算指标和进行交易判断
        # 这里的判断条件需要根据最长周期的指标来定，例如布林带周期20，RSI周期14，长均线周期5，短均线周期3
        # 至少需要20个bar才能计算布林带
        if len(self) < max(self.params.sma_long_period, self.params.rsi_period, self.params.boll_period):
             return

        # 检查买入信号
        condition1 = self.sma_short[0] > self.sma_long[0] and self.sma_short[-1] <= self.sma_long[-1] # 3日均线上穿5日均线
        condition2 = self.rsi[0] < 45 # RSI小于45

        # 计算3日均线与布林线下轨50%分位
        boll_lower = self.boll.lines.bot[0]
        sma_short_val = self.sma_short[0]
        fifty_percent_level = boll_lower + (sma_short_val - boll_lower) * 0.5

        condition3 = self.dataclose[0] > fifty_percent_level and self.dataclose[0] < sma_short_val # 当前股价处于3日均线与布林线下轨50%分位

        if not self.position:
            if condition1 and condition2 and condition3:
                self.log(f'BUY CREATE, {self.dataclose[0]:.2f}')
                self.order = self.buy()
        else:
            # 检查卖出信号
            # 计算当前仓位的盈亏比例
            current_price = self.dataclose[0]
            # 确保self.buyprice已经被设置 (即已经有买入交易发生)
            if self.buyprice is None:
                return # 如果还没有买入，不执行卖出检查

            price_change_pct = (current_price - self.buyprice) / self.buyprice
            
            sell_condition1 = price_change_pct >= self.params.profit_target # 盈利达到目标
            sell_condition2 = (int(len(self)) - int(self.bar_entered)) >= int(self.params.holding_period) # 明确转换为整数运算
            sell_condition3 = price_change_pct <= -self.params.loss_limit # 亏损超过限制

            if sell_condition1 or sell_condition2 or sell_condition3:
                self.log(f'SELL CREATE, {self.dataclose[0]:.2f}')
                self.order = self.sell()

def run_batch_backtest(start_date, end_date, output_dir="backtest_results"):
    """
    对所有SH股票进行批量回测并生成汇总报告和详细交易记录
    """
    engine = create_engine(DB_URL)
    loader = StockDataLoader()
    summary_data = []

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")

    # 获取所有SH股票代码
    try:
        # Limiting to 20 stocks for demonstration, remove limit for all stocks
        sh_stocks_df = pd.read_sql("SELECT ts_code FROM stock_basic WHERE exchange = 'SSE' limit 500", engine)
        sh_stock_list = sh_stocks_df['ts_code'].tolist()
    except Exception as e:
        print(f"Error getting SH stock list: {e}")
        return

    print(f"Found {len(sh_stock_list)} SH stocks for backtesting.")

    for i, ts_code in enumerate(sh_stock_list):
        print(f"Running backtest for {ts_code} ({i+1}/{len(sh_stock_list)})")
        try:
            # 加载股票数据
            data = loader.get_stock_factors(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                factors=['trade_date', 'open_hfq', 'high_hfq', 'low_hfq', 'close_hfq', 'vol'] # Using hfq for adjusted prices
            )
            data['trade_date'] = pd.to_datetime(data['trade_date'])
            data = data.rename(columns={
                'trade_date': 'datetime',
                'open_hfq': 'open',
                'high_hfq': 'high',
                'low_hfq': 'low',
                'close_hfq': 'close',
                'vol': 'volume'
            })
            # 强制类型转换，防止int类型导致backtrader报错
            data['open'] = data['open'].astype(float)
            data['high'] = data['high'].astype(float)
            data['low'] = data['low'].astype(float)
            data['close'] = data['close'].astype(float)
            data['volume'] = data['volume'].astype(float)
            data = data.set_index('datetime')
            data = data.sort_index()
            # 只保留必须的5列
            data = data[['open', 'high', 'low', 'close', 'volume']]
            # 再次填充缺失值
            data = data.ffill().bfill()
            # 检查NaN和inf
            if data.isnull().any().any():
                print(f"{ts_code} 仍有缺失值！")
            if (data == float('inf')).any().any() or (data == float('-inf')).any().any():
                print(f"{ts_code} 存在inf！")
            # 强制index为datetime类型
            data.index = pd.to_datetime(data.index)

            if data.empty:
                print(f"No data for {ts_code}, skipping.")
                continue

            # 创建Backtrader数据源
            data_feed = MyPandasData(dataname=data)

            # 创建Cerebro引擎
            cerebro = bt.Cerebro()

            # 添加策略
            cerebro.addstrategy(BuyStrategy)

            # 添加数据
            cerebro.adddata(data_feed)

            # 设置初始资金
            cerebro.broker.setcash(100000.0)

            # 运行回测
            print(f"Starting backtest for {ts_code}...")
            results = cerebro.run()
            print(f"Backtest finished for {ts_code}.")

            # 获取策略实例
            strategy_instance = results[0]

            # 收集结果
            final_value = cerebro.broker.getvalue()
            initial_value = 100000.0
            pnl = final_value - initial_value

            summary_data.append({
                'ts_code': ts_code,
                'initial_value': initial_value,
                'final_value': final_value,
                'pnl': pnl,
                'return': pnl / initial_value
            })

            # 保存详细交易记录
            if hasattr(strategy_instance, 'trade_history') and strategy_instance.trade_history:
                trade_history_df = pd.DataFrame(strategy_instance.trade_history)
                trade_history_filename = os.path.join(output_dir, f'trade_history_{ts_code}.csv')
                trade_history_df.to_csv(trade_history_filename, index=False)
                print(f"Saved trade history for {ts_code} to {trade_history_filename}")

        except Exception as e:
            print(f"Error running backtest for {ts_code}: {e}")
            continue

    # 生成并保存汇总报告
    summary_df = pd.DataFrame(summary_data)
    if not summary_df.empty:
        summary_filename = os.path.join(output_dir, 'batch_backtest_summary.csv')
        summary_df.to_csv(summary_filename, index=False)
        print(f"\n--- Batch Backtest Summary Report ---")
        print(summary_df.to_string(index=False))
        print(f"\nSummary report saved to {summary_filename}")
        print("\n--- Overall Statistics ---")
        print(f"Number of stocks tested: {len(summary_df)}")
        print(f"Average Return: {summary_df['return'].mean():.4f}")
        print(f"Total PnL: {summary_df['pnl'].sum():.2f}")
    else:
        print("\nNo successful backtests to summarize.")


if __name__ == '__main__':
    # Example usage of batch backtest
    # Define your desired backtest period
    start_date = '2024-01-01'
    end_date = '2024-12-31'
    run_batch_backtest(start_date, end_date)
