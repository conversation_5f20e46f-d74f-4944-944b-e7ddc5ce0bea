"""
@Author: ji<PERSON><PERSON>
@Date: 2025/6/16 15:16
@File: atr_calculate.py
@Version: 1.0
@Description: 计算所有A股的14日ATR值，并保存到数据库中，首次初始化数据，从 23年开始
"""

import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
import sys
import os
from datetime import datetime, date
import logging
import warnings
import time
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from get_data.db_config import get_engine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'atr_calculate_all_fixed_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ATRCalculatorFixed:
    """ATR（平均真实波动范围）计算器（修复版本）"""
    
    def __init__(self, period=14):
        """
        初始化ATR计算器
        
        Args:
            period (int): ATR计算周期，默认14日
        """
        self.period = period
        self.engine = get_engine()
    
    def calculate_tr(self, df):
        """计算真实波动范围（True Range）"""
        high_low = df['high'] - df['low']
        high_close_prev = np.abs(df['high'] - df['close'].shift(1))
        low_close_prev = np.abs(df['low'] - df['close'].shift(1))
        
        tr = np.maximum(high_low, np.maximum(high_close_prev, low_close_prev))
        return tr
    
    def calculate_atr(self, df):
        """计算ATR"""
        df = df.sort_values('trade_date').copy()
        df['tr'] = self.calculate_tr(df)
        df['atr'] = df['tr'].ewm(span=self.period, adjust=False).mean()
        df = df.iloc[self.period-1:].copy()
        return df[['ts_code', 'trade_date', 'atr']].copy()
    
    def get_stock_data(self, ts_code, start_date='2023-01-01'):
        """获取股票价格数据"""
        query = """
        SELECT ts_code, trade_date, high, low, close
        FROM stk_factor 
        WHERE ts_code = %s 
        AND trade_date >= %s
        ORDER BY trade_date
        """
        
        try:
            df = pd.read_sql(query, self.engine, params=(ts_code, start_date))
            return df
        except Exception as e:
            logger.error(f"获取股票数据失败 {ts_code}: {str(e)}")
            return pd.DataFrame()
    
    def save_atr_data(self, atr_data):
        """保存ATR数据到数据库"""
        if atr_data.empty:
            return
        
        # 添加period列和update_time列
        atr_data['period'] = self.period
        atr_data['update_time'] = datetime.now()
        
        try:
            # 使用ON DUPLICATE KEY UPDATE处理重复数据
            atr_data.to_sql('atr_data_temp', self.engine, if_exists='replace', index=False, method='multi')
            
            # 执行插入或更新操作
            update_query = """
            INSERT INTO atr_data (ts_code, trade_date, atr, period, update_time)
            SELECT ts_code, trade_date, atr, period, update_time
            FROM atr_data_temp
            ON DUPLICATE KEY UPDATE
            atr = VALUES(atr),
            update_time = VALUES(update_time)
            """
            
            with self.engine.connect() as conn:
                conn.execute(text(update_query))
                conn.commit()
                
            # 删除临时表
            with self.engine.connect() as conn:
                conn.execute(text("DROP TABLE IF EXISTS atr_data_temp"))
                conn.commit()
                
        except Exception as e:
            logger.error(f"保存ATR数据失败: {str(e)}")
    
    def process_single_stock(self, ts_code):
        """处理单只股票的ATR计算"""
        try:
            # 获取股票数据
            stock_data = self.get_stock_data(ts_code)
            
            if stock_data.empty:
                return (ts_code, False, "无数据")
            
            if len(stock_data) < self.period:
                return (ts_code, False, f"数据不足{self.period}个交易日")
            
            # 计算ATR
            atr_data = self.calculate_atr(stock_data)
            
            if atr_data.empty:
                return (ts_code, False, "ATR计算失败")
            
            # 保存数据
            self.save_atr_data(atr_data)
            
            return (ts_code, True, f"成功计算{len(atr_data)}条ATR数据")
            
        except Exception as e:
            return (ts_code, False, f"处理异常: {str(e)}")
    
    def get_all_stocks(self):
        """获取所有上市的A股股票代码"""
        query = """
        SELECT ts_code 
        FROM stock_basic 
        WHERE list_status = 'L' 
        AND (ts_code LIKE '%%.SZ' OR ts_code LIKE '%%.SH')
        ORDER BY ts_code
        """
        
        try:
            df = pd.read_sql(query, self.engine)
            return df['ts_code'].tolist()
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            return []
    
    def create_atr_table(self):
        """创建ATR数据表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS `atr_data` (
          `ts_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'TS代码',
          `trade_date` date NOT NULL COMMENT '交易日期',
          `atr` decimal(10,4) NOT NULL COMMENT 'ATR值',
          `period` int NOT NULL COMMENT 'ATR计算周期（如14日）',
          `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`ts_code`,`trade_date`,`period`),
          KEY `idx_trade_date` (`trade_date`),
          KEY `idx_ts_code` (`ts_code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='ATR因子数据表';
        """
        
        try:
            with self.engine.connect() as conn:
                conn.execute(text(create_table_sql))
                conn.commit()
            logger.info("ATR数据表创建成功")
        except Exception as e:
            logger.error(f"创建ATR数据表失败: {str(e)}")

def main():
    """主函数"""
    logger.info("开始计算所有A股ATR值...")
    
    # 创建ATR计算器
    calculator = ATRCalculatorFixed(period=14)
    
    # 创建ATR数据表
    calculator.create_atr_table()
    
    # 获取所有股票代码
    all_stocks = calculator.get_all_stocks()
    logger.info(f"获取到{len(all_stocks)}只股票")
    
    if not all_stocks:
        logger.error("未获取到股票列表，程序退出")
        return
    
    # 开始处理
    start_time = datetime.now()
    total_success = 0
    total_failed = 0
    
    for i, ts_code in enumerate(all_stocks, 1):
        try:
            result = calculator.process_single_stock(ts_code)
            
            if result[1]:  # 成功
                total_success += 1
                logger.info(f"[{i}/{len(all_stocks)}] ✓ {result[0]}: {result[2]}")
            else:  # 失败
                total_failed += 1
                logger.warning(f"[{i}/{len(all_stocks)}] ✗ {result[0]}: {result[2]}")
            
            # 每处理100只股票显示一次进度
            if i % 100 == 0:
                elapsed = datetime.now() - start_time
                speed = i / elapsed.total_seconds() * 60  # 每分钟处理数量
                remaining = (len(all_stocks) - i) / speed if speed > 0 else 0
                logger.info(f"进度: {i}/{len(all_stocks)} ({i/len(all_stocks)*100:.1f}%), "
                           f"成功: {total_success}, 失败: {total_failed}, "
                           f"速度: {speed:.1f}只/分钟, 预计剩余: {remaining:.1f}分钟")
                
        except Exception as e:
            total_failed += 1
            logger.error(f"[{i}/{len(all_stocks)}] 处理股票{ts_code}时发生异常: {str(e)}")
        
        # 小延迟避免数据库压力过大
        time.sleep(0.01)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info("=" * 60)
    logger.info("ATR计算完成!")
    logger.info(f"总处理股票数: {len(all_stocks)}")
    logger.info(f"成功: {total_success}")
    logger.info(f"失败: {total_failed}")
    logger.info(f"成功率: {total_success/len(all_stocks)*100:.1f}%")
    logger.info(f"总耗时: {duration}")
    logger.info(f"平均速度: {len(all_stocks)/duration.total_seconds()*60:.1f}只/分钟")
    logger.info("=" * 60)

if __name__ == "__main__":
    main() 