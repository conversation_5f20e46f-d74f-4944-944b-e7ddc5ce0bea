"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/6/18 18:30
@File: atr_calculate_incremental_mp_fixed.py
@Version: 3.0
@Description: 单进程版ATR增量计算程序，简化逻辑避免多进程冲突
"""

import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
import sys
import os
from datetime import datetime, date, timedelta
import logging
import warnings
import time
import gc

warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from get_data.db_config import get_engine

# 配置日志 - 只输出到控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 全局配置
BATCH_SIZE = 100   # 增加批次大小，单进程处理更高效

def calculate_tr(df):
    """计算真实波动范围（True Range）"""
    high_low = df['high'] - df['low']
    high_close_prev = np.abs(df['high'] - df['close'].shift(1))
    low_close_prev = np.abs(df['low'] - df['close'].shift(1))
    
    tr = np.maximum(high_low, np.maximum(high_close_prev, low_close_prev))
    return tr

def calculate_atr(df, period=14):
    """计算ATR"""
    df = df.sort_values('trade_date').copy()
    df['tr'] = calculate_tr(df)
    df['atr'] = df['tr'].ewm(span=period, adjust=False).mean()
    return df

def get_stock_data_from_date_safe(ts_code, start_date, period=14):
    """安全地获取股票价格数据，带有连接管理"""
    engine = None
    try:
        engine = get_engine()
        extended_start_date = start_date - timedelta(days=30)
        
        query = """
        SELECT ts_code, trade_date, high, low, close
        FROM stk_factor 
        WHERE ts_code = %s 
        AND trade_date >= %s
        ORDER BY trade_date
        """
        
        df = pd.read_sql(query, engine, params=(ts_code, extended_start_date))
        return df
        
    except Exception as e:
        logger.error(f"获取股票数据失败 {ts_code}: {str(e)}")
        return pd.DataFrame()
    finally:
        if engine:
            try:
                engine.dispose()
            except:
                pass

def get_missing_dates_safe(ts_code, period=14):
    """安全地获取某股票缺失的交易日期"""
    engine = None
    try:
        engine = get_engine()
        
        # 获取最新的ATR日期
        query = """
        SELECT MAX(trade_date) as latest_date
        FROM atr_data 
        WHERE ts_code = %s AND period = %s
        """
        result = pd.read_sql(query, engine, params=(ts_code, period))
        latest_atr_date = result['latest_date'].iloc[0]
        
        if pd.isna(latest_atr_date):
            start_date = date(2023, 1, 1)
        else:
            start_date = latest_atr_date + timedelta(days=1)
        
        # 获取该股票在stk_factor表中的最新交易日期
        query = """
        SELECT MAX(trade_date) as latest_trade_date
        FROM stk_factor 
        WHERE ts_code = %s
        """
        result = pd.read_sql(query, engine, params=(ts_code,))
        latest_trade_date = result['latest_trade_date'].iloc[0]
        
        if pd.isna(latest_trade_date) or latest_trade_date < start_date:
            return None, None
            
        return start_date, latest_trade_date
        
    except Exception as e:
        logger.error(f"获取缺失日期失败 {ts_code}: {str(e)}")
        return None, None
    finally:
        if engine:
            try:
                engine.dispose()
            except:
                pass

def process_single_stock_safe(ts_code, period=14):
    """安全处理单只股票的ATR增量计算"""
    try:
        # 获取缺失的日期范围
        start_date, end_date = get_missing_dates_safe(ts_code, period)
        
        if start_date is None or end_date is None:
            return (ts_code, True, "无新数据需要计算", pd.DataFrame())
        
        # 获取股票数据
        stock_data = get_stock_data_from_date_safe(ts_code, start_date, period)
        
        if stock_data.empty:
            return (ts_code, False, "无股票数据", pd.DataFrame())
        
        if len(stock_data) < period:
            return (ts_code, False, f"数据不足{period}个交易日", pd.DataFrame())
        
        # 计算ATR
        atr_data = calculate_atr(stock_data, period)
        
        if atr_data.empty:
            return (ts_code, False, "ATR计算失败", pd.DataFrame())
        
        # 过滤掉 atr 为空的行，并只返回新增的数据
        atr_data.dropna(subset=['atr'], inplace=True)
        new_data = atr_data[atr_data['trade_date'] >= start_date][['ts_code', 'trade_date', 'atr']].copy()
        
        return (ts_code, True, f"计算出{len(new_data)}条ATR数据", new_data)
        
    except Exception as e:
        return (ts_code, False, f"处理异常: {str(e)}", pd.DataFrame())

def save_atr_data_safe(all_atr_data, period=14):
    """直接保存ATR数据到数据库"""
    if all_atr_data.empty:
        return 0
    
    engine = None
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            engine = get_engine()
            all_atr_data['period'] = period
            all_atr_data['update_time'] = datetime.now()
            
            # 直接保存到atr_data表，使用replace方式处理重复数据
            saved_count = all_atr_data.to_sql(
                'atr_data', 
                engine, 
                if_exists='append', 
                index=False, 
                method='multi'
            )
            
            logger.info(f"成功保存{len(all_atr_data)}条ATR数据")
            return len(all_atr_data)
                
        except Exception as e:
            logger.warning(f"批量保存ATR数据失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
            else:
                logger.error(f"批量保存ATR数据最终失败，已重试{max_retries}次")
                return 0
        finally:
            if engine:
                try:
                    engine.dispose()
                except:
                    pass

def get_stocks_need_update(period=14):
    """获取需要更新ATR数据的股票"""
    engine = None
    try:
        engine = get_engine()
        logger.info("开始分析需要更新ATR数据的股票...")
        
        query = """
        SELECT 
            sb.ts_code,
            sf.latest_trade_date,
            atr.latest_atr_date
        FROM stock_basic sb
        LEFT JOIN (
            SELECT ts_code, MAX(trade_date) as latest_trade_date
            FROM stk_factor 
            GROUP BY ts_code
        ) sf ON sb.ts_code = sf.ts_code
        LEFT JOIN (
            SELECT ts_code, MAX(trade_date) as latest_atr_date
            FROM atr_data 
            WHERE period = %s
            GROUP BY ts_code
        ) atr ON sb.ts_code = atr.ts_code
        WHERE sb.list_status = 'L' 
        AND (sb.ts_code LIKE '%%.SZ' OR sb.ts_code LIKE '%%.SH')
        AND sf.latest_trade_date IS NOT NULL
        AND (atr.latest_atr_date IS NULL OR atr.latest_atr_date < sf.latest_trade_date)
        ORDER BY sb.ts_code
        """
        
        result_df = pd.read_sql(query, engine, params=(period,))
        
        stocks_need_update = result_df['ts_code'].tolist()
        no_atr_count = len(result_df[result_df['latest_atr_date'].isna()])
        outdated_count = len(result_df) - no_atr_count
        
        logger.info(f"需要更新ATR数据的股票数量: {len(stocks_need_update)}")
        logger.info(f"  - 完全没有ATR数据: {no_atr_count}只")
        logger.info(f"  - ATR数据需要更新: {outdated_count}只")
        
        return stocks_need_update
        
    except Exception as e:
        logger.error(f"获取需要更新的股票列表失败: {str(e)}")
        return []
    finally:
        if engine:
            try:
                engine.dispose()
            except:
                pass

def main():
    """主函数（单进程版）"""
    logger.info("开始增量计算A股ATR值（单进程版）...")
    
    period = 14
    
    # 获取需要更新的股票代码
    stocks_need_update = get_stocks_need_update(period)
    logger.info(f"需要更新ATR数据的股票数量: {len(stocks_need_update)}")
    
    if not stocks_need_update:
        logger.info("所有股票的ATR数据都是最新的，无需更新")
        return
    
    # 分批处理参数
    batch_size = BATCH_SIZE
    batch_count = (len(stocks_need_update) + batch_size - 1) // batch_size
    
    start_time = datetime.now()
    total_success = 0
    total_failed = 0
    total_new_records = 0
    
    logger.info(f"总共{len(stocks_need_update)}只股票，分{batch_count}批处理，每批{batch_size}只")
    
    for batch_num in range(batch_count):
        batch_start_idx = batch_num * batch_size
        batch_end_idx = min((batch_num + 1) * batch_size, len(stocks_need_update))
        batch_stocks = stocks_need_update[batch_start_idx:batch_end_idx]
        
        logger.info(f"\n=== 处理第{batch_num + 1}/{batch_count}批股票 ({len(batch_stocks)}只) ===")
        
        batch_atr_data = []
        batch_success = 0
        batch_failed = 0
        
        batch_start_time = time.time()
        
        # 使用单进程顺序处理当前批次
        for ts_code in batch_stocks:
            try:
                ts_code_result, success, message, atr_data = process_single_stock_safe(ts_code, period)
                
                if success:
                    batch_success += 1
                    if not atr_data.empty:
                        batch_atr_data.append(atr_data)
                        logger.info(f"✓ {ts_code}: {message}")
                    else:
                        logger.debug(f"✓ {ts_code}: {message}")
                else:
                    batch_failed += 1
                    logger.warning(f"✗ {ts_code}: {message}")
                    
            except Exception as e:
                batch_failed += 1
                logger.error(f"✗ {ts_code}: 处理异常: {str(e)}")
        
        # 批量保存当前批次的ATR数据
        batch_new_records = 0
        if batch_atr_data:
            logger.info(f"开始保存第{batch_num + 1}批ATR数据到数据库...")
            try:
                combined_atr_data = pd.concat(batch_atr_data, ignore_index=True)
                batch_new_records = save_atr_data_safe(combined_atr_data, period)
            except Exception as e:
                logger.error(f"第{batch_num + 1}批保存失败: {str(e)}")
        
        # 更新总计数
        total_success += batch_success
        total_failed += batch_failed
        total_new_records += batch_new_records
        
        # 计算批次耗时
        batch_duration = time.time() - batch_start_time
        
        # 显示当前批次进度
        logger.info(f"第{batch_num + 1}批完成: 成功{batch_success}只, 失败{batch_failed}只, 新增记录{batch_new_records}条, 耗时{batch_duration:.1f}秒")
        
        # 强制垃圾回收
        gc.collect()
        
        # 短暂休眠，让数据库恢复
        if batch_num < batch_count - 1:
            time.sleep(0.1)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    logger.info("=" * 60)
    logger.info("ATR增量计算完成!")
    logger.info(f"需要更新股票数: {len(stocks_need_update)}")
    logger.info(f"成功: {total_success}")
    logger.info(f"失败: {total_failed}")
    logger.info(f"新增ATR记录数: {total_new_records}")
    if len(stocks_need_update) > 0:
        logger.info(f"成功率: {total_success/len(stocks_need_update)*100:.1f}%")
    logger.info(f"总耗时: {duration}")
    if duration.total_seconds() > 0:
        logger.info(f"平均速度: {len(stocks_need_update)/duration.total_seconds()*60:.1f}只/分钟")
    logger.info("=" * 60)

if __name__ == '__main__':
    main()  