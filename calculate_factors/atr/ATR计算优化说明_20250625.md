# ATR计算脚本卡住问题分析与优化方案

## 问题现象
ATR增量计算脚本在执行过程中会卡住，从日志文件可以看到：
- 脚本执行到第767只股票（002263.SZ）后停止
- 总共需要处理5139只股票，只完成了约15%
- 执行时间约在2025-06-18 17:35:06左右停止

## 问题根因分析

### 1. 内存累积问题
原始脚本采用"先计算所有数据，再批量保存"的策略：
```python
all_new_atr_data = []
for i, ts_code in enumerate(stocks_need_update, 1):
    # 计算ATR数据
    new_data = atr_data[atr_data['trade_date'] >= start_date][['ts_code', 'trade_date', 'atr']].copy()
    all_new_atr_data.append(new_data)  # 累积在内存中

# 最后一次性保存所有数据
combined_atr_data = pd.concat(all_new_atr_data, ignore_index=True)
```

**问题**：
- 5139只股票的ATR数据全部累积在内存中
- 即使有24GB内存，当处理到几百只股票时，内存压力已经很大
- 可能触发内存不足或垃圾回收导致的停顿

### 2. 数据库连接和锁定问题
- 长时间持有数据库连接可能导致超时
- 批量保存大量数据时可能遇到表锁定
- 没有事务管理和重试机制

### 3. 单线程处理效率低
- M4 Pro有12个CPU核心，但原脚本只使用单线程
- 处理5000+股票的串行方式效率很低

## 优化方案

### 方案1：分批处理优化版（推荐）
文件：`atr_calculate_incremental.py`（已优化）

**核心改进**：
1. **分批处理**：每100只股票为一批，避免内存累积
2. **增强的数据库操作**：
   - 添加重试机制（最多3次）
   - 使用事务管理
   - 指数退避等待策略
3. **内存管理**：
   - 每批处理后强制垃圾回收
   - 分批保存数据到数据库
4. **更好的日志和错误处理**

**特点**：
- 稳定性高，不会因内存问题卡住
- 处理失败的批次不会影响其他批次
- 实时显示进度，便于监控

### 方案2：多进程优化版（性能最优）
文件：`atr_calculate_incremental_mp.py`（新创建）

**核心改进**：
1. **多进程并行计算**：充分利用M4 Pro的12核性能
2. **智能进程数控制**：使用8个进程（保留4核给系统）
3. **分批+并行**：每批160只股票，8进程并行处理
4. **连接池管理**：每个进程独立管理数据库连接

**特点**：
- 性能最高，预计速度提升5-8倍
- 充分利用多核CPU
- 适合大批量数据处理

## 运行建议

### 立即可用：分批处理版
```bash
python /Users/<USER>/Desktop/Quant_trade/light_sys_0.1/calculate_factors/atr/atr_calculate_incremental.py
```

### 高性能版本：多进程版
```bash
python /Users/<USER>/Desktop/Quant_trade/light_sys_0.1/calculate_factors/atr/atr_calculate_incremental_mp.py
```

## 配置调整

### run_tasks.py 配置更新
- 超时时间从1800秒增加到3600秒
- 确保有足够时间完成所有股票的处理

### 监控要点
1. **内存使用**：应该保持稳定，不会持续增长
2. **进度显示**：每批处理完成后都会显示进度
3. **错误处理**：失败的股票会记录但不会中断整体处理
4. **数据库保存**：分批保存，避免大事务

## 性能对比预估

| 版本 | 预估耗时 | 内存使用 | CPU利用率 | 稳定性 |
|------|----------|----------|-----------|--------|
| 原版本 | >2小时（卡住） | 持续增长 | 单核25% | 低 |
| 分批版 | 45-60分钟 | 稳定低位 | 单核90% | 高 |
| 多进程版 | 8-15分钟 | 稳定 | 多核80% | 高 |

## 总结
通过分批处理和多进程优化，解决了原脚本的内存累积和单线程瓶颈问题，大幅提升了处理效率和稳定性。建议优先使用分批处理版本进行验证，确认稳定后可切换到多进程版本获得最佳性能。 