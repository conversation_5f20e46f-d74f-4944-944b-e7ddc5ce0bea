#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析股票涨跌与主力资金/散户资金之间的关系
"""

import pandas as pd
import numpy as np
from sqlalchemy import create_engine
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import sys
import os

# 添加项目根目录到系统路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from get_data.db_config import DB_URL

def get_money_flow_data():
    """
    获取资金流向和股票涨跌数据
    """
    engine = create_engine(DB_URL)
    
    # 获取2025年之前的所有数据
    query = """
    SELECT 
        f.ts_code,
        f.trade_date,
        f.main_money_ratio,
        f.retail_money_ratio,
        s.pct_change
    FROM factors_money_focus_data f
    JOIN stk_factor s ON f.ts_code = s.ts_code 
        AND f.trade_date = s.trade_date
    WHERE f.trade_date >= '2025-01-01'
    """
    
    df = pd.read_sql(query, engine)
    return df

def analyze_money_flow():
    """
    分析资金流向与股票涨跌的关系
    """
    df = get_money_flow_data()
    
    # 1. 计算整体相关性
    correlation = df[['main_money_ratio', 'retail_money_ratio', 'pct_change']].corr()
    
    # 2. 按主力资金占比分组，计算各组的平均涨跌幅
    df['main_money_group'] = pd.qcut(df['main_money_ratio'], 5, labels=['很低', '低', '中等', '高', '很高'])
    group_stats = df.groupby('main_money_group')['pct_change'].agg(['mean', 'count']).round(4)
    
    # 3. 计算主力资金高低对次日涨跌的影响
    df['next_day_return'] = df.groupby('ts_code')['pct_change'].shift(-1)
    next_day_correlation = df[['main_money_ratio', 'next_day_return']].corr().iloc[0,1]
    
    # 4. 绘制分析图表
    plt.figure(figsize=(15, 10))
    
    # 4.1 主力资金占比与涨跌幅的散点图
    plt.subplot(2, 2, 1)
    plt.scatter(df['main_money_ratio'], df['pct_change'], alpha=0.1)
    plt.xlabel('主力资金占比')
    plt.ylabel('涨跌幅')
    plt.title('主力资金占比与涨跌幅的关系')
    
    # 4.2 各组平均涨跌幅对比
    plt.subplot(2, 2, 2)
    group_stats['mean'].plot(kind='bar')
    plt.title('不同主力资金占比组的平均涨跌幅')
    plt.xlabel('主力资金占比分组')
    plt.ylabel('平均涨跌幅')
    
    # 4.3 相关性热力图
    plt.subplot(2, 2, 3)
    sns.heatmap(correlation, annot=True, cmap='RdYlBu')
    plt.title('相关性热力图')
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(os.path.join(project_root, 'analysis/money_flow_analysis.png'))
    
    return {
        'correlation': correlation,
        'group_stats': group_stats,
        'next_day_correlation': next_day_correlation
    }

if __name__ == "__main__":
    results = analyze_money_flow()
    
    print("\n=== 资金流向分析结果 ===")
    print("\n1. 相关性分析:")
    print(results['correlation'])
    
    print("\n2. 主力资金分组统计:")
    print(results['group_stats'])
    
    print("\n3. 主力资金与次日收益相关性:", results['next_day_correlation'])
    
    print("\n分析图表已保存至 analysis/money_flow_analysis.png")
