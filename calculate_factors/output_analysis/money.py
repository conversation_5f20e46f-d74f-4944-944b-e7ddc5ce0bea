"""
@Author: Jiang<PERSON>in
@Date: 2025/2/25 19:08
@Description: 计算资金集中度以及交易活跃度
"""


from sqlalchemy import create_engine
import pandas as pd
import os
import datetime
import sys

# 添加项目根目录到Python路径
sys.path.append('/Users/<USER>/AI_QUANT/light_sys')


def money_focus():
    '''
    资金集中度
    公式：
    主力资金占比 = (特大单买入金额 + 大单买入金额) / 总买入金额
    散户资金占比 = 小单买入金额 / 总买入金额
    用途：识别资金主导方（散户 vs 机构），高主力占比可能预示后续行情。
    :return: DataFrame，包含ts_code、trade_date、主力资金占比、散户资金占比
    '''
    # 创建数据库连接
    from get_data.db_config import DB_URL
    engine = create_engine(DB_URL)
    
    # 获取数据库中最新的日期
    max_date_query = "SELECT MAX(trade_date) as max_date FROM factors_money_focus_data"
    max_date_df = pd.read_sql(max_date_query, engine)
    max_date = max_date_df['max_date'].iloc[0] if not max_date_df['max_date'].isnull().iloc[0] else '2025-01-01'
    
    # 获取当前日期
    current_date = datetime.datetime.now().strftime('%Y-%m-%d')
    
    # 查询资金流向数据
    query = f"""
    SELECT 
        ts_code,
        trade_date,
        buy_sm_amount,  -- 小单买入金额
        buy_md_amount,  -- 中单买入金额
        buy_lg_amount,  -- 大单买入金额
        buy_elg_amount  -- 特大单买入金额
    FROM stock_fund_flow WHERE trade_date > '{max_date}' AND trade_date <= '{current_date}'
    """
    
    # 读取数据
    df = pd.read_sql(query, engine)
    
    # 计算总买入金额
    df['total_buy_amount'] = df['buy_sm_amount'] + df['buy_md_amount'] + df['buy_lg_amount'] + df['buy_elg_amount']
    
    # 计算主力资金占比 = (特大单买入金额 + 大单买入金额) / 总买入金额
    df['main_money_ratio'] = (df['buy_elg_amount'] + df['buy_lg_amount']) / df['total_buy_amount']
    
    # 计算散户资金占比 = 小单买入金额 / 总买入金额
    df['retail_money_ratio'] = df['buy_sm_amount'] / df['total_buy_amount']
    
    # 选择需要的列
    result = df[['ts_code', 'trade_date', 'main_money_ratio', 'retail_money_ratio']]

    # 保存到数据库
    try:
        result.to_sql('factors_money_focus_data', engine, if_exists='append', index=False)
        print(f"成功将{len(result)}条数据写入数据库表factors_money_focus_data")
    except Exception as e:
        print(f"写入数据库时出错：{str(e)}")

    # 创建输出目录（保留CSV输出作为备份）
    # output_dir = 'output_analysis'
    # os.makedirs(output_dir, exist_ok=True)

    # 保存结果到CSV
    # output_file = os.path.join(output_dir, 'money_focus.csv')
    # result.to_csv(output_file, index=False, encoding='utf-8')

    # return result

if __name__ == '__main__':
    print("开始计算资金集中度...")
    result = money_focus()
    # print("\n数据预览：")
    # print(result.head())