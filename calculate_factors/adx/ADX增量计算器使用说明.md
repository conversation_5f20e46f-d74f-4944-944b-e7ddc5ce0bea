# ADX增量计算器使用说明

## 概述

ADX增量计算器是一个用于计算和保存个股ADX（Average Directional Index）指标的工具，支持每日增量更新，使用后复权价格进行计算。

## 功能特点

- ✅ **增量更新**: 支持每日增量更新，只计算新增的交易日数据
- ✅ **多进程并行**: 利用多核CPU并行处理，提高计算效率
- ✅ **数据库存储**: 自动保存计算结果到MySQL数据库
- ✅ **错误处理**: 完善的异常处理和日志记录
- ✅ **灵活配置**: 可配置计算周期、回看天数等参数
- ✅ **后复权价格**: 使用stk_factor表的后复权价格进行计算

## 数据库表结构

```sql
CREATE TABLE `adx_data` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `adx` decimal(10,4) NOT NULL COMMENT 'ADX值',
  `pdi` decimal(10,4) NOT NULL COMMENT '+DI值（正向指标）',
  `mdi` decimal(10,4) NOT NULL COMMENT '-DI值（负向指标）',
  `period` int NOT NULL COMMENT 'ADX计算周期（如14日）',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ts_code`,`trade_date`,`period`),
  KEY `idx_trade_date` (`trade_date`),
  KEY `idx_ts_code` (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='ADX因子数据表';
```

## 文件说明

### 1. `adx_incremental_calculator.py` - 主计算器
- **ADXIncrementalCalculator类**: 主要的计算器类
- **calculate_single_stock_adx函数**: 独立的单股计算函数（用于多进程）

### 2. `test_adx_calculator.py` - 测试工具
- 提供测试功能验证计算器的正确性
- 支持单股测试和增量更新测试

### 3. `ADX_corrected.py` - ADX计算核心
- 使用Wilder's平滑方法的标准ADX计算
- 更准确的ADX指标计算

## 使用方法

### 1. 基本使用

```python
from calculate_factors.adx.adx_incremental_calculator import ADXIncrementalCalculator

# 创建计算器实例
calculator = ADXIncrementalCalculator(
	period=14,  # ADX计算周期，默认14
	lookback_days=60  # 回看天数，默认60
)

# 运行增量更新（处理所有股票）
calculator.run_incremental_update()
```

### 2. 指定股票列表

```python
# 只处理指定的股票
test_stocks = ['000001.SZ', '000002.SZ', '600000.SH']

calculator.run_incremental_update(
    stock_codes=test_stocks,
    force_recalculate=False,  # 增量更新
    max_workers=4            # 并行进程数
)
```

### 3. 强制重新计算

```python
# 强制重新计算所有数据
calculator.run_incremental_update(
    force_recalculate=True  # 强制重新计算
)
```

### 4. 查看统计信息

```python
# 获取ADX数据统计信息
stats = calculator.get_adx_statistics()
```

## 命令行使用

### 直接运行主程序
```bash
cd big_a_compass
python adx_incremental_calculator.py
```

### 运行测试程序
```bash
cd big_a_compass
python test_adx_calculator.py
```

## 参数配置

### ADXIncrementalCalculator参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| period | int | 14 | ADX计算周期 |
| lookback_days | int | 60 | 数据回看天数 |

### run_incremental_update参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| stock_codes | list | None | 指定股票代码列表，None表示所有股票 |
| force_recalculate | bool | False | 是否强制重新计算 |
| max_workers | int | None | 最大并行进程数，None表示自动设置 |

## 增量更新逻辑

1. **首次运行**: 如果数据库中没有ADX数据，将计算过去60+14+30天的历史数据
2. **增量更新**: 
   - 获取每只股票最新的ADX数据日期
   - 从最新日期前14+5天开始重新计算（确保数据连续性）
   - 只保存比最新日期更新的数据
3. **数据覆盖**: 使用`ON DUPLICATE KEY UPDATE`避免重复数据

## 性能优化

### 1. 多进程并行
- 默认使用CPU核心数，最多限制为6个进程
- 每个进程独立处理一只股票，避免进程间竞争

### 2. 数据库连接管理
- 每个进程创建独立的数据库连接
- 及时释放连接，避免连接池耗尽

### 3. 内存优化
- 按股票逐个处理，避免一次性加载大量数据
- 及时清理中间变量

## 日志记录

- 日志文件: `adx_calculation.log`
- 日志级别: INFO
- 记录内容:
  - 处理进度
  - 成功/失败统计
  - 错误详情
  - 性能指标

## 错误处理

### 常见错误及解决方案

1. **数据库连接错误**
   - 检查`get_data/db_config.py`中的数据库配置
   - 确保MySQL服务正在运行

2. **表不存在错误**
   - 确保已创建`adx_data`表
   - 检查表结构是否正确

3. **股票数据不足**
   - 某些股票可能缺少足够的历史数据
   - 系统会自动跳过并记录警告

4. **多进程错误**
   - 减少`max_workers`参数
   - 检查系统资源使用情况

## 数据验证

### 1. 运行测试
```bash
python test_adx_calculator.py
```

### 2. 查询数据库验证
```sql
-- 查看ADX数据统计
SELECT 
    COUNT(DISTINCT ts_code) as stock_count,
    COUNT(*) as total_records,
    MIN(trade_date) as earliest_date,
    MAX(trade_date) as latest_date
FROM adx_data 
WHERE period = 14;

-- 查看某只股票的ADX数据
SELECT * FROM adx_data 
WHERE ts_code = '000001.SZ' AND period = 14 
ORDER BY trade_date DESC 
LIMIT 10;
```

## 定时任务设置

### 使用crontab设置每日自动更新

1. 编辑crontab
```bash
crontab -e
```

2. 添加定时任务（每个交易日下午5点执行）
```bash
0 17 * * 1-5 cd /path/to/your/project && /usr/bin/python3 big_a_compass/adx_incremental_calculator.py >> /var/log/adx_update.log 2>&1
```

### 使用systemd定时器

1. 创建服务文件
```bash
sudo vim /etc/systemd/system/adx-update.service
```

2. 添加内容
```ini
[Unit]
Description=ADX Incremental Update
After=network.target

[Service]
Type=oneshot
User=your_username
WorkingDirectory=/path/to/your/project
ExecStart=/usr/bin/python3 big_a_compass/adx_incremental_calculator.py
```

3. 创建定时器文件
```bash
sudo vim /etc/systemd/system/adx-update.timer
```

4. 添加内容
```ini
[Unit]
Description=Run ADX Update Daily
Requires=adx-update.service

[Timer]
OnCalendar=Mon-Fri 17:00
Persistent=true

[Install]
WantedBy=timers.target
```

5. 启用定时器
```bash
sudo systemctl enable adx-update.timer
sudo systemctl start adx-update.timer
```

## 监控和维护

### 1. 检查日志
```bash
tail -f adx_calculation.log
```

### 2. 监控数据库大小
```sql
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS "DB Size in MB"
FROM information_schema.tables
WHERE table_schema = "your_database_name" 
    AND table_name = "adx_data";
```

### 3. 数据清理（可选）
```sql
-- 删除过旧的数据（保留最近2年）
DELETE FROM adx_data 
WHERE trade_date < DATE_SUB(CURDATE(), INTERVAL 2 YEAR);
```

## 常见问题

**Q: 为什么某些股票没有ADX数据？**
A: 可能原因：股票数据不足、停牌、退市或数据质量问题。系统会自动跳过这些股票。

**Q: 如何修改ADX计算周期？**
A: 修改`ADXIncrementalCalculator`的`period`参数，注意不同周期的数据会分别存储。

**Q: 增量更新为什么还是很慢？**
A: 首次运行需要计算历史数据，后续增量更新会很快。可以适当增加`max_workers`参数。

**Q: 如何备份ADX数据？**
A: 使用mysqldump工具：
```bash
mysqldump -u username -p database_name adx_data > adx_backup.sql
```

## 技术支持

如有问题，请检查：
1. 日志文件 `adx_calculation.log`
2. 数据库连接配置
3. 必要的Python依赖包
4. 系统资源使用情况 