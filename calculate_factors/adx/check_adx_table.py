"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/7/2
@File: check_adx_table.py
@Version: 1.0
@Description: 检查ADX数据表中的重复或异常数据
"""

import pandas as pd
import pymysql
from sqlalchemy import create_engine, text
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['STHeiti']
plt.rcParams['axes.unicode_minus'] = False

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'oyvla6qe',
    'database': 'test',
    'port': 3306,
    'charset': 'utf8mb4'
}

def get_engine():
    DB_URL = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}&collation=utf8mb4_0900_ai_ci"
    engine = create_engine(DB_URL)
    return engine

def check_adx_duplicates():
    """检查ADX数据表中的重复数据"""
    engine = get_engine()
    
    print("=== ADX数据表重复数据检查报告 ===")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 检查表结构和基本信息
    print("1. 表基本信息:")
    basic_info_sql = """
    SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT ts_code) as unique_stocks,
        COUNT(DISTINCT trade_date) as unique_dates,
        COUNT(DISTINCT period) as unique_periods,
        MIN(trade_date) as min_date,
        MAX(trade_date) as max_date
    FROM adx_data
    """
    
    basic_info = pd.read_sql(basic_info_sql, engine)
    print(f"总记录数: {basic_info.iloc[0]['total_records']:,}")
    print(f"唯一股票数: {basic_info.iloc[0]['unique_stocks']}")
    print(f"唯一交易日数: {basic_info.iloc[0]['unique_dates']}")
    print(f"唯一周期数: {basic_info.iloc[0]['unique_periods']}")
    print(f"日期范围: {basic_info.iloc[0]['min_date']} 到 {basic_info.iloc[0]['max_date']}")
    print()
    
    # 2. 检查主键重复（理论上不应该存在）
    print("2. 主键重复检查:")
    primary_key_dup_sql = """
    SELECT ts_code, trade_date, period, COUNT(*) as count
    FROM adx_data
    GROUP BY ts_code, trade_date, period
    HAVING COUNT(*) > 1
    ORDER BY count DESC
    LIMIT 10
    """
    
    pk_duplicates = pd.read_sql(primary_key_dup_sql, engine)
    if len(pk_duplicates) > 0:
        print("发现主键重复数据:")
        print(pk_duplicates)
        print(f"主键重复记录总数: {len(pk_duplicates)}")
    else:
        print("✓ 无主键重复数据")
    print()
    
    # 3. 检查数据值异常重复（相同股票相同日期但不同周期的数据是否异常相似）
    print("3. 数据值异常重复检查:")
    value_similarity_sql = """
    SELECT 
        ts_code, 
        trade_date,
        COUNT(DISTINCT period) as period_count,
        COUNT(DISTINCT adx) as unique_adx,
        COUNT(DISTINCT pdi) as unique_pdi,
        COUNT(DISTINCT mdi) as unique_mdi,
        GROUP_CONCAT(DISTINCT period ORDER BY period) as periods,
        GROUP_CONCAT(DISTINCT adx ORDER BY period) as adx_values
    FROM adx_data
    GROUP BY ts_code, trade_date
    HAVING period_count > 1 
       AND (unique_adx = 1 OR unique_pdi = 1 OR unique_mdi = 1)
    ORDER BY period_count DESC, ts_code, trade_date
    LIMIT 20
    """
    
    value_duplicates = pd.read_sql(value_similarity_sql, engine)
    if len(value_duplicates) > 0:
        print("发现数据值异常重复（同一天同一股票不同周期的ADX值完全相同）:")
        for idx, row in value_duplicates.iterrows():
            print(f"股票: {row['ts_code']}, 日期: {row['trade_date']}, "
                  f"周期数: {row['period_count']}, 周期: {row['periods']}, "
                  f"ADX值: {row['adx_values']}")
        print(f"异常相似记录总数: {len(value_duplicates)}")
    else:
        print("✓ 无数据值异常重复")
    print()
    
    # 4. 检查某只股票某个周期是否有重复的交易日期
    print("4. 股票-周期维度重复日期检查:")
    date_dup_sql = """
    SELECT 
        ts_code, 
        period,
        COUNT(DISTINCT trade_date) as unique_dates,
        COUNT(*) as total_records
    FROM adx_data
    GROUP BY ts_code, period
    HAVING total_records > unique_dates
    ORDER BY (total_records - unique_dates) DESC
    LIMIT 10
    """
    
    date_duplicates = pd.read_sql(date_dup_sql, engine)
    if len(date_duplicates) > 0:
        print("发现同一股票同一周期有重复交易日期:")
        print(date_duplicates)
    else:
        print("✓ 无重复交易日期")
    print()
    
    # 5. 检查数据分布情况
    print("5. 数据分布统计:")
    distribution_sql = """
    SELECT 
        period,
        COUNT(*) as records,
        COUNT(DISTINCT ts_code) as stocks,
        AVG(adx) as avg_adx,
        MIN(adx) as min_adx,
        MAX(adx) as max_adx,
        AVG(pdi) as avg_pdi,
        AVG(mdi) as avg_mdi
    FROM adx_data
    GROUP BY period
    ORDER BY period
    """
    
    distribution = pd.read_sql(distribution_sql, engine)
    print("按周期分组的数据分布:")
    print(distribution.round(2))
    print()
    
    # 6. 检查异常ADX值
    print("6. 异常ADX值检查:")
    abnormal_values_sql = """
    SELECT 
        ts_code, 
        trade_date, 
        period,
        adx, 
        pdi, 
        mdi
    FROM adx_data
    WHERE adx < 0 OR adx > 100 OR pdi < 0 OR pdi > 100 OR mdi < 0 OR mdi > 100
    ORDER BY ts_code, trade_date
    LIMIT 20
    """
    
    abnormal_values = pd.read_sql(abnormal_values_sql, engine)
    if len(abnormal_values) > 0:
        print("发现异常ADX值（超出0-100范围）:")
        print(abnormal_values)
    else:
        print("✓ 无异常ADX值")
    print()
    
    # 7. 检查更新时间异常
    print("7. 更新时间异常检查:")
    update_time_sql = """
    SELECT 
        DATE(update_time) as update_date,
        COUNT(*) as records
    FROM adx_data
    GROUP BY DATE(update_time)
    ORDER BY update_date DESC
    LIMIT 10
    """
    
    update_times = pd.read_sql(update_time_sql, engine)
    print("最近更新时间分布:")
    print(update_times)
    print()
    
    # 8. 生成数据质量总结
    print("8. 数据质量总结:")
    
    # 计算数据完整性
    expected_records_sql = """
    SELECT 
        COUNT(DISTINCT ts_code) * COUNT(DISTINCT trade_date) * COUNT(DISTINCT period) as expected_max
    FROM adx_data
    """
    expected_max = pd.read_sql(expected_records_sql, engine).iloc[0]['expected_max']
    actual_records = basic_info.iloc[0]['total_records']
    completeness = (actual_records / expected_max) * 100 if expected_max > 0 else 0
    
    print(f"理论最大记录数: {expected_max:,}")
    print(f"实际记录数: {actual_records:,}")
    print(f"数据完整性: {completeness:.2f}%")
    
    # 检查是否有明显的数据质量问题
    quality_issues = []
    if len(pk_duplicates) > 0:
        quality_issues.append(f"主键重复: {len(pk_duplicates)}条")
    if len(value_duplicates) > 0:
        quality_issues.append(f"数据值异常重复: {len(value_duplicates)}条")
    if len(date_duplicates) > 0:
        quality_issues.append(f"日期重复: {len(date_duplicates)}条")
    if len(abnormal_values) > 0:
        quality_issues.append(f"异常数值: {len(abnormal_values)}条")
    
    if quality_issues:
        print("\n⚠️  发现的数据质量问题:")
        for issue in quality_issues:
            print(f"  - {issue}")
    else:
        print("\n✅ 数据质量良好，未发现明显问题")
    
    print(f"\n=== 检查完成 ===")
    
    # 关闭连接
    engine.dispose()
    
    return {
        'basic_info': basic_info,
        'pk_duplicates': pk_duplicates,
        'value_duplicates': value_duplicates,
        'date_duplicates': date_duplicates,
        'distribution': distribution,
        'abnormal_values': abnormal_values,
        'update_times': update_times
    }

if __name__ == "__main__":
    try:
        results = check_adx_duplicates()
        print("\n检查结果已生成完成!")
    except Exception as e:
        print(f"检查过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()