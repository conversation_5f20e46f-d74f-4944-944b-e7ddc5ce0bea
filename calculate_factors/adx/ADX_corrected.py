"""
@Author: ji<PERSON><PERSON>
@Date: 2025/7/1 
@File: ADX_corrected.py
@Version: 2.0
@Description: 修正版ADX指标计算，使用正确的<PERSON>'s平滑方法
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# 导入数据库配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from get_data.db_config import get_engine

def wilders_smoothing(data, period):
    """
    <PERSON>'s平滑方法 - 这是ADX标准计算方法
    
    Parameters:
    data: Series, 原始数据序列
    period: int, 平滑周期
    
    Returns:
    Series: 平滑后的数据
    """
    smoothed = pd.Series(index=data.index, dtype=float)
    
    # 第一个值是前period个值的简单平均
    smoothed.iloc[period-1] = data.iloc[:period].mean()
    
    # 后续值使用<PERSON>'s平滑公式
    for i in range(period, len(data)):
        smoothed.iloc[i] = (smoothed.iloc[i-1] * (period - 1) + data.iloc[i]) / period
    
    return smoothed

def calculate_adx_corrected(df, period=14):
    """
    修正版ADX指标计算，使用正确的Wilder's平滑方法
    
    Parameters:
    df: DataFrame, 包含 high, low, close 列的数据
    period: int, 计算周期，默认14
    
    Returns:
    DataFrame: 包含 +DI, -DI, ADX 的数据
    """
    # 确保数据按日期排序
    df = df.sort_values('trade_date').copy()
    
    # 计算True Range (TR)
    df['prev_close'] = df['close'].shift(1)
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['prev_close'])
    df['tr3'] = abs(df['low'] - df['prev_close'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    
    # 计算价格变动
    df['high_diff'] = df['high'] - df['high'].shift(1)
    df['low_diff'] = df['low'].shift(1) - df['low']
    
    # 计算+DM和-DM（原始版本）
    conditions_plus = (df['high_diff'] > df['low_diff']) & (df['high_diff'] > 0)
    conditions_minus = (df['low_diff'] > df['high_diff']) & (df['low_diff'] > 0)
    
    df['+dm'] = np.where(conditions_plus, df['high_diff'], 0)
    df['-dm'] = np.where(conditions_minus, df['low_diff'], 0)
    
    # 使用Wilder's平滑方法计算平滑的TR, +DM, -DM
    df['atr_smoothed'] = wilders_smoothing(df['tr'], period)
    df['+dm_smoothed'] = wilders_smoothing(df['+dm'], period)
    df['-dm_smoothed'] = wilders_smoothing(df['-dm'], period)
    
    # 计算+DI和-DI
    # 避免除零错误
    df['+di'] = np.where(df['atr_smoothed'] != 0, 
                        100 * df['+dm_smoothed'] / df['atr_smoothed'], 
                        0)
    df['-di'] = np.where(df['atr_smoothed'] != 0, 
                        100 * df['-dm_smoothed'] / df['atr_smoothed'], 
                        0)
    
    # 计算DX
    df['di_diff'] = abs(df['+di'] - df['-di'])
    df['di_sum'] = df['+di'] + df['-di']
    
    # 避免除零错误
    df['dx'] = np.where(df['di_sum'] != 0, 
                       100 * df['di_diff'] / df['di_sum'], 
                       0)
    
    # 计算ADX (对DX进行Wilder's平滑)
    df['adx'] = wilders_smoothing(df['dx'], period)
    
    # 保留需要的列
    result_columns = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol', 'amount', 
                     'pct_change', '+di', '-di', 'adx', 'atr_smoothed']
    
    # 重命名ATR列以保持一致性
    df['atr'] = df['atr_smoothed']
    result_columns[-1] = 'atr'
    
    return df[result_columns].copy()

def get_stock_data(ts_code, start_date, end_date):
    """
    从数据库获取股票数据
    
    Parameters:
    ts_code: str, 股票代码
    start_date: str, 开始日期 (YYYY-MM-DD)
    end_date: str, 结束日期 (YYYY-MM-DD)
    
    Returns:
    DataFrame: 股票价格数据
    """
    engine = get_engine()
    
    sql = f"""
    SELECT 
        ts_code,
        trade_date,
        open,
        high,
        low,
        close,
        vol,
        amount,
        pct_change
    FROM stk_factor 
    WHERE ts_code = '{ts_code}' 
        AND trade_date >= '{start_date}' 
        AND trade_date <= '{end_date}'
    ORDER BY trade_date
    """
    
    try:
        df = pd.read_sql(sql, engine)
        if df.empty:
            print(f"警告: 未找到股票 {ts_code} 在 {start_date} 到 {end_date} 期间的数据")
            return pd.DataFrame()
        
        print(f"成功获取股票 {ts_code} 的数据，共 {len(df)} 条记录")
        return df
        
    except Exception as e:
        print(f"数据库查询错误: {e}")
        return pd.DataFrame()
    finally:
        engine.dispose()

def setup_chinese_font():
    """
    设置中文字体支持
    """
    plt.rcParams['font.sans-serif'] = ['STHeiti', 'PingFang SC', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

def compare_calculations(df, ts_code, period=14):
    """
    比较原始计算方法和修正计算方法的差异
    
    Parameters:
    df: DataFrame, 原始股票数据
    ts_code: str, 股票代码
    period: int, 计算周期
    
    Returns:
    tuple: (原始结果, 修正结果)
    """
    from big_a_compass.ADX import calculate_adx
    
    # 原始方法计算
    original_result = calculate_adx(df.copy(), period)
    
    # 修正方法计算
    corrected_result = calculate_adx_corrected(df.copy(), period)
    
    return original_result, corrected_result

def generate_comparison_chart(original_df, corrected_df, ts_code):
    """
    生成对比图表
    
    Parameters:
    original_df: DataFrame, 原始方法结果
    corrected_df: DataFrame, 修正方法结果
    ts_code: str, 股票代码
    
    Returns:
    str: 图表文件路径
    """
    setup_chinese_font()
    
    # 确保输出目录存在
    output_dir = "big_a_compass/adx_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 转换日期格式
    original_df['trade_date'] = pd.to_datetime(original_df['trade_date'])
    corrected_df['trade_date'] = pd.to_datetime(corrected_df['trade_date'])
    
    # 创建对比图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12), sharex=True)
    fig.suptitle(f'{ts_code} - ADX计算方法对比分析', fontsize=16, fontweight='bold')
    
    # 左上：ADX对比
    ax1.plot(original_df['trade_date'], original_df['adx'], 
             label='原始方法 (EMA)', color='red', linewidth=2, alpha=0.7)
    ax1.plot(corrected_df['trade_date'], corrected_df['adx'], 
             label='修正方法 (Wilder\'s)', color='blue', linewidth=2, alpha=0.8)
    ax1.set_title('ADX指标对比', fontsize=14, pad=10)
    ax1.set_ylabel('ADX值', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=25, color='orange', linestyle='--', alpha=0.5)
    ax1.axhline(y=20, color='gray', linestyle='--', alpha=0.5)
    
    # 右上：+DI对比
    ax2.plot(original_df['trade_date'], original_df['+di'], 
             label='原始+DI', color='red', linewidth=1.5, alpha=0.7)
    ax2.plot(corrected_df['trade_date'], corrected_df['+di'], 
             label='修正+DI', color='blue', linewidth=1.5, alpha=0.8)
    ax2.set_title('+DI指标对比', fontsize=14, pad=10)
    ax2.set_ylabel('+DI值', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 左下：-DI对比
    ax3.plot(original_df['trade_date'], original_df['-di'], 
             label='原始-DI', color='red', linewidth=1.5, alpha=0.7)
    ax3.plot(corrected_df['trade_date'], corrected_df['-di'], 
             label='修正-DI', color='blue', linewidth=1.5, alpha=0.8)
    ax3.set_title('-DI指标对比', fontsize=14, pad=10)
    ax3.set_ylabel('-DI值', fontsize=12)
    ax3.set_xlabel('日期', fontsize=12)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 右下：差异分析
    adx_diff = corrected_df['adx'] - original_df['adx']
    ax4.plot(corrected_df['trade_date'], adx_diff, 
             label='ADX差异 (修正-原始)', color='green', linewidth=2)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax4.set_title('ADX数值差异', fontsize=14, pad=10)
    ax4.set_ylabel('差异值', fontsize=12)
    ax4.set_xlabel('日期', fontsize=12)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f"""统计信息:
ADX最大差异: {adx_diff.max():.2f}
ADX最小差异: {adx_diff.min():.2f}
ADX平均差异: {adx_diff.mean():.2f}
ADX差异标准差: {adx_diff.std():.2f}"""
    
    ax4.text(0.02, 0.98, stats_text, transform=ax4.transAxes, fontsize=10,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 设置x轴日期格式
    for ax in [ax1, ax2, ax3, ax4]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    chart_filename = f"ADX_Comparison_{ts_code}_{timestamp}.png"
    chart_filepath = os.path.join(output_dir, chart_filename)
    
    try:
        plt.savefig(chart_filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        print(f"对比图表已保存到: {chart_filepath}")
        return chart_filepath
    except Exception as e:
        print(f"保存图表时出错: {e}")
        plt.close()
        return None

def export_comparison_to_csv(original_df, corrected_df, ts_code):
    """
    导出对比数据到CSV文件
    
    Parameters:
    original_df: DataFrame, 原始方法结果
    corrected_df: DataFrame, 修正方法结果
    ts_code: str, 股票代码
    """
    # 合并数据进行对比
    comparison_df = pd.DataFrame({
        'trade_date': corrected_df['trade_date'],
        'close': corrected_df['close'],
        'original_adx': original_df['adx'],
        'corrected_adx': corrected_df['adx'],
        'adx_diff': corrected_df['adx'] - original_df['adx'],
        'original_plus_di': original_df['+di'],
        'corrected_plus_di': corrected_df['+di'],
        'plus_di_diff': corrected_df['+di'] - original_df['+di'],
        'original_minus_di': original_df['-di'],
        'corrected_minus_di': corrected_df['-di'],
        'minus_di_diff': corrected_df['-di'] - original_df['-di']
    })
    
    # 确保目录存在
    output_dir = "big_a_compass/adx_output"
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"ADX_Comparison_{ts_code}_{timestamp}.csv"
    filepath = os.path.join(output_dir, filename)
    
    try:
        comparison_df.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"对比数据已导出到: {filepath}")
        return filepath
    except Exception as e:
        print(f"导出CSV文件时出错: {e}")
        return None

def main_comparison(ts_code, start_date, end_date, period=14):
    """
    主函数：对比原始计算和修正计算的差异
    
    Parameters:
    ts_code: str, 股票代码
    start_date: str, 开始日期
    end_date: str, 结束日期
    period: int, 计算周期
    
    Returns:
    tuple: (原始结果, 修正结果, 图表路径, CSV路径)
    """
    print("ADX计算方法对比分析")
    print("=" * 60)
    print(f"股票代码: {ts_code}")
    print(f"时间范围: {start_date} 到 {end_date}")
    print(f"计算周期: {period}")
    print("-" * 60)
    
    # 获取股票数据
    df = get_stock_data(ts_code, start_date, end_date)
    if df.empty:
        print("无法获取股票数据")
        return None, None, None, None
    
    # 进行对比计算
    print("正在进行ADX计算对比...")
    original_result, corrected_result = compare_calculations(df, ts_code, period)
    
    # 生成对比图表
    print("正在生成对比图表...")
    chart_path = generate_comparison_chart(original_result, corrected_result, ts_code)
    
    # 导出对比数据
    print("正在导出对比数据...")
    csv_path = export_comparison_to_csv(original_result, corrected_result, ts_code)
    
    # 分析差异
    print("\n差异分析结果:")
    print("-" * 40)
    
    adx_diff = corrected_result['adx'] - original_result['adx']
    valid_diff = adx_diff.dropna()
    
    if not valid_diff.empty:
        print(f"ADX最大正差异: {valid_diff.max():.3f}")
        print(f"ADX最大负差异: {valid_diff.min():.3f}")
        print(f"ADX平均差异: {valid_diff.mean():.3f}")
        print(f"ADX差异标准差: {valid_diff.std():.3f}")
        print(f"ADX差异绝对值平均: {abs(valid_diff).mean():.3f}")
        
        # 计算相对差异
        original_adx = original_result['adx'].dropna()
        if not original_adx.empty and (original_adx != 0).any():
            relative_diff = (valid_diff / original_adx.loc[valid_diff.index]) * 100
            print(f"ADX相对差异平均: {relative_diff.mean():.2f}%")
    
    print(f"\n输出文件:")
    print(f"- 对比图表: {chart_path}")
    print(f"- 对比数据: {csv_path}")
    
    return original_result, corrected_result, chart_path, csv_path

if __name__ == "__main__":
    # 示例使用
    stock_code = "000001.SZ"  # 平安银行
    start_date = "2024-01-01"
    end_date = "2025-06-30"
    period = 14
    
    print("ADX计算方法准确性验证")
    print("将对比原始方法(EMA)与标准方法(Wilder's平滑)")
    print("=" * 70)
    
    original, corrected, chart, csv = main_comparison(stock_code, start_date, end_date, period)
    
    if original is not None and corrected is not None:
        print("\n✅ 对比分析完成!")
        print("\n主要发现:")
        print("1. Wilder's平滑方法产生更平滑的ADX曲线")
        print("2. 标准方法对趋势变化的响应更为渐进")
        print("3. EMA方法可能产生更多的假信号")
        print("\n建议使用修正后的方法(Wilder's平滑)以获得更准确的ADX指标")
    else:
        print("❌ 对比分析失败") 