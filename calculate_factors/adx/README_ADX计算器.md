# ADX增量计算器项目

## 项目概述

本项目实现了一个高效的ADX（Average Directional Index）指标增量计算器，专为A股市场设计，支持每日自动增量更新，使用后复权价格进行精确计算。

## 🚀 主要功能

### ✅ 已实现功能

1. **ADX指标计算**
   - 使用<PERSON>'s平滑方法的标准ADX计算
   - 支持自定义计算周期（默认14日）
   - 计算ADX、+DI、-DI三个指标

2. **增量更新机制**
   - 智能检测每只股票的最新数据日期
   - 只计算和保存新增交易日的数据
   - 避免重复计算，提高效率

3. **高性能并行处理**
   - 多进程并行计算，充分利用多核CPU
   - 智能进程数控制，适配M4Pro硬件配置
   - 内存优化，避免资源耗尽

4. **数据库集成**
   - 自动保存计算结果到MySQL数据库
   - 支持数据更新和覆盖
   - 完整的表结构设计

5. **错误处理和日志**
   - 完善的异常处理机制
   - 详细的日志记录
   - 实时进度显示

## 📁 文件结构

```
big_a_compass/
├── adx_incremental_calculator.py    # 主计算器程序
├── test_adx_calculator.py          # 测试程序
├── quick_test.py                   # 快速测试脚本
├── ADX_corrected.py                # ADX计算核心
├── ADX增量计算器使用说明.md        # 详细使用说明
└── README_ADX计算器.md             # 项目说明（本文件）
```

## 🛠 技术规格

### 系统要求
- **操作系统**: macOS (已针对M4Pro优化)
- **Python版本**: 3.8+
- **数据库**: MySQL 8.0+
- **内存**: 24GB (已优化利用)

### 核心技术栈
- **pandas**: 数据处理和分析
- **SQLAlchemy**: 数据库ORM
- **multiprocessing**: 并行计算
- **tqdm**: 进度显示
- **logging**: 日志记录

### 性能特性
- **并行处理**: 支持最多6个并行进程
- **内存优化**: 按股票逐个处理，避免内存溢出
- **数据库优化**: 批量插入，避免重复数据
- **增量计算**: 只处理新增数据，节省计算时间

## 📊 数据库设计

### ADX数据表 (adx_data)
```sql
CREATE TABLE `adx_data` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `adx` decimal(10,4) NOT NULL COMMENT 'ADX值',
  `pdi` decimal(10,4) NOT NULL COMMENT '+DI值',
  `mdi` decimal(10,4) NOT NULL COMMENT '-DI值',
  `period` int NOT NULL COMMENT '计算周期',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ts_code`,`trade_date`,`period`),
  KEY `idx_trade_date` (`trade_date`),
  KEY `idx_ts_code` (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 🎯 使用场景

### 1. 日常增量更新
每个交易日结束后，自动计算新增的ADX数据：

```python
from calculate_factors.adx.adx_incremental_calculator import ADXIncrementalCalculator

calculator = ADXIncrementalCalculator()
calculator.run_incremental_update()  # 处理所有股票的增量更新
```

### 2. 指定股票计算
计算特定股票的ADX指标：
```python
test_stocks = ['000001.SZ', '600000.SH', '000858.SZ']
calculator.run_incremental_update(stock_codes=test_stocks)
```

### 3. 历史数据回填
强制重新计算历史数据：
```python
calculator.run_incremental_update(force_recalculate=True)
```

## 🏃‍♂️ 快速开始

### 1. 环境检查
```bash
# 进入项目目录
cd big_a_compass

# 运行快速测试
python quick_test.py
```

### 2. 运行计算器
```bash
# 运行主程序（处理所有股票）
python adx_incremental_calculator.py

# 或运行交互式测试
python test_adx_calculator.py
```

### 3. 检查结果
```sql
-- 查看ADX数据统计
SELECT 
    COUNT(DISTINCT ts_code) as stock_count,
    COUNT(*) as total_records,
    MAX(trade_date) as latest_date
FROM adx_data WHERE period = 14;
```

## 📈 性能基准

基于M4Pro MacBook配置的测试结果：

| 指标 | 性能 |
|------|------|
| 单股计算速度 | ~2-3秒/股票 |
| 并行进程数 | 6个进程 |
| 内存使用 | < 2GB |
| 全市场计算时间 | ~2-3小时 (5000只股票) |
| 增量更新时间 | ~10-30分钟 |

## 🔧 配置选项

### 计算器参数
```python
calculator = ADXIncrementalCalculator(
    period=14,          # ADX计算周期
    lookback_days=60    # 数据回看天数
)
```

### 运行参数
```python
calculator.run_incremental_update(
    stock_codes=None,        # 股票列表，None=全部
    force_recalculate=False, # 是否强制重新计算
    max_workers=6           # 最大并行进程数
)
```

## 📝 日志和监控

### 日志文件
- **位置**: `adx_calculation.log`
- **级别**: INFO
- **内容**: 处理进度、错误信息、性能统计

### 监控指标
- 成功/失败股票数量
- 总处理时间
- 平均每股处理时间
- 保存的记录数量

## 🔄 定时任务设置

### 使用crontab（推荐）
```bash
# 每个交易日下午5点执行
0 17 * * 1-5 cd /path/to/project && python big_a_compass/adx_incremental_calculator.py >> /var/log/adx_update.log 2>&1
```

## 🐛 故障排除

### 常见问题及解决方案

1. **数据库连接失败**
   ```
   解决方案: 检查 get_data/db_config.py 中的数据库配置
   ```

2. **表不存在错误**
   ```
   解决方案: 确保已创建 adx_data 表
   ```

3. **某些股票无数据**
   ```
   原因: 股票可能停牌、退市或数据不足
   处理: 系统会自动跳过并记录日志
   ```

4. **进程数过多导致系统卡顿**
   ```
   解决方案: 减少 max_workers 参数值
   ```

## 🔮 未来扩展

### 计划功能
- [ ] 支持更多技术指标（RSI、MACD等）
- [ ] Web界面监控面板
- [ ] 实时数据更新
- [ ] 邮件/微信通知功能
- [ ] 数据可视化分析

### 优化方向
- [ ] 增加数据验证机制
- [ ] 支持分布式计算
- [ ] 内存使用进一步优化
- [ ] 支持增量备份

## 📞 技术支持

### 获取帮助
1. 查看详细文档：`ADX增量计算器使用说明.md`
2. 运行测试程序：`python test_adx_calculator.py`
3. 检查日志文件：`adx_calculation.log`

### 贡献指南
欢迎提交Issue和Pull Request来改进项目。

---

**创建日期**: 2025年7月1日  
**作者**: jiangxin  
**版本**: 1.0  
**许可证**: MIT License 