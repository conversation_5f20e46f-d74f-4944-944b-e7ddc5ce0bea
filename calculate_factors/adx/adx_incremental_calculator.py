"""
@Author: ji<PERSON><PERSON>
@Date: 2025/7/1 
@File: adx_incremental_calculator.py
@Version: 1.0
@Description: ADX指标增量计算器，支持每日增量更新ADX数据到数据库
"""

import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
from tqdm import tqdm
import logging
from sqlalchemy import text

# 导入数据库配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from get_data.db_config import get_engine

# 导入修正版ADX计算函数
from calculate_factors.adx.ADX_corrected import calculate_adx_corrected

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('adx_calculation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def calculate_single_stock_adx(ts_code, period=14, lookback_days=60, force_recalculate=False):
    """
    独立函数：计算单只股票的ADX指标（用于多进程）
    
    Parameters:
    ts_code: str, 股票代码
    period: int, ADX计算周期
    lookback_days: int, 回看天数
    force_recalculate: bool, 是否强制重新计算
    
    Returns:
    tuple: (ts_code, success, message, data_count)
    """
    try:
        # 在每个进程中创建新的数据库连接
        engine = get_engine()
        
        # 获取当前日期
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        # 获取最新ADX数据日期
        def get_latest_adx_date():
            try:
                sql = f"""
                SELECT MAX(trade_date) as latest_date
                FROM adx_data 
                WHERE ts_code = '{ts_code}' AND period = {period}
                """
                df = pd.read_sql(sql, engine)
                latest_date = df['latest_date'].iloc[0]
                if pd.isna(latest_date):
                    return None
                # 确保返回datetime对象
                if isinstance(latest_date, str):
                    return datetime.strptime(latest_date, '%Y-%m-%d')
                elif hasattr(latest_date, 'strftime'):
                    return latest_date if isinstance(latest_date, datetime) else datetime.combine(latest_date, datetime.min.time())
                else:
                    return None
            except:
                return None
        
        # 确定数据获取的起始日期
        if force_recalculate:
            start_date = (datetime.now() - timedelta(days=lookback_days + period + 30)).strftime('%Y-%m-%d')
        else:
            latest_adx_date = get_latest_adx_date()
            if latest_adx_date is None:
                start_date = (datetime.now() - timedelta(days=lookback_days + period + 30)).strftime('%Y-%m-%d')
            else:
                start_date = (latest_adx_date - timedelta(days=period+30)).strftime('%Y-%m-%d')
        
        # 获取股票价格数据
        sql = f"""
        SELECT 
            ts_code,
            trade_date,
            open_hfq as open,
            high_hfq as high,
            low_hfq as low,
            close_hfq as close,
            vol,
            amount,
            pct_change
        FROM stk_factor 
        WHERE ts_code = '{ts_code}' 
            AND trade_date >= '{start_date}' 
            AND trade_date <= '{current_date}'
        ORDER BY trade_date
        """
        
        price_data = pd.read_sql(sql, engine)
        
        if price_data.empty:
            engine.dispose()
            return (ts_code, False, "无价格数据", 0)
        
        if len(price_data) < period + 10:
            engine.dispose()
            return (ts_code, False, f"数据不足，仅有{len(price_data)}条记录", 0)
        
        # 计算ADX指标
        adx_result = calculate_adx_corrected(price_data, period)
        
        if adx_result.empty:
            engine.dispose()
            return (ts_code, False, "ADX计算失败", 0)
        
        # 过滤出有效的ADX数据
        valid_adx = adx_result.dropna(subset=['adx', '+di', '-di']).copy()
        
        if valid_adx.empty:
            engine.dispose()
            return (ts_code, False, "无有效ADX数据", 0)
        
        # 只保留新数据
        if not force_recalculate:
            latest_adx_date = get_latest_adx_date()
            if latest_adx_date is not None:
                latest_date_str = latest_adx_date.strftime('%Y-%m-%d')
                # 确保trade_date列为字符串类型进行比较
                valid_adx = valid_adx[valid_adx['trade_date'].astype(str) > latest_date_str]
        
        if valid_adx.empty:
            engine.dispose()
            return (ts_code, True, "无新数据需要更新", 0)
        
        # 保存数据
        save_data = valid_adx[['ts_code', 'trade_date', '+di', '-di', 'adx']].copy()
        save_data['period'] = period
        save_data['update_time'] = datetime.now()
        
        # 重命名列
        save_data = save_data.rename(columns={'+di': 'pdi', '-di': 'mdi'})
        
        # 数据类型转换
        save_data['adx'] = save_data['adx'].round(4)
        save_data['pdi'] = save_data['pdi'].round(4)
        save_data['mdi'] = save_data['mdi'].round(4)
        
        # 保存到数据库
        saved_count = 0
        with engine.connect() as connection:
            with connection.begin():
                for _, row in save_data.iterrows():
                    sql = text("""
                    INSERT INTO adx_data (ts_code, trade_date, adx, pdi, mdi, period, update_time)
                    VALUES (:ts_code, :trade_date, :adx, :pdi, :mdi, :period, :update_time)
                    ON DUPLICATE KEY UPDATE
                    adx = VALUES(adx),
                    pdi = VALUES(pdi),
                    mdi = VALUES(mdi),
                    update_time = VALUES(update_time)
                    """)
                    
                    connection.execute(sql, {
                        'ts_code': row['ts_code'],
                        'trade_date': row['trade_date'],
                        'adx': float(row['adx']),
                        'pdi': float(row['pdi']),
                        'mdi': float(row['mdi']),
                        'period': int(row['period']),
                        'update_time': row['update_time']
                    })
                    saved_count += 1
        
        engine.dispose()
        return (ts_code, True, f"成功计算并保存", saved_count)
        
    except Exception as e:
        try:
            engine.dispose()
        except:
            pass
        return (ts_code, False, f"计算异常: {str(e)}", 0)

class ADXIncrementalCalculator:
    """ADX增量计算器"""
    
    def __init__(self, period=14, lookback_days=60):
        """
        初始化ADX计算器
        
        Parameters:
        period: int, ADX计算周期，默认14
        lookback_days: int, 回看天数，默认60个交易日
        """
        self.period = period
        self.lookback_days = lookback_days
        self.engine = get_engine()
        
    def get_all_stock_codes(self):
        """
        获取所有活跃上市的A股股票代码
        
        Returns:
        list: 股票代码列表
        """
        try:
            sql = """
            SELECT ts_code 
            FROM stock_basic 
            WHERE list_status = 'L' 
            AND (ts_code LIKE '%%.SZ' OR ts_code LIKE '%%.SH')
            AND name NOT LIKE '%%ST%%'
            AND name NOT LIKE '%%*ST%%'
            ORDER BY ts_code
            """
            
            df = pd.read_sql(sql, self.engine)
            stock_codes = df['ts_code'].tolist()
            logger.info(f"获取到 {len(stock_codes)} 只股票代码")
            return stock_codes
            
        except Exception as e:
            logger.error(f"获取股票代码失败: {e}")
            return []
    
    def run_incremental_update(self, stock_codes=None, force_recalculate=False, max_workers=None):
        """
        运行增量更新
        
        Parameters:
        stock_codes: list, 指定的股票代码列表，为None时处理所有股票
        force_recalculate: bool, 是否强制重新计算
        max_workers: int, 最大并行工作数
        """
        logger.info("开始ADX增量更新")
        logger.info(f"计算周期: {self.period}, 回看天数: {self.lookback_days}")
        
        # 获取股票列表
        if stock_codes is None:
            stock_codes = self.get_all_stock_codes()
        
        if not stock_codes:
            logger.error("未获取到股票代码")
            return
        
        logger.info(f"将处理 {len(stock_codes)} 只股票")
        
        # 设置并行工作数
        if max_workers is None:
            max_workers = min(mp.cpu_count(), 6)  # 限制最大进程数
        
        logger.info(f"使用 {max_workers} 个并行进程")
        
        # 统计结果
        success_count = 0
        fail_count = 0
        total_saved = 0
        
        start_time = time.time()
        
        # 使用ProcessPoolExecutor进行多进程处理
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_stock = {
                executor.submit(
                    calculate_single_stock_adx, 
                    ts_code, 
                    self.period, 
                    self.lookback_days, 
                    force_recalculate
                ): ts_code for ts_code in stock_codes
            }
            
            # 处理结果
            with tqdm(total=len(stock_codes), desc="计算ADX") as pbar:
                for future in as_completed(future_to_stock):
                    ts_code, success, message, saved_count = future.result()
                    
                    if success:
                        success_count += 1
                        total_saved += saved_count
                    else:
                        fail_count += 1
                        logger.warning(f"{ts_code}: {message}")
                    
                    pbar.update(1)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 输出统计信息
        logger.info("=" * 50)
        logger.info("ADX增量更新完成")
        logger.info(f"总处理股票数: {len(stock_codes)}")
        logger.info(f"成功: {success_count}")
        logger.info(f"失败: {fail_count}")
        logger.info(f"总保存记录数: {total_saved}")
        logger.info(f"总耗时: {elapsed_time:.2f} 秒")
        logger.info(f"平均每只股票: {elapsed_time/len(stock_codes):.2f} 秒")
        logger.info("=" * 50)
    
    def get_adx_statistics(self):
        """
        获取ADX数据统计信息
        
        Returns:
        dict: 统计信息
        """
        try:
            sql = f"""
            SELECT 
                COUNT(DISTINCT ts_code) as stock_count,
                COUNT(*) as total_records,
                MIN(trade_date) as earliest_date,
                MAX(trade_date) as latest_date,
                MAX(update_time) as last_update_time
            FROM adx_data 
            WHERE period = {self.period}
            """
            
            df = pd.read_sql(sql, self.engine)
            
            if not df.empty:
                stats = df.iloc[0].to_dict()
                logger.info("ADX数据统计:")
                logger.info(f"  股票数量: {stats['stock_count']}")
                logger.info(f"  总记录数: {stats['total_records']}")
                logger.info(f"  最早日期: {stats['earliest_date']}")
                logger.info(f"  最新日期: {stats['latest_date']}")
                logger.info(f"  最后更新: {stats['last_update_time']}")
                return stats
            else:
                logger.info("暂无ADX数据")
                return {}
                
        except Exception as e:
            logger.error(f"获取ADX统计信息失败: {e}")
            return {}

def main():
    """主函数"""
    print("ADX增量计算器")
    print("=" * 50)
    
    # 创建计算器实例
    calculator = ADXIncrementalCalculator(period=14, lookback_days=60)
    
    # 显示当前统计信息
    calculator.get_adx_statistics()
    
    # 运行增量更新
    calculator.run_incremental_update(
        stock_codes=None,  # 处理所有股票
        force_recalculate=False,  # 增量更新模式
        max_workers=6  # 使用6个并行进程
    )
    
    # 显示更新后的统计信息
    calculator.get_adx_statistics()

if __name__ == "__main__":
    main() 