"""
@Author: ji<PERSON><PERSON>
@Date: 2025/7/2
@File: adx_stock_selector.py
@Version: 1.0
@Description: 基于ADX指标的选股器，筛选ADX小于50且最近5个交易日ADX值都上涨的股票
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from sqlalchemy import text
import sys
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['STHeiti']
plt.rcParams['axes.unicode_minus'] = False

# 导入数据库配置
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from get_data.db_config import get_engine

class ADXStockSelector:
    """ADX指标选股器"""
    
    def __init__(self, period=14):
        """
        初始化ADX选股器
        
        Parameters:
        period: int, ADX计算周期，默认14
        """
        self.period = period
        self.engine = get_engine()
        
    def get_adx_uptrend_stocks(self, max_adx=50, uptrend_days=5, min_adx=20):
        """
        筛选符合条件的股票
        
        Parameters:
        max_adx: float, ADX最大值阈值（默认50）
        uptrend_days: int, 连续上涨的交易日数（默认5天）
        min_adx: float, ADX最小值阈值（默认20，确保有一定趋势强度）
        
        Returns:
        DataFrame: 符合条件的股票列表
        """
        print("正在筛选ADX指标符合条件的股票...")
        print("条件：最新ADX < " + str(max_adx) + "，且ADX > " + str(min_adx) + "，最近" + str(uptrend_days) + "个交易日ADX连续上涨")
        print("-" * 60)
        
        # 获取最新交易日期
        latest_date_sql = text("""
        SELECT MAX(trade_date) as latest_date
        FROM adx_data 
        WHERE period = :period
        """)
        
        latest_date_df = pd.read_sql(latest_date_sql, self.engine, params={'period': self.period})
        latest_date = latest_date_df.iloc[0]['latest_date']
        
        if latest_date is None:
            print("❌ 数据库中没有找到ADX数据")
            return pd.DataFrame()
        
        print("最新交易日期: " + str(latest_date))
        
        # 计算需要回看的天数（考虑周末和节假日，多取一些天数）
        lookback_days = uptrend_days + 10  # 额外增加天数确保有足够的交易日
        start_date = latest_date - timedelta(days=lookback_days)
        
        print("数据查询范围: " + str(start_date) + " 到 " + str(latest_date))
        
        # 获取所有股票的ADX数据
        sql = text("""
        WITH stock_adx AS (
            SELECT 
                a.ts_code,
                a.trade_date,
                a.adx,
                a.pdi,
                a.mdi,
                s.name as stock_name,
                s.industry
            FROM adx_data a
            LEFT JOIN stock_basic s ON a.ts_code = s.ts_code
            WHERE a.period = :period
                AND a.trade_date >= :start_date
                AND a.trade_date <= :latest_date
                AND s.list_status = 'L'
                AND (a.ts_code LIKE '%.SZ' OR a.ts_code LIKE '%.SH')
                AND s.name NOT LIKE '%ST%'
                AND s.name NOT LIKE '%*ST%'
            ORDER BY a.ts_code, a.trade_date
        )
        SELECT *
        FROM stock_adx
        """)
        
        df = pd.read_sql(sql, self.engine, params={
            'period': self.period,
            'start_date': start_date,
            'latest_date': latest_date
        })
        
        if df.empty:
            print("❌ 没有找到ADX数据")
            return pd.DataFrame()
        
        print("获取到 " + str(df['ts_code'].nunique()) + " 只股票的ADX数据，共 " + str(len(df)) + " 条记录")
        
        # 筛选符合条件的股票
        selected_stocks = []
        
        for ts_code in df['ts_code'].unique():
            stock_data = df[df['ts_code'] == ts_code].sort_values('trade_date').reset_index(drop=True)
            
            # 确保有足够的数据
            if len(stock_data) < uptrend_days + 1:
                continue
            
            # 获取最新的ADX值
            latest_adx = stock_data.iloc[-1]['adx']
            
            # 检查最新ADX是否在合理范围内
            if latest_adx >= max_adx or latest_adx <= min_adx:
                continue
            
            # 检查最近N个交易日是否连续上涨
            recent_data = stock_data.tail(uptrend_days + 1)  # 需要N+1个点来计算N个涨跌
            
            if len(recent_data) < uptrend_days + 1:
                continue
            
            # 计算ADX变化
            adx_changes = recent_data['adx'].diff().dropna()
            
            # 检查是否连续上涨（所有变化值都大于0）
            if len(adx_changes) >= uptrend_days and all(adx_changes.tail(uptrend_days) > 0):
                stock_info = stock_data.iloc[-1].copy()
                
                # 计算额外的统计信息
                adx_increase = recent_data['adx'].iloc[-1] - recent_data['adx'].iloc[0]
                avg_daily_increase = adx_increase / uptrend_days
                
                # 计算趋势强度指标
                di_diff = abs(stock_info['pdi'] - stock_info['mdi'])
                trend_direction = '上涨趋势' if stock_info['pdi'] > stock_info['mdi'] else '下跌趋势'
                
                selected_stocks.append({
                    'ts_code': stock_info['ts_code'],
                    'stock_name': stock_info['stock_name'],
                    'industry': stock_info['industry'],
                    'latest_date': stock_info['trade_date'],
                    'latest_adx': round(latest_adx, 2),
                    'pdi': round(stock_info['pdi'], 2),
                    'mdi': round(stock_info['mdi'], 2),
                    'di_diff': round(di_diff, 2),
                    'trend_direction': trend_direction,
                    str(uptrend_days) + '日ADX涨幅': round(adx_increase, 2),
                    '日均ADX涨幅': round(avg_daily_increase, 2),
                    '连续上涨天数': uptrend_days
                })
        
        result_df = pd.DataFrame(selected_stocks)
        
        if result_df.empty:
            print("❌ 没有找到符合条件的股票")
            return pd.DataFrame()
        
        # 按ADX值排序
        result_df = result_df.sort_values('latest_adx', ascending=False)
        
        print("✅ 找到 " + str(len(result_df)) + " 只符合条件的股票")
        
        return result_df
    
    def save_results(self, df, filename_prefix="adx_selected_stocks"):
        """
        保存筛选结果到CSV文件
        
        Parameters:
        df: DataFrame, 筛选结果
        filename_prefix: str, 文件名前缀
        
        Returns:
        str: 保存的文件路径
        """
        if df.empty:
            print("❌ 没有数据需要保存")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = filename_prefix + "_" + timestamp + ".csv"
        
        # 确保输出目录存在
        output_dir = "big_a_compass/adx_output"
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        print("✅ 结果已保存到: " + filepath)
        return filepath
    
    def display_results(self, df, top_n=20):
        """
        展示筛选结果
        
        Parameters:
        df: DataFrame, 筛选结果
        top_n: int, 显示前N只股票
        """
        if df.empty:
            print("❌ 没有结果可显示")
            return
        
        print("\n📊 ADX指标选股结果 (前" + str(min(top_n, len(df))) + "只)")
        print("=" * 120)
        
        # 格式化显示
        display_df = df.head(top_n).copy()
        
        # 重新排列列的顺序，使显示更清晰
        columns_order = [
            'ts_code', 'stock_name', 'industry', 'latest_adx', 
            'pdi', 'mdi', 'trend_direction', '5日ADX涨幅', 
            '日均ADX涨幅', 'latest_date'
        ]
        
        # 确保所有列都存在
        display_columns = [col for col in columns_order if col in display_df.columns]
        display_df = display_df[display_columns]
        
        # 设置显示选项
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 15)
        
        print(display_df.to_string(index=False))
        
        # 显示汇总统计
        print("\n📈 汇总统计:")
        print("-" * 40)
        print("总计选中股票数: " + str(len(df)))
        print("平均ADX值: " + "{:.2f}".format(df['latest_adx'].mean()))
        print("ADX值范围: " + "{:.2f}".format(df['latest_adx'].min()) + " - " + "{:.2f}".format(df['latest_adx'].max()))
        print("平均5日ADX涨幅: " + "{:.2f}".format(df['5日ADX涨幅'].mean()))
        
        # 按行业分组统计
        if 'industry' in df.columns:
            industry_stats = df.groupby('industry').size().sort_values(ascending=False)
            print("\n🏭 行业分布 (前5):")
            print(industry_stats.head().to_string())
    
    def plot_adx_trends(self, stock_codes, days=30):
        """
        绘制选中股票的ADX趋势图
        
        Parameters:
        stock_codes: list, 股票代码列表
        days: int, 显示的天数
        """
        if not stock_codes:
            print("❌ 没有股票代码可绘制")
            return
        
        # 限制绘制的股票数量
        max_stocks = 6
        if len(stock_codes) > max_stocks:
            stock_codes = stock_codes[:max_stocks]
            print("⚠️ 只绘制前" + str(max_stocks) + "只股票的趋势图")
        
        # 获取数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days + 10)).strftime('%Y-%m-%d')
        
        # 由于SQLAlchemy的IN查询需要特殊处理，我们分别查询每只股票
        all_data = []
        for stock_code in stock_codes:
            sql = text("""
            SELECT a.ts_code, a.trade_date, a.adx, a.pdi, a.mdi, s.name as stock_name
            FROM adx_data a
            LEFT JOIN stock_basic s ON a.ts_code = s.ts_code
            WHERE a.ts_code = :stock_code
                AND a.period = :period
                AND a.trade_date >= :start_date
                AND a.trade_date <= :end_date
            ORDER BY a.trade_date
            """)
            
            stock_df = pd.read_sql(sql, self.engine, params={
                'stock_code': stock_code,
                'period': self.period,
                'start_date': start_date,
                'end_date': end_date
            })
            
            if not stock_df.empty:
                all_data.append(stock_df)
        
        if all_data:
            df = pd.concat(all_data, ignore_index=True)
        else:
            df = pd.DataFrame()
        
        if df.empty:
            print("❌ 没有找到相关股票的ADX数据")
            return
        
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        axes = axes.flatten()
        
        for i, ts_code in enumerate(stock_codes):
            if i >= len(axes):
                break
                
            stock_data = df[df['ts_code'] == ts_code].sort_values('trade_date')
            
            if stock_data.empty:
                continue
            
            ax = axes[i]
            stock_name = stock_data['stock_name'].iloc[0] if not stock_data['stock_name'].isna().all() else ts_code
            
            # 绘制ADX主线
            ax.plot(stock_data['trade_date'], stock_data['adx'], 
                   label='ADX', color='red', linewidth=2.5, marker='o', markersize=3)
            
            # 绘制+DI和-DI线
            ax.plot(stock_data['trade_date'], stock_data['pdi'], 
                   label='+DI', color='green', linewidth=1.5, alpha=0.8)
            ax.plot(stock_data['trade_date'], stock_data['mdi'], 
                   label='-DI', color='blue', linewidth=1.5, alpha=0.8)
            
            # 添加参考线
            ax.axhline(y=50, color='red', linestyle='--', alpha=0.5, label='ADX=50')
            ax.axhline(y=25, color='orange', linestyle='--', alpha=0.5, label='ADX=25')
            ax.axhline(y=20, color='gray', linestyle='--', alpha=0.5, label='ADX=20')
            
            # 设置图表属性
            ax.set_title(stock_name + ' (' + ts_code + ')\nADX趋势图', fontsize=12, pad=10)
            ax.set_ylabel('ADX/DI值', fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=8)
            
            # 设置x轴格式
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=7))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # 设置y轴范围
            ax.set_ylim(0, max(100, stock_data[['adx', 'pdi', 'mdi']].max().max() * 1.1))
        
        # 隐藏空的子图
        for i in range(len(stock_codes), len(axes)):
            axes[i].set_visible(False)
        
        plt.suptitle('ADX指标选股结果 - 趋势分析图\n(' + datetime.now().strftime("%Y-%m-%d") + ')', 
                     fontsize=16, y=0.98)
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = "adx_selected_stocks_trends_" + timestamp + ".png"
        output_dir = "big_a_compass/adx_output"
        os.makedirs(output_dir, exist_ok=True)
        chart_path = os.path.join(output_dir, chart_filename)
        
        plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
        print("✅ 趋势图已保存到: " + chart_path)
        
        plt.show()
        return chart_path

def main():
    """主函数"""
    print("🎯 ADX指标选股器")
    print("=" * 60)
    
    # 创建选股器实例
    selector = ADXStockSelector(period=14)
    
    # 执行选股
    try:
        # 筛选股票：ADX < 50，ADX > 20，最近5天连续上涨
        result_df = selector.get_adx_uptrend_stocks(
            max_adx=50,      # 最大ADX值
            uptrend_days=5,  # 连续上涨天数
            min_adx=30       # 最小ADX值，确保有一定趋势强度
        )
        
        if not result_df.empty:
            # 显示结果
            selector.display_results(result_df, top_n=20)
            
            # 保存结果
            csv_path = selector.save_results(result_df)
            
            # 绘制趋势图（前6只股票）
            top_stocks = result_df['ts_code'].head(6).tolist()
            if top_stocks:
                chart_path = selector.plot_adx_trends(top_stocks)
            
            print("\n🎉 选股完成！")
            print("📁 结果文件: " + csv_path)
            if 'chart_path' in locals():
                print("📊 趋势图: " + chart_path)
        
        else:
            print("\n❌ 未找到符合条件的股票")
            print("💡 建议调整筛选条件，例如：")
            print("   - 放宽ADX上限（如调整为60）")
            print("   - 减少连续上涨天数（如调整为3-4天）")
            print("   - 降低ADX下限（如调整为15）")
    
    except Exception as e:
        print("❌ 程序执行出错: " + str(e))
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 