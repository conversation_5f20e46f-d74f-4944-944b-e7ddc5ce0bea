"""
Alpha101因子实现 - 因子1至10

该模块实现了WorldQuant Alpha101因子中的前10个因子。
实现基于原始论文中的公式，并提供了详细的注释和解释。
"""

import pandas as pd
import numpy as np
from alpha_utils import *


def alpha_1(open_price: pd.DataFrame, close_price: pd.DataFrame, returns: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#1: (rank(Ts_ArgMax(SignedPower(((returns < 0) ? stddev(returns, 20) : close), 2.), 5)) - 0.5)
    
    逻辑解释:
    1. 对于收益率为负的情况，计算20天收益率的标准差；对于收益率为正的情况，使用收盘价
    2. 对上述结果进行平方处理（带符号）
    3. 找出过去5天中上述结果最大值出现的位置
    4. 对该位置进行横截面排名，并减去0.5
    
    参数:
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        returns: 收益率数据框
        
    返回:
        因子值
    """
    # 条件选择：收益率为负时使用20天标准差，否则使用收盘价
    condition = returns < 0
    data = pd.DataFrame(np.where(condition, stddev(returns, 20), close_price), 
                       index=close_price.index, columns=close_price.columns)
    
    # 计算带符号的平方
    data = signedpower(data, 2.0)
    
    # 找出过去5天中最大值出现的位置
    data = ts_argmax(data, 5)
    
    # 横截面排名并减去0.5
    return rank(data) - 0.5


def alpha_2(open_price: pd.DataFrame, close_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#2: (-1 * correlation(rank(delta(log(volume), 2)), rank(((close - open) / open)), 6))
    
    逻辑解释:
    1. 计算交易量的对数变化率（2天）
    2. 计算日内收益率（收盘价-开盘价）/开盘价
    3. 对上述两个序列进行横截面排名
    4. 计算这两个排名序列在过去6天的相关性
    5. 取相关性的负值作为因子值
    
    参数:
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        volume: 交易量数据框
        
    返回:
        因子值
    """
    # 计算交易量的对数变化率（2天）
    volume_change = delta(np.log(volume), 2)
    
    # 计算日内收益率
    intraday_return = (close_price - open_price) / open_price
    
    # 对两个序列进行横截面排名
    rank_volume_change = rank(volume_change)
    rank_intraday_return = rank(intraday_return)
    
    # 计算相关性并取负值
    return -1 * correlation(rank_volume_change, rank_intraday_return, 6)


def alpha_3(open_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#3: (-1 * correlation(rank(open), rank(volume), 10))
    
    逻辑解释:
    1. 对开盘价和交易量进行横截面排名
    2. 计算这两个排名序列在过去10天的相关性
    3. 取相关性的负值作为因子值
    
    参数:
        open_price: 开盘价数据框
        volume: 交易量数据框
        
    返回:
        因子值
    """
    # 对开盘价和交易量进行横截面排名
    rank_open = rank(open_price)
    rank_volume = rank(volume)
    
    # 计算相关性并取负值
    return -1 * correlation(rank_open, rank_volume, 10)


def alpha_4(low_price: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#4: (-1 * Ts_Rank(rank(low), 9))
    
    逻辑解释:
    1. 对最低价进行横截面排名
    2. 计算该排名在过去9天的时间序列排名
    3. 取时间序列排名的负值作为因子值
    
    参数:
        low_price: 最低价数据框
        
    返回:
        因子值
    """
    # 对最低价进行横截面排名
    rank_low = rank(low_price)
    
    # 计算时间序列排名并取负值
    return -1 * ts_rank(rank_low, 9)


def alpha_5(open_price: pd.DataFrame, close_price: pd.DataFrame, vwap: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#5: (rank((open - (sum(vwap, 10) / 10))) * (-1 * abs(rank((close - vwap)))))
    
    逻辑解释:
    1. 计算开盘价与过去10天vwap均值的差
    2. 计算收盘价与当日vwap的差
    3. 对上述两个差值进行横截面排名
    4. 第二个排名取绝对值并取负
    5. 两个排名相乘得到因子值
    
    参数:
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        vwap: 成交量加权平均价数据框
        
    返回:
        因子值
    """
    # 计算过去10天vwap的均值
    vwap_mean_10 = sum(vwap, 10) / 10
    
    # 计算开盘价与vwap均值的差，并排名
    rank_open_vwap_diff = rank(open_price - vwap_mean_10)
    
    # 计算收盘价与当日vwap的差，排名后取绝对值并取负
    rank_close_vwap_diff = -1 * abs(rank(close_price - vwap))
    
    # 两个排名相乘
    return rank_open_vwap_diff * rank_close_vwap_diff


def alpha_6(open_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#6: (-1 * correlation(open, volume, 10))
    
    逻辑解释:
    1. 计算开盘价和交易量在过去10天的相关性
    2. 取相关性的负值作为因子值
    
    参数:
        open_price: 开盘价数据框
        volume: 交易量数据框
        
    返回:
        因子值
    """
    # 计算相关性并取负值
    return -1 * correlation(open_price, volume, 10)


def alpha_7(close_price: pd.DataFrame, volume: pd.DataFrame, adv20: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#7: ((adv20 < volume) ? ((-1 * ts_rank(abs(delta(close, 7)), 60)) * sign(delta(close, 7))) : (-1 * 1))
    
    逻辑解释:
    1. 判断当日交易量是否大于20日平均交易量
    2. 如果是，计算收盘价7日变化的绝对值的60日时间序列排名，乘以收盘价7日变化的符号，再取负值
    3. 如果不是，返回-1
    
    参数:
        close_price: 收盘价数据框
        volume: 交易量数据框
        adv20: 20日平均交易量数据框
        
    返回:
        因子值
    """
    # 计算收盘价7日变化
    delta_close_7 = delta(close_price, 7)
    
    # 计算条件部分
    condition_true = (-1 * ts_rank(abs(delta_close_7), 60)) * np.sign(delta_close_7)
    condition_false = pd.DataFrame(-1, index=close_price.index, columns=close_price.columns)
    
    # 根据条件选择结果
    return pd.DataFrame(np.where(adv20 < volume, condition_true, condition_false),
                       index=close_price.index, columns=close_price.columns)


def alpha_8(open_price: pd.DataFrame, returns: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#8: (-1 * rank(((sum(open, 5) * sum(returns, 5)) - delay((sum(open, 5) * sum(returns, 5)), 10))))
    
    逻辑解释:
    1. 计算过去5天开盘价之和与过去5天收益率之和的乘积
    2. 计算该乘积与其10天前值的差
    3. 对该差值进行横截面排名并取负值
    
    参数:
        open_price: 开盘价数据框
        returns: 收益率数据框
        
    返回:
        因子值
    """
    # 计算过去5天开盘价之和与过去5天收益率之和的乘积
    product = sum(open_price, 5) * sum(returns, 5)
    
    # 计算该乘积与其10天前值的差
    diff = product - delay(product, 10)
    
    # 对差值进行横截面排名并取负值
    return -1 * rank(diff)


def alpha_9(close_price: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#9: ((0 < ts_min(delta(close, 1), 5)) ? delta(close, 1) : ((ts_max(delta(close, 1), 5) < 0) ?
    delta(close, 1) : (-1 * delta(close, 1))))
    
    逻辑解释:
    1. 判断过去5天收盘价变化的最小值是否大于0
    2. 如果是，返回当日收盘价变化
    3. 如果不是，判断过去5天收盘价变化的最大值是否小于0
    4. 如果是，返回当日收盘价变化
    5. 如果不是，返回当日收盘价变化的负值
    
    参数:
        close_price: 收盘价数据框
        
    返回:
        因子值
    """
    # 计算收盘价1日变化
    delta_close_1 = delta(close_price, 1)
    
    # 计算过去5天收盘价变化的最小值和最大值
    ts_min_delta_close = ts_min(delta_close_1, 5)
    ts_max_delta_close = ts_max(delta_close_1, 5)
    
    # 根据条件选择结果
    condition1 = 0 < ts_min_delta_close
    condition2 = ts_max_delta_close < 0
    
    result = pd.DataFrame(np.where(condition1, delta_close_1, 
                                  np.where(condition2, delta_close_1, -1 * delta_close_1)),
                         index=close_price.index, columns=close_price.columns)
    
    return result


def alpha_10(close_price: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#10: rank(((0 < ts_min(delta(close, 1), 4)) ? delta(close, 1) : ((ts_max(delta(close, 1), 4) < 0) ?
    delta(close, 1) : (-1 * delta(close, 1)))))
    
    逻辑解释:
    1. 判断过去4天收盘价变化的最小值是否大于0
    2. 如果是，使用当日收盘价变化
    3. 如果不是，判断过去4天收盘价变化的最大值是否小于0
    4. 如果是，使用当日收盘价变化
    5. 如果不是，使用当日收盘价变化的负值
    6. 对上述结果进行横截面排名
    
    参数:
        close_price: 收盘价数据框
        
    返回:
        因子值
    """
    # 计算收盘价1日变化
    delta_close_1 = delta(close_price, 1)
    
    # 计算过去4天收盘价变化的最小值和最大值
    ts_min_delta_close = ts_min(delta_close_1, 4)
    ts_max_delta_close = ts_max(delta_close_1, 4)
    
    # 根据条件选择结果
    condition1 = 0 < ts_min_delta_close
    condition2 = ts_max_delta_close < 0
    
    result = pd.DataFrame(np.where(condition1, delta_close_1, 
                                  np.where(condition2, delta_close_1, -1 * delta_close_1)),
                         index=close_price.index, columns=close_price.columns)
    
    # 对结果进行横截面排名
    return rank(result)
