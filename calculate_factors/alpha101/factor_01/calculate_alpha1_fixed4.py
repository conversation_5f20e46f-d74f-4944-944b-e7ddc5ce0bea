"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/3/12 20:23
@Description: 使用真实数据库数据计算 alpha_1 因子，并输出最近90天的结果到CSV文件
              理论上来说，值越大，说明越好
"""
import os
import sys
import pandas as pd
from datetime import datetime, timedelta, date
import multiprocessing as mp

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入自定义模块
from get_data.db_config import get_engine
from calculate_factors.alpha101.factor_01.test_01 import alpha_1


def get_stock_list():
    """
    获取股票列表
    
    返回:
        股票代码列表
    """
    # 从数据库获取股票列表
    # 注意：使用双百分号 %% 来转义 SQL 中的 LIKE 操作符
    sql = """
    SELECT DISTINCT ts_code FROM stk_factor
    WHERE ts_code LIKE '%%SZ' OR ts_code LIKE '%%SH'
    """
    with get_engine.connect() as conn:
        df = pd.read_sql(sql, conn)
    return df['ts_code'].tolist()


def get_trade_dates(start_date=None, end_date=None, days=90):
    """
    获取交易日期列表
    
    参数:
        start_date: 开始日期，默认为None
        end_date: 结束日期，默认为None
        days: 获取最近的交易日天数，默认为90
        
    返回:
        交易日期列表
    """
    # 如果未指定结束日期，则使用当前日期
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    elif isinstance(end_date, date):
        end_date = end_date.strftime('%Y-%m-%d')
    
    # 如果未指定开始日期，则根据days参数计算
    if start_date is None:
        # 获取比所需天数更多的日期，以确保有足够的交易日
        date_before = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=days*2)).strftime('%Y-%m-%d')
        
        # 从数据库获取交易日期
        sql = f"""
        SELECT DISTINCT trade_date FROM stk_factor
        WHERE trade_date BETWEEN '{date_before}' AND '{end_date}'
        ORDER BY trade_date DESC
        """
        with get_engine.connect() as conn:
            df = pd.read_sql(sql, conn)
        
        # 获取最近days天的交易日
        trade_dates = df['trade_date'].tolist()[:days]
        trade_dates.sort()  # 按日期升序排序
        return trade_dates
    else:
        # 确保开始日期是字符串格式
        if isinstance(start_date, date):
            start_date = start_date.strftime('%Y-%m-%d')
            
        # 获取指定日期范围内的交易日
        sql = f"""
        SELECT DISTINCT trade_date FROM stk_factor
        WHERE trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        with get_engine.connect() as conn:
            df = pd.read_sql(sql, conn)
        return df['trade_date'].tolist()


def get_price_data(stock_list, start_date, end_date):
    """
    获取股票价格数据
    
    参数:
        stock_list: 股票代码列表
        start_date: 开始日期
        end_date: 结束日期
        
    返回:
        开盘价、收盘价和收益率数据框
    """
    # 为了计算收益率和滚动指标，需要获取更早的数据
    # 假设我们需要计算20天标准差和5天最大值，至少需要额外的20+5=25天数据
    extended_days = 25
    
    # 确保日期是字符串格式
    if isinstance(start_date, date):
        start_date = start_date.strftime('%Y-%m-%d')
    if isinstance(end_date, date):
        end_date = end_date.strftime('%Y-%m-%d')
        
    extended_start_date = (datetime.strptime(start_date, '%Y-%m-%d') - timedelta(days=extended_days*2)).strftime('%Y-%m-%d')
    
    # 构建SQL查询，获取后复权价格数据
    stock_list_str = ','.join([f"'{stock}'" for stock in stock_list])
    
    sql = f"""
    SELECT ts_code, trade_date, open_hfq as open, close_hfq as close
    FROM stk_factor
    WHERE ts_code IN ({stock_list_str})
    AND trade_date BETWEEN '{extended_start_date}' AND '{end_date}'
    ORDER BY ts_code, trade_date
    """
    
    # 从数据库获取数据
    with get_engine.connect() as conn:
        df = pd.read_sql(sql, conn)
    
    # 将数据透视为宽格式
    open_price = df.pivot(index='trade_date', columns='ts_code', values='open')
    close_price = df.pivot(index='trade_date', columns='ts_code', values='close')
    
    # 计算收益率 (当日收盘价 / 前一日收盘价 - 1)
    # 明确指定 fill_method=None，不对 NA 值进行填充
    returns = close_price.pct_change(fill_method=None)
    
    # 对于第一行的 NA 值（因为没有前一天的数据），填充为 0
    returns.fillna(0, inplace=True)
    
    return open_price, close_price, returns


def calculate_alpha1_batch(stock_batch, start_date, end_date, output_dir):
    """
    批量计算一组股票的alpha1因子
    
    参数:
        stock_batch: 股票代码批次
        start_date: 开始日期
        end_date: 结束日期
        output_dir: 输出目录
        
    返回:
        因子计算结果
    """
    try:
        # 确保日期是字符串格式
        if isinstance(start_date, date):
            start_date = start_date.strftime('%Y-%m-%d')
        if isinstance(end_date, date):
            end_date = end_date.strftime('%Y-%m-%d')
            
        # 获取价格数据
        open_price, close_price, returns = get_price_data(stock_batch, start_date, end_date)
        
        # 计算alpha1因子
        alpha1_result = alpha_1(open_price, close_price, returns)
        
        # 筛选指定日期范围内的结果
        # 使用字符串格式的日期进行比较，以避免Timestamp和datetime.date比较的问题
        alpha1_result.index = alpha1_result.index.astype(str)  # 将索引转换为字符串
        mask = (alpha1_result.index >= start_date) & (alpha1_result.index <= end_date)
        alpha1_result = alpha1_result[mask]
        
        # 转换为长格式
        alpha1_df = alpha1_result.stack().reset_index()
        alpha1_df.columns = ['trade_date', 'ts_code', 'alpha1']
        
        # 保存到CSV
        batch_id = stock_batch[0].split('.')[0]  # 使用第一个股票代码作为批次标识
        output_file = os.path.join(output_dir, f'alpha1_batch_{batch_id}.csv')
        alpha1_df.to_csv(output_file, index=False)
        
        print(f"已完成批次 {batch_id} 的计算，结果保存至 {output_file}")
        return alpha1_df
    except Exception as e:
        print(f"处理批次 {stock_batch[0]} 时出错: {str(e)}")
        import traceback
        traceback.print_exc()  # 打印详细的错误堆栈
        return None


def main():
    """
    主函数
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '#1')
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取最近90天的交易日期
    end_date = datetime.now().strftime('%Y-%m-%d')
    trade_dates = get_trade_dates(end_date=end_date, days=90)
    if not trade_dates:
        print("未找到交易日期数据")
        return
    
    start_date = trade_dates[0]
    end_date = trade_dates[-1]
    print(f"计算日期范围: {start_date} 至 {end_date}")
    
    # 获取股票列表
    stock_list = get_stock_list()
    if not stock_list:
        print("未找到股票数据")
        return
    
    print(f"共找到 {len(stock_list)} 只股票")
    
    # 将股票列表分成多个批次，以便并行处理
    # 根据CPU核心数确定批次数量，但每批至少包含50只股票
    num_cores = mp.cpu_count()
    batch_size = max(50, len(stock_list) // num_cores)
    stock_batches = [stock_list[i:i+batch_size] for i in range(0, len(stock_list), batch_size)]
    
    print(f"将使用 {num_cores} 个CPU核心处理 {len(stock_batches)} 个批次")
    
    # 使用多进程并行计算
    with mp.Pool(processes=num_cores) as pool:
        results = []
        for batch in stock_batches:
            result = pool.apply_async(calculate_alpha1_batch, args=(batch, start_date, end_date, output_dir))
            results.append(result)
        
        # 获取所有结果
        alpha1_results = [res.get() for res in results if res.get() is not None]
    
    # 合并所有批次的结果
    if alpha1_results:
        final_result = pd.concat(alpha1_results, ignore_index=True)
        final_output = os.path.join(output_dir, 'alpha1_all.csv')
        final_result.to_csv(final_output, index=False)
        print(f"所有计算结果已合并保存至 {final_output}")
    else:
        print("所有批次处理均失败，无法生成最终结果")


if __name__ == "__main__":
    print("开始计算alpha1因子...")
    start_time = datetime.now()
    main()
    end_time = datetime.now()
    print(f"计算完成，总耗时: {end_time - start_time}")
