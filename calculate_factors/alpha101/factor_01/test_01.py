"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/3/12 20:19
@Description: 
"""
import pandas as pd
import numpy as np
from calculate_factors.alpha101.alpha_utils import *


def alpha_1(open_price: pd.DataFrame, close_price: pd.DataFrame, returns: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#1: (rank(Ts_ArgMax(SignedPower(((returns < 0) ? stddev(returns, 20) : close), 2.), 5)) - 0.5)

    逻辑解释:
    1. 对于收益率为负的情况，计算20天收益率的标准差；对于收益率为正的情况，使用收盘价
    2. 对上述结果进行平方处理（带符号）
    3. 找出过去5天中上述结果最大值出现的位置
    4. 对该位置进行横截面排名，并减去0.5

    参数:
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        returns: 收益率数据框

    返回:
        因子值
    """
    # 条件选择：收益率为负时使用20天标准差，否则使用收盘价
    condition = returns < 0
    data = pd.DataFrame(np.where(condition, stddev(returns, 20), close_price),
                        index=close_price.index, columns=close_price.columns)

    # 计算带符号的平方
    data = signedpower(data, 2.0)

    # 找出过去5天中最大值出现的位置
    data = ts_argmax(data, 5)

    # 横截面排名并减去0.5
    return rank(data) - 0.5
