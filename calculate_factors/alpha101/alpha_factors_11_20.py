"""
Alpha101因子实现 - 因子11至20

该模块实现了WorldQuant Alpha101因子中的11-20号因子。
实现基于原始论文中的公式，并提供了详细的注释和解释。
"""

import pandas as pd
import numpy as np
from alpha_utils import *


def alpha_11(high_price: pd.DataFrame, low_price: pd.DataFrame, volume: pd.DataFrame, vwap: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#11: ((rank(ts_max((vwap - close), 3)) + rank(ts_min((vwap - close), 3))) *
    rank(delta(volume, 3)))
    
    逻辑解释:
    1. 计算vwap与收盘价的差值在过去3天的最大值和最小值
    2. 对这两个极值分别进行横截面排名，并相加
    3. 计算交易量3日变化，并进行横截面排名
    4. 将前两步的结果相乘得到因子值
    
    参数:
        high_price: 最高价数据框
        low_price: 最低价数据框
        volume: 交易量数据框
        vwap: 成交量加权平均价数据框
        
    返回:
        因子值
    """
    # 计算收盘价（这里使用(high+low)/2作为收盘价的近似）
    close_price = (high_price + low_price) / 2
    
    # 计算vwap与收盘价的差值
    vwap_close_diff = vwap - close_price
    
    # 计算差值在过去3天的最大值和最小值
    ts_max_diff = ts_max(vwap_close_diff, 3)
    ts_min_diff = ts_min(vwap_close_diff, 3)
    
    # 对极值进行横截面排名并相加
    sum_rank = rank(ts_max_diff) + rank(ts_min_diff)
    
    # 计算交易量3日变化并排名
    volume_delta_rank = rank(delta(volume, 3))
    
    # 相乘得到因子值
    return sum_rank * volume_delta_rank


def alpha_12(close_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#12: (sign(delta(volume, 1)) * (-1 * delta(close, 1)))
    
    逻辑解释:
    1. 计算交易量的1日变化的符号
    2. 计算收盘价的1日变化，并取负值
    3. 将两者相乘得到因子值
    
    参数:
        close_price: 收盘价数据框
        volume: 交易量数据框
        
    返回:
        因子值
    """
    # 计算交易量1日变化的符号
    volume_delta_sign = np.sign(delta(volume, 1))
    
    # 计算收盘价1日变化并取负值
    close_delta_neg = -1 * delta(close_price, 1)
    
    # 相乘得到因子值
    return volume_delta_sign * close_delta_neg


def alpha_13(close_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#13: (-1 * rank(covariance(rank(close), rank(volume), 5)))
    
    逻辑解释:
    1. 对收盘价和交易量分别进行横截面排名
    2. 计算这两个排名序列在过去5天的协方差
    3. 对协方差进行横截面排名，并取负值
    
    参数:
        close_price: 收盘价数据框
        volume: 交易量数据框
        
    返回:
        因子值
    """
    # 对收盘价和交易量进行横截面排名
    rank_close = rank(close_price)
    rank_volume = rank(volume)
    
    # 计算排名序列的协方差
    cov = covariance(rank_close, rank_volume, 5)
    
    # 对协方差进行横截面排名并取负值
    return -1 * rank(cov)


def alpha_14(open_price: pd.DataFrame, volume: pd.DataFrame, returns: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#14: ((-1 * rank(delta(returns, 3))) * correlation(open, volume, 10))
    
    逻辑解释:
    1. 计算收益率的3日变化，并进行横截面排名，取负值
    2. 计算开盘价与交易量在过去10天的相关性
    3. 将两者相乘得到因子值
    
    参数:
        open_price: 开盘价数据框
        volume: 交易量数据框
        returns: 收益率数据框
        
    返回:
        因子值
    """
    # 计算收益率3日变化的排名并取负值
    returns_delta_rank = -1 * rank(delta(returns, 3))
    
    # 计算开盘价与交易量的相关性
    open_volume_corr = correlation(open_price, volume, 10)
    
    # 相乘得到因子值
    return returns_delta_rank * open_volume_corr


def alpha_15(high_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#15: (-1 * sum(rank(correlation(rank(high), rank(volume), 3)), 3))
    
    逻辑解释:
    1. 对最高价和交易量分别进行横截面排名
    2. 计算这两个排名序列在过去3天的相关性
    3. 对相关性进行横截面排名
    4. 计算排名的3日总和，并取负值
    
    参数:
        high_price: 最高价数据框
        volume: 交易量数据框
        
    返回:
        因子值
    """
    # 对最高价和交易量进行横截面排名
    rank_high = rank(high_price)
    rank_volume = rank(volume)
    
    # 计算排名序列的相关性
    corr = correlation(rank_high, rank_volume, 3)
    
    # 对相关性进行横截面排名
    rank_corr = rank(corr)
    
    # 计算排名的3日总和并取负值
    return -1 * sum(rank_corr, 3)


def alpha_16(high_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#16: (-1 * rank(covariance(rank(high), rank(volume), 5)))
    
    逻辑解释:
    1. 对最高价和交易量分别进行横截面排名
    2. 计算这两个排名序列在过去5天的协方差
    3. 对协方差进行横截面排名，并取负值
    
    参数:
        high_price: 最高价数据框
        volume: 交易量数据框
        
    返回:
        因子值
    """
    # 对最高价和交易量进行横截面排名
    rank_high = rank(high_price)
    rank_volume = rank(volume)
    
    # 计算排名序列的协方差
    cov = covariance(rank_high, rank_volume, 5)
    
    # 对协方差进行横截面排名并取负值
    return -1 * rank(cov)


def alpha_17(close_price: pd.DataFrame, volume: pd.DataFrame, adv20: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#17: (((-1 * rank(ts_rank(close, 10))) * rank(delta(delta(close, 1), 1))) *
    rank(ts_rank((volume / adv20), 5)))
    
    逻辑解释:
    1. 计算收盘价在过去10天的时间序列排名，进行横截面排名，取负值
    2. 计算收盘价的1日变化的1日变化（二阶差分），进行横截面排名
    3. 计算交易量与20日平均交易量的比值在过去5天的时间序列排名，进行横截面排名
    4. 将上述三个结果相乘得到因子值
    
    参数:
        close_price: 收盘价数据框
        volume: 交易量数据框
        adv20: 20日平均交易量数据框
        
    返回:
        因子值
    """
    # 计算收盘价时间序列排名的横截面排名并取负值
    close_ts_rank = -1 * rank(ts_rank(close_price, 10))
    
    # 计算收盘价的二阶差分并排名
    close_delta_delta = rank(delta(delta(close_price, 1), 1))
    
    # 计算交易量比值的时间序列排名并进行横截面排名
    volume_ratio_rank = rank(ts_rank((volume / adv20), 5))
    
    # 三者相乘得到因子值
    return close_ts_rank * close_delta_delta * volume_ratio_rank


def alpha_18(open_price: pd.DataFrame, close_price: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#18: (-1 * rank(((stddev(abs((close - open)), 5) + (close - open)) + correlation(close, open, 10))))
    
    逻辑解释:
    1. 计算收盘价与开盘价差值的绝对值在过去5天的标准差
    2. 将标准差与当日收盘价和开盘价的差值相加
    3. 计算收盘价与开盘价在过去10天的相关性
    4. 将步骤2和步骤3的结果相加
    5. 对结果进行横截面排名，并取负值
    
    参数:
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        
    返回:
        因子值
    """
    # 计算收盘价与开盘价的差值
    close_open_diff = close_price - open_price
    
    # 计算差值绝对值的标准差
    diff_abs_std = stddev(abs(close_open_diff), 5)
    
    # 标准差与当日差值相加
    sum_part = diff_abs_std + close_open_diff
    
    # 计算收盘价与开盘价的相关性
    corr = correlation(close_price, open_price, 10)
    
    # 将两部分相加，进行横截面排名并取负值
    return -1 * rank(sum_part + corr)


def alpha_19(close_price: pd.DataFrame, returns: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#19: ((-1 * sign(((close - delay(close, 7)) + delta(close, 7)))) * (1 + rank((1 + sum(returns, 250)))))
    
    逻辑解释:
    1. 计算收盘价与7天前收盘价的差值，加上收盘价7日变化
    2. 取上述结果的符号的负值
    3. 计算过去250天收益率之和加1，进行横截面排名，再加1
    4. 将步骤2和步骤3的结果相乘
    
    参数:
        close_price: 收盘价数据框
        returns: 收益率数据框
        
    返回:
        因子值
    """
    # 计算收盘价与7天前的差值
    close_delay_diff = close_price - delay(close_price, 7)
    
    # 计算收盘价7日变化
    close_delta = delta(close_price, 7)
    
    # 计算差值加变化的符号并取负值
    sign_part = -1 * np.sign(close_delay_diff + close_delta)
    
    # 计算收益率之和加1，排名后再加1
    returns_part = 1 + rank(1 + sum(returns, 250))
    
    # 两部分相乘得到因子值
    return sign_part * returns_part


def alpha_20(open_price: pd.DataFrame, high_price: pd.DataFrame, low_price: pd.DataFrame, close_price: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#20: (((-1 * rank((open - delay(high, 1)))) * rank((open - delay(close, 1)))) * rank((open - delay(low, 1))))
    
    逻辑解释:
    1. 计算开盘价与前一日最高价的差值，进行横截面排名，取负值
    2. 计算开盘价与前一日收盘价的差值，进行横截面排名
    3. 计算开盘价与前一日最低价的差值，进行横截面排名
    4. 将上述三个排名相乘得到因子值
    
    参数:
        open_price: 开盘价数据框
        high_price: 最高价数据框
        low_price: 最低价数据框
        close_price: 收盘价数据框
        
    返回:
        因子值
    """
    # 计算开盘价与前一日最高价的差值排名并取负值
    open_high_diff_rank = -1 * rank(open_price - delay(high_price, 1))
    
    # 计算开盘价与前一日收盘价的差值排名
    open_close_diff_rank = rank(open_price - delay(close_price, 1))
    
    # 计算开盘价与前一日最低价的差值排名
    open_low_diff_rank = rank(open_price - delay(low_price, 1))
    
    # 三者相乘得到因子值
    return open_high_diff_rank * open_close_diff_rank * open_low_diff_rank
