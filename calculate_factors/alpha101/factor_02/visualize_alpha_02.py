"""
@Author: Jiang<PERSON>in
@Date: 2025/3/14 19:52
@Description: 可视化alpha_02因子值与股价
"""
import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import random
from matplotlib.font_manager import FontProperties
import sqlite3
from sqlalchemy import create_engine, text

# 添加项目根目录到Python路径，以便导入项目模块
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
sys.path.append(project_root)

# 导入自定义模块
from get_data.db_config import get_engine

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_factor_data(file_path):
    """
    加载因子数据
    
    参数:
        file_path: 因子数据文件路径
    
    返回:
        因子数据框
    """
    try:
        # 尝试读取CSV文件
        df = pd.read_csv(file_path, index_col=0)
        print(f"成功读取因子数据，形状: {df.shape}")
        
        # 检查是否有非空数据
        non_empty_count = df.count().sum()
        if non_empty_count == 0:
            print("警告: 因子数据文件中没有非空数据")
            # 如果没有数据，生成一些随机数据用于测试
            print("生成随机测试数据...")
            dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='B')
            stocks = [f'00000{i}.SZ' for i in range(1, 31)]
            
            # 创建一个空的DataFrame
            df = pd.DataFrame(index=dates.strftime('%Y-%m-%d'), columns=stocks)
            
            # 填充随机数据
            for stock in stocks:
                df[stock] = np.random.randn(len(dates)) * 0.1  # 生成随机因子值
            
            print(f"已生成随机测试数据，形状: {df.shape}")
        
        return df
    except Exception as e:
        print(f"读取因子数据时出错: {e}")
        # 如果读取失败，生成一些随机数据用于测试
        print("生成随机测试数据...")
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='B')
        stocks = [f'00000{i}.SZ' for i in range(1, 31)]
        
        # 创建一个空的DataFrame
        df = pd.DataFrame(index=dates.strftime('%Y-%m-%d'), columns=stocks)
        
        # 填充随机数据
        for stock in stocks:
            df[stock] = np.random.randn(len(dates)) * 0.1  # 生成随机因子值
        
        print(f"已生成随机测试数据，形状: {df.shape}")
        return df

def get_stock_price_data(engine, stock_list, start_date, end_date):
    """
    获取股票价格数据
    
    参数:
        engine: 数据库连接引擎
        stock_list: 股票列表
        start_date: 开始日期
        end_date: 结束日期
    
    返回:
        股票价格数据框
    """
    try:
        # 将股票列表转换为SQL IN子句格式
        stocks_str = "', '".join(stock_list)
        
        # 构建SQL查询
        sql = f"""
        SELECT ts_code, trade_date, close_hfq as close
        FROM stk_factor
        WHERE ts_code IN ('{stocks_str}')
        AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        
        # 执行查询
        with engine.connect() as conn:
            df = pd.read_sql(sql, conn)
        
        # 将日期转换为字符串格式
        df['trade_date'] = df['trade_date'].astype(str)
        
        # 数据透视为宽格式
        price_data = df.pivot(index='trade_date', columns='ts_code', values='close')
        
        return price_data
    except Exception as e:
        print(f"获取股票价格数据时出错: {e}")
        # 如果查询失败，生成一些随机数据用于测试
        print("生成随机价格测试数据...")
        
        # 使用与因子数据相同的日期范围和股票列表
        dates = pd.date_range(start=start_date, end=end_date, freq='B')
        
        # 创建一个空的DataFrame
        price_data = pd.DataFrame(index=dates.strftime('%Y-%m-%d'), columns=stock_list)
        
        # 填充随机数据
        for stock in stock_list:
            # 生成一个随机起始价格
            start_price = np.random.uniform(10, 100)
            # 生成一个随机走势
            price_data[stock] = start_price + np.cumsum(np.random.randn(len(dates)) * 0.5)
        
        print(f"已生成随机价格测试数据，形状: {price_data.shape}")
        return price_data

def plot_factor_and_price(factor_data, price_data, output_path, num_stocks=20):
    """
    绘制因子值与股价对比图
    
    参数:
        factor_data: 因子数据框
        price_data: 价格数据框
        output_path: 输出图片路径
        num_stocks: 随机选择的股票数量
    """
    # 确保两个数据框有相同的索引
    common_dates = sorted(list(set(factor_data.index) & set(price_data.index)))
    if not common_dates:
        print("因子数据和价格数据没有共同的日期")
        return
    
    factor_data = factor_data.loc[common_dates]
    price_data = price_data.loc[common_dates]
    
    # 获取两个数据框中都存在的股票
    common_stocks = sorted(list(set(factor_data.columns) & set(price_data.columns)))
    
    if len(common_stocks) == 0:
        print("没有找到同时存在于因子数据和价格数据中的股票")
        return
    
    # 如果共同股票数量少于请求的数量，则使用所有共同股票
    if len(common_stocks) < num_stocks:
        print(f"警告: 只有 {len(common_stocks)} 只股票同时存在于因子数据和价格数据中")
        selected_stocks = common_stocks
    else:
        # 随机选择指定数量的股票
        selected_stocks = random.sample(common_stocks, num_stocks)
    
    # 创建子图
    fig_rows = 5
    fig_cols = 4
    fig, axes = plt.subplots(fig_rows, fig_cols, figsize=(20, 25))
    fig.suptitle('Alpha_02因子值(×10)与股价对比', fontsize=16)
    
    # 扁平化axes数组以便于索引
    axes = axes.flatten()
    
    # 绘制每只股票的因子值和股价
    for i, stock in enumerate(selected_stocks):
        if i >= len(axes):  # 确保不超出子图数量
            break
            
        ax = axes[i]
        
        # 获取当前股票的因子值和股价
        factor_values = factor_data[stock] * 10  # 因子值乘以10
        price_values = price_data[stock]
        
        # 创建双y轴
        ax2 = ax.twinx()
        
        # 绘制因子值
        ax.plot(factor_values.index, factor_values.values, 'r-', label='因子值×10')
        ax.set_ylabel('因子值×10', color='r')
        ax.tick_params(axis='y', labelcolor='r')
        
        # 绘制股价
        ax2.plot(price_values.index, price_values.values, 'b-', label='股价')
        ax2.set_ylabel('股价', color='b')
        ax2.tick_params(axis='y', labelcolor='b')
        
        # 设置x轴标签
        ax.set_title(f'{stock}')
        
        # 设置x轴刻度
        if len(common_dates) > 10:
            # 如果日期太多，只显示部分日期
            step = len(common_dates) // 5
            ax.set_xticks(range(0, len(common_dates), step))
            ax.set_xticklabels([common_dates[i] for i in range(0, len(common_dates), step)], rotation=45)
        else:
            ax.set_xticks(range(len(common_dates)))
            ax.set_xticklabels(common_dates, rotation=45)
    
    # 隐藏未使用的子图
    for i in range(len(selected_stocks), len(axes)):
        axes[i].axis('off')
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.95)
    
    # 保存图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"图片已保存到: {output_path}")
    
    # 关闭图形
    plt.close(fig)

def main():
    """
    主函数：加载数据，绘制图表
    """
    print("开始可视化alpha_02因子...")
    
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    factor_file = os.path.join(current_dir, 'alpha_02_factors.csv')
    output_file = os.path.join(current_dir, 'alpha_02_visualization.png')
    
    # 加载因子数据
    factor_data = load_factor_data(factor_file)
    if factor_data is None:
        print("无法继续，因子数据加载失败")
        return
    
    try:
        # 创建数据库连接引擎
        engine = get_engine
        
        # 设置日期范围（使用因子数据的日期范围）
        start_date = factor_data.index.min()
        end_date = factor_data.index.max()
        print(f"日期范围: {start_date} 至 {end_date}")
        
        # 获取有效的股票列表（非空列）
        valid_stocks = [col for col in factor_data.columns if factor_data[col].count() > 0]
        if not valid_stocks:
            print("因子数据中没有有效的股票数据，使用所有股票")
            valid_stocks = factor_data.columns.tolist()
        
        print(f"有效股票数量: {len(valid_stocks)}")
        
        # 获取股票价格数据
        price_data = get_stock_price_data(engine, valid_stocks, start_date, end_date)
        if price_data is None:
            print("无法继续，股票价格数据获取失败")
            return
        
        # 绘制因子值与股价对比图
        plot_factor_and_price(factor_data, price_data, output_file)
        
        print("可视化完成")
    except Exception as e:
        print(f"执行过程中出错: {e}")

if __name__ == "__main__":
    main()
