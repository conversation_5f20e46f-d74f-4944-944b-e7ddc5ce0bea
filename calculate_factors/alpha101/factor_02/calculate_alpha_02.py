"""
@Author: JiangXin
@Date: 2025/3/14 19:08
@Description: 使用实际数据计算alpha_02因子并保存到CSV文件
"""
import os
import pandas as pd
import numpy as np
from datetime import datetime
import multiprocessing as mp
from sqlalchemy import text
import time
from tqdm import tqdm

# 导入自定义模块
from get_data.db_config import get_engine
from calculate_factors.alpha101.factor_02.alpha_02 import alpha_2

def get_stock_list(engine):
    """
    从数据库获取股票列表
    
    参数:
        engine: 数据库连接引擎
    
    返回:
        股票列表
    """
    sql = "SELECT DISTINCT ts_code FROM stk_factor WHERE ts_code LIKE '%%0.SZ' OR ts_code LIKE '%%1.SZ' OR ts_code LIKE '6%%.SH'"
    with engine.connect() as conn:
        df = pd.read_sql(sql, conn)
    return df['ts_code'].tolist()

def get_trade_dates(engine, start_date, end_date):
    """
    获取交易日期列表
    
    参数:
        engine: 数据库连接引擎
        start_date: 开始日期
        end_date: 结束日期
    
    返回:
        交易日期列表
    """
    sql = f"""
    SELECT DISTINCT trade_date 
    FROM stk_factor 
    WHERE trade_date BETWEEN '{start_date}' AND '{end_date}'
    ORDER BY trade_date
    """
    with engine.connect() as conn:
        df = pd.read_sql(sql, conn)
    return df['trade_date'].tolist()

def get_price_data(engine, stock_list, start_date, end_date):
    """
    获取股票价格数据
    
    参数:
        engine: 数据库连接引擎
        stock_list: 股票列表
        start_date: 开始日期
        end_date: 结束日期
    
    返回:
        开盘价、收盘价和成交量数据框
    """
    # 将股票列表转换为SQL IN子句格式
    stocks_str = "', '".join(stock_list)
    
    # 构建SQL查询
    sql = f"""
    SELECT ts_code, trade_date, open_hfq as open, close_hfq as close, vol as volume
    FROM stk_factor
    WHERE ts_code IN ('{stocks_str}')
    AND trade_date BETWEEN '{start_date}' AND '{end_date}'
    ORDER BY trade_date
    """
    
    # 执行查询
    with engine.connect() as conn:
        df = pd.read_sql(sql, conn)
    
    # 将日期转换为字符串格式
    df['trade_date'] = df['trade_date'].astype(str)
    
    # 数据透视为宽格式
    open_price = df.pivot(index='trade_date', columns='ts_code', values='open')
    close_price = df.pivot(index='trade_date', columns='ts_code', values='close')
    volume = df.pivot(index='trade_date', columns='ts_code', values='volume')
    
    # 确保只保留在所有数据框中都存在的股票
    common_stocks = sorted(list(set(open_price.columns) & set(close_price.columns) & set(volume.columns)))
    open_price = open_price[common_stocks]
    close_price = close_price[common_stocks]
    volume = volume[common_stocks]
    
    return open_price, close_price, volume

def process_batch(batch_stocks, open_price, close_price, volume):
    """
    处理一批股票的alpha_2因子计算
    
    参数:
        batch_stocks: 一批股票代码
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        volume: 成交量数据框
    
    返回:
        计算得到的alpha_2因子值
    """
    # 确保只处理在所有数据框中都存在的股票
    valid_stocks = [stock for stock in batch_stocks if stock in open_price.columns and stock in close_price.columns and stock in volume.columns]
    
    if not valid_stocks:
        return pd.DataFrame()  # 如果没有有效股票，返回空数据框
    
    # 选择当前批次的股票数据
    batch_open = open_price[valid_stocks]
    batch_close = close_price[valid_stocks]
    batch_volume = volume[valid_stocks]
    
    # 计算alpha_2因子
    alpha = alpha_2(batch_open, batch_close, batch_volume)
    
    return alpha

def calculate_alpha_2_factor(stock_list, open_price, close_price, volume, num_processes=4):
    """
    使用多进程计算alpha_2因子
    
    参数:
        stock_list: 股票列表
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        volume: 成交量数据框
        num_processes: 进程数量
    
    返回:
        计算得到的alpha_2因子值
    """
    # 确保只处理在所有数据框中都存在的股票
    valid_stocks = [stock for stock in stock_list if stock in open_price.columns and stock in close_price.columns and stock in volume.columns]
    print(f"有效股票数量: {len(valid_stocks)}/{len(stock_list)}")
    
    if not valid_stocks:
        print("没有找到有效的股票数据，请检查数据获取过程")
        return pd.DataFrame()
    
    # 确定每个进程处理的股票数量
    batch_size = max(1, len(valid_stocks) // num_processes)
    
    # 将股票列表分成多个批次
    batches = [valid_stocks[i:i+batch_size] for i in range(0, len(valid_stocks), batch_size)]
    
    # 创建进程池
    pool = mp.Pool(processes=min(num_processes, len(batches)))
    
    # 提交任务到进程池
    results = []
    for batch in batches:
        result = pool.apply_async(process_batch, args=(batch, open_price, close_price, volume))
        results.append(result)
    
    # 关闭进程池
    pool.close()
    pool.join()
    
    # 合并结果
    alpha_factors = pd.DataFrame()
    for i, result in enumerate(results):
        batch_result = result.get()
        if batch_result.empty:
            continue
        if alpha_factors.empty:
            alpha_factors = batch_result
        else:
            alpha_factors = pd.concat([alpha_factors, batch_result], axis=1)
    
    return alpha_factors

def main():
    """
    主函数：获取数据，计算因子，保存结果
    """
    print("开始计算alpha_2因子...")
    start_time = time.time()
    
    # 创建数据库连接引擎
    engine = get_engine
    
    # 设置日期范围
    start_date = '2020-01-01'
    end_date = '2023-12-31'
    
    # 获取股票列表
    print("获取股票列表...")
    stock_list = get_stock_list(engine)
    print(f"共获取到 {len(stock_list)} 只股票")
    
    # 获取交易日期
    print("获取交易日期...")
    trade_dates = get_trade_dates(engine, start_date, end_date)
    print(f"共获取到 {len(trade_dates)} 个交易日")
    
    # 获取价格数据
    print("获取价格数据...")
    open_price, close_price, volume = get_price_data(engine, stock_list, start_date, end_date)
    print(f"数据形状 - 开盘价: {open_price.shape}, 收盘价: {close_price.shape}, 成交量: {volume.shape}")
    
    # 计算alpha_2因子
    print("计算alpha_2因子...")
    # 根据CPU核心数确定进程数，但最多使用7个进程（考虑到内存限制）
    num_processes = min(mp.cpu_count(), 7)
    print(f"使用 {num_processes} 个进程进行并行计算")
    
    alpha_factors = calculate_alpha_2_factor(stock_list, open_price, close_price, volume, num_processes)
    
    # 保存结果到CSV文件
    output_dir = os.path.dirname(os.path.abspath(__file__))
    output_file = os.path.join(output_dir, 'alpha_02_factors.csv')
    alpha_factors.to_csv(output_file)
    print(f"因子计算完成，结果已保存到: {output_file}")
    
    # 计算耗时
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"总耗时: {elapsed_time:.2f} 秒")

if __name__ == "__main__":
    main()
