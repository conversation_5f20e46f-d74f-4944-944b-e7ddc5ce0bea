"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/3/14 19:01
@Description: 
"""
from calculate_factors.alpha101.alpha_utils import *

def alpha_2(open_price: pd.DataFrame, close_price: pd.DataFrame, volume: pd.DataFrame) -> pd.DataFrame:
    """
    Alpha#2: (-1 * correlation(rank(delta(log(volume), 2)), rank(((close - open) / open)), 6))

    逻辑解释:
    1. 计算交易量的对数变化率（2天）
    2. 计算日内收益率（收盘价-开盘价）/开盘价
    3. 对上述两个序列进行横截面排名
    4. 计算这两个排名序列在过去6天的相关性
    5. 取相关性的负值作为因子值

    参数:
        open_price: 开盘价数据框
        close_price: 收盘价数据框
        volume: 交易量数据框

    返回:
        因子值
    """
    # 计算交易量的对数变化率（2天）
    volume_change = delta(np.log(volume), 2)

    # 计算日内收益率
    intraday_return = (close_price - open_price) / open_price

    # 对两个序列进行横截面排名
    rank_volume_change = rank(volume_change)
    rank_intraday_return = rank(intraday_return)

    # 计算相关性并取负值
    return -1 * correlation(rank_volume_change, rank_intraday_return, 6)