"""
@Author: JiangXin
@Date: 2025/3/14 20:03
@Description: 计算alpha_02因子值与股价的相关性分析
"""
import os
import sys
import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
sys.path.append(project_root)

# 导入自定义模块
from get_data.db_config import get_engine

def load_factor_data(file_path):
    """
    加载因子数据
    
    参数:
        file_path: 因子数据文件路径
    
    返回:
        因子数据框
    """
    try:
        df = pd.read_csv(file_path, index_col=0)
        print(f"成功读取因子数据，形状: {df.shape}")
        return df
    except Exception as e:
        print(f"读取因子数据时出错: {e}")
        return None

def get_stock_price_data(engine, stock_list, start_date, end_date):
    """
    获取股票价格数据
    
    参数:
        engine: 数据库连接引擎
        stock_list: 股票列表
        start_date: 开始日期
        end_date: 结束日期
    
    返回:
        股票价格数据框
    """
    try:
        # 将股票列表转换为SQL IN子句格式
        stocks_str = "', '".join(stock_list)
        
        # 构建SQL查询
        sql = f"""
        SELECT ts_code, trade_date, close_hfq as close
        FROM stk_factor
        WHERE ts_code IN ('{stocks_str}')
        AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        
        # 执行查询
        with engine.connect() as conn:
            df = pd.read_sql(sql, conn)
        
        # 将日期转换为字符串格式
        df['trade_date'] = df['trade_date'].astype(str)
        
        # 数据透视为宽格式
        price_data = df.pivot(index='trade_date', columns='ts_code', values='close')
        
        return price_data
    except Exception as e:
        print(f"获取股票价格数据时出错: {e}")
        return None

def calculate_correlations(factor_data, price_data):
    """
    计算每只股票的因子值与股价的相关性
    
    参数:
        factor_data: 因子数据框
        price_data: 价格数据框
    
    返回:
        相关性结果数据框
    """
    # 获取共同的日期和股票
    common_dates = sorted(list(set(factor_data.index) & set(price_data.index)))
    common_stocks = sorted(list(set(factor_data.columns) & set(price_data.columns)))
    
    print(f"开始计算相关性，共 {len(common_stocks)} 只股票...")
    
    # 准备结果数据
    results = []
    
    # 使用tqdm显示进度
    for stock in tqdm(common_stocks):
        # 获取当前股票的因子值和价格
        factor_values = factor_data.loc[common_dates, stock]
        price_values = price_data.loc[common_dates, stock]
        
        # 计算相关性
        correlation = factor_values.corr(price_values)
        
        # 计算因子值和价格的非空数据点数量
        valid_points = len(factor_values.dropna())
        
        # 将结果添加到列表中
        results.append({
            'stock_code': stock,
            'correlation': round(correlation, 4) if not pd.isna(correlation) else None,
            'valid_points': valid_points
        })
    
    # 转换为DataFrame
    results_df = pd.DataFrame(results)
    
    # 按相关性绝对值降序排序
    results_df['abs_correlation'] = results_df['correlation'].abs()
    results_df = results_df.sort_values('abs_correlation', ascending=False)
    
    # 删除辅助列
    results_df = results_df.drop('abs_correlation', axis=1)
    
    return results_df

def main():
    """
    主函数：加载数据，计算相关性，保存结果
    """
    print("开始计算alpha_02因子与股价的相关性...")
    
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    factor_file = os.path.join(current_dir, 'alpha_02_factors.csv')
    output_file = os.path.join(current_dir, 'alpha_02_correlations.csv')
    
    # 加载因子数据
    factor_data = load_factor_data(factor_file)
    if factor_data is None:
        print("无法继续，因子数据加载失败")
        return
    
    try:
        # 创建数据库连接引擎
        engine = get_engine
        
        # 设置日期范围（使用因子数据的日期范围）
        start_date = factor_data.index.min()
        end_date = factor_data.index.max()
        print(f"日期范围: {start_date} 至 {end_date}")
        
        # 获取有效的股票列表（非空列）
        valid_stocks = [col for col in factor_data.columns if factor_data[col].count() > 0]
        if not valid_stocks:
            print("因子数据中没有有效的股票数据")
            return
        
        print(f"有效股票数量: {len(valid_stocks)}")
        
        # 获取股票价格数据
        price_data = get_stock_price_data(engine, valid_stocks, start_date, end_date)
        if price_data is None:
            print("无法继续，股票价格数据获取失败")
            return
        
        # 计算相关性
        correlations = calculate_correlations(factor_data, price_data)
        
        # 保存结果到CSV文件
        correlations.to_csv(output_file, index=False)
        print(f"\n相关性计算完成，结果已保存到: {output_file}")
        
        # 打印一些统计信息
        print("\n相关性统计信息:")
        print(f"最高相关性: {correlations['correlation'].max():.4f}")
        print(f"最低相关性: {correlations['correlation'].min():.4f}")
        print(f"平均相关性: {correlations['correlation'].mean():.4f}")
        print(f"相关性标准差: {correlations['correlation'].std():.4f}")
        
        # 打印相关性最高的前10只股票
        print("\n相关性最高的前10只股票:")
        print(correlations.head(10))
        
    except Exception as e:
        print(f"执行过程中出错: {e}")

if __name__ == "__main__":
    main()
