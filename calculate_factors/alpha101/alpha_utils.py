"""
Alpha101因子工具函数库

该模块提供了实现WorldQuant Alpha101因子所需的基础函数和操作符。
这些函数和操作符基于原始论文中的定义实现，用于因子计算。
"""

import numpy as np
import pandas as pd
from typing import Union, Callable


def rank(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算横截面排名（cross-sectional rank）
    
    参数:
        df: 输入数据框
        
    返回:
        排名后的数据框，值范围在[0, 1]之间
    """
    return df.rank(axis=1, pct=True)


def delay(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    获取d天前的数据
    
    参数:
        df: 输入数据框
        d: 延迟天数
        
    返回:
        延迟d天的数据
    """
    return df.shift(d)


def correlation(x: pd.DataFrame, y: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天x和y的时间序列相关性
    
    参数:
        x: 第一个输入数据框
        y: 第二个输入数据框
        d: 回溯天数
        
    返回:
        x和y在过去d天的滚动相关系数
    """
    return x.rolling(d).corr(y)


def covariance(x: pd.DataFrame, y: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天x和y的时间序列协方差
    
    参数:
        x: 第一个输入数据框
        y: 第二个输入数据框
        d: 回溯天数
        
    返回:
        x和y在过去d天的滚动协方差
    """
    return x.rolling(d).cov(y)


def scale(df: pd.DataFrame, a: float = 1.0) -> pd.DataFrame:
    """
    缩放数据，使得绝对值之和等于a
    
    参数:
        df: 输入数据框
        a: 缩放目标值，默认为1.0
        
    返回:
        缩放后的数据框
    """
    return df.div(df.abs().sum(axis=1), axis=0).mul(a, axis=0)


def delta(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算当前值与d天前值的差
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        当前值与d天前值的差值
    """
    return df - df.shift(d)


def signedpower(df: pd.DataFrame, a: float) -> pd.DataFrame:
    """
    计算带符号的幂函数
    
    参数:
        df: 输入数据框
        a: 幂指数
        
    返回:
        带符号的幂运算结果
    """
    return np.sign(df) * np.abs(df) ** a


def decay_linear(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算线性衰减的加权移动平均
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        线性衰减加权的移动平均值
    """
    # 创建权重序列：d, d-1, ..., 1
    weights = np.arange(1, d + 1)[::-1]
    # 归一化权重使其和为1
    weights = weights / weights.sum()
    
    # 应用加权移动平均
    return df.rolling(d).apply(lambda x: np.sum(weights * x[-d:]), raw=False)


def ts_min(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天的时间序列最小值
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天的最小值
    """
    return df.rolling(d).min()


def ts_max(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天的时间序列最大值
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天的最大值
    """
    return df.rolling(d).max()


def ts_argmax(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天内最大值出现的位置
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天内最大值出现的相对位置（0表示当前，d-1表示d天前）
    """
    return df.rolling(d).apply(np.argmax, raw=True)


def ts_argmin(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天内最小值出现的位置
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天内最小值出现的相对位置（0表示当前，d-1表示d天前）
    """
    return df.rolling(d).apply(np.argmin, raw=True)


def ts_rank(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天的时间序列排名
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天的排名，值范围在[0, 1]之间
    """
    return df.rolling(d).apply(lambda x: pd.Series(x).rank(pct=True).iloc[-1], raw=False)


def sum(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天的时间序列总和
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天的总和
    """
    return df.rolling(d).sum()


def product(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天的时间序列乘积
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天的乘积
    """
    return df.rolling(d).apply(np.prod, raw=True)


def stddev(df: pd.DataFrame, d: int) -> pd.DataFrame:
    """
    计算过去d天的时间序列标准差
    
    参数:
        df: 输入数据框
        d: 回溯天数
        
    返回:
        过去d天的标准差
    """
    return df.rolling(d).std()
