__pycache__/
*.pyc
code_demo/backtest_results/
.vscode
/prompt/
code_demo/log_return/output
code_demo/log_return/output_analysis
code_demo/log_return/output_analysis_deviation

get_data/db_config.py

# IDE
.idea/
.vscode/
/code_demo/sperman/factor_analysis_results/
/code_demo/QMT/output/
/give_up/value/circ_mv_by_industry_2025-04-17.csv
/give_up/value/circ_mv_by_market_2025-04-17.csv
/give_up/value/circ_mv_distribution_2025-04-17.png
/give_up/value/circ_mv_industry_box_2025-04-17.png
/give_up/value/circ_mv_industry_kde_2025-04-17.png
/give_up/value/circ_mv_market_box_2025-04-17.png
/give_up/value/circ_mv_market_kde_2025-04-17.png
/give_up/value/circ_mv_overall_2025-04-17.csv
/agent/report/
/calculate_factors/adx/adx_output/
/big_a_compass/up_and_down_results/
/big_a_compass/training_data/
/big_a_compass/sw/results/
/get_data/adx_calculation.log
