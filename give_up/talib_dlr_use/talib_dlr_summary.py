"""
@Author: Jiang<PERSON>in
@Date: 2025/1/6
@Description: 统计最新交易日的某个股票K线形态识别结果
"""
import talib
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import create_engine

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': '123456',
    'database': 'tushare',
    'port': 3306,
    'charset': 'utf8mb4'
}

# 构建数据库连接URL
DB_URL = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"

# 创建数据库连接引擎
engine = create_engine(DB_URL)

def get_stock_data(ts_code='000001.SZ', lookback_days=100):
    """
    获取股票的K线数据
    
    Args:
        ts_code: 股票代码
        lookback_days: 回看的天数
    
    Returns:
        tuple: (dates, open, high, low, close) 历史数据
    """
    try:
        # 计算日期范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=lookback_days)).strftime('%Y-%m-%d')
        
        # 构建SQL查询，使用后复权价格
        query = f"""
        SELECT ts_code, trade_date, 
               open_hfq as open, 
               high_hfq as high, 
               low_hfq as low, 
               close_hfq as close
        FROM stk_factor
        WHERE ts_code = '{ts_code}'
        AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        
        # 执行查询
        df = pd.read_sql(query, engine)
        
        if df.empty:
            raise ValueError(f"未找到股票 {ts_code} 的数据")
        
        print(f"\n成功获取 {len(df)} 条历史数据")
        print(f"数据日期范围: {df['trade_date'].min()} 至 {df['trade_date'].max()}")
        
        # 转换为numpy数组
        dates = df['trade_date'].values
        open_data = df['open'].astype(float).values
        high_data = df['high'].astype(float).values
        low_data = df['low'].astype(float).values
        close_data = df['close'].astype(float).values
        
        return dates, open_data, high_data, low_data, close_data
        
    except Exception as e:
        print(f"获取数据失败: {str(e)}")
        return None, None, None, None, None

def analyze_latest_patterns(ts_code='000001.SZ'):
    """
    分析最新交易日的K线形态
    
    Args:
        ts_code: 股票代码
    
    Returns:
        tuple: (上涨形态数量, 下跌形态数量, 详细信息)
    """
    # 获取数据
    dates, open_data, high_data, low_data, close_data = get_stock_data(ts_code)
    if dates is None:
        return 0, 0, "获取数据失败"

    # 获取所有K线形态识别函数
    pattern_funcs = [func for func in dir(talib) if func.startswith('CDL')]
    
    # 统计最新交易日的形态
    bullish_count = 0  # 看涨形态计数
    bearish_count = 0  # 看跌形态计数
    details = []       # 详细信息
    
    for pattern in pattern_funcs:
        try:
            # 获取形态识别函数
            pattern_func = getattr(talib, pattern)
            # 计算形态
            result = pattern_func(open_data, high_data, low_data, close_data)
            
            # 检查最新的信号
            latest_signal = result[-1]
            if latest_signal != 0:
                pattern_name = pattern[3:]  # 移除'CDL'前缀
                if latest_signal > 0:
                    bullish_count += 1
                    details.append(f"看涨形态: {pattern_name} (强度: {latest_signal})")
                else:
                    bearish_count += 1
                    details.append(f"看跌形态: {pattern_name} (强度: {abs(latest_signal)})")
        except Exception as e:
            print(f"计算{pattern}形态时出错: {str(e)}")
            continue
    
    return bullish_count, bearish_count, details

def main():
    """主函数"""
    ts_code = input("请输入股票代码（例如：000001.SZ）：")
    
    print(f"\n正在分析股票 {ts_code} 的最新K线形态...")
    bullish_count, bearish_count, details = analyze_latest_patterns(ts_code)
    
    print(f"\n最新交易日形态识别结果:")
    print(f"上涨形态：{bullish_count} 个")
    print(f"下跌形态：{bearish_count} 个")
    
    if details:
        print("\n详细信息:")
        for detail in details:
            print(detail)
    else:
        print("\n没有检测到明显的形态信号")

if __name__ == "__main__":
    main()
