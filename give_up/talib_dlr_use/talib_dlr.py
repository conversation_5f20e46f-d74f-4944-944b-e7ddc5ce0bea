"""
@Author: JiangXin
@Date: 2025/1/5 19:26
@Description: 使用 TA-Lib 进行K线形态识别
"""
import time
import numpy as np
import talib
import tkinter as tk
from tkinter import messagebox
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import create_engine

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': '123456',
    'database': 'tushare',
    'port': 3306,
    'charset': 'utf8mb4'
}

# 构建数据库连接URL
DB_URL = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"

# 创建数据库连接引擎
engine = create_engine(DB_URL)

# 获取所有TA-Lib的K线形态识别函数
def get_all_cdl_patterns():
    """返回所有TA-Lib的K线形态识别函数"""
    cdl_patterns = [
        func for func in dir(talib)
        if func.startswith('CDL') and callable(getattr(talib, func))
    ]
    return cdl_patterns

# 从数据库获取实时数据
def get_real_time_data(ts_code='000001.SZ', lookback_days=100):
    """
    获取股票的最新K线数据
    
    Args:
        ts_code: 股票代码，默认为平安银行
        lookback_days: 回看的天数，用于计算技术指标
        
    Returns:
        tuple: (dates, open, high, low, close) 历史数据
    """
    try:
        # 计算日期范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=lookback_days)).strftime('%Y-%m-%d')
        
        # 构建SQL查询
        query = f"""
        SELECT ts_code, trade_date, open, high, low, close
        FROM no_rights_price_daily
        WHERE ts_code = '{ts_code}'
        AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        
        # 执行查询
        df = pd.read_sql(query, engine)
        
        if df.empty:
            raise ValueError(f"未找到股票 {ts_code} 的数据")
        
        print(f"\n成功获取 {len(df)} 条历史数据")
        print(f"数据日期范围: {df['trade_date'].min()} 至 {df['trade_date'].max()}")
            
        # 转换为numpy数组
        dates = df['trade_date'].values
        open_data = df['open'].astype(float).values
        high_data = df['high'].astype(float).values
        low_data = df['low'].astype(float).values
        close_data = df['close'].astype(float).values
        
        return dates, open_data, high_data, low_data, close_data
        
    except Exception as e:
        print(f"获取数据失败: {str(e)}")
        # 如果获取失败，返回模拟数据
        np.random.seed(int(time.time()))
        size = lookback_days
        dates = pd.date_range(end=datetime.now(), periods=size).values
        open = np.random.random(size) * 100
        high = open + np.random.random(size) * 10
        low = open - np.random.random(size) * 10
        close = open + (np.random.random(size) - 0.5) * 5
        return dates, open, high, low, close

# 弹出系统窗口消息
def show_message(signal_name):
    """在系统窗口中显示消息"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    messagebox.showinfo("K线形态信号", f"检测到 {signal_name} 信号！")
    root.destroy()

# 格式化形态识别结果
def format_pattern_result(dates, pattern_name, result):
    """
    格式化形态识别结果
    
    Args:
        dates: 日期序列
        pattern_name: 形态名称
        result: 识别结果数组
    
    Returns:
        str: 格式化后的结果字符串
    """
    # 找出所有非零信号的位置
    signal_days = np.where(result != 0)[0]
    if len(signal_days) == 0:
        return None
        
    # 获取信号详情
    signals = []
    for idx in signal_days:
        signal_type = "看涨" if result[idx] > 0 else "看跌"
        signal_strength = abs(result[idx])
        signal_date = dates[idx]
        signals.append(f"{signal_date}: {signal_type}(强度:{signal_strength})")
    
    return f"{pattern_name} 形态:\n" + "\n".join(signals)

# 主程序
def main():
    """实时K线识别主程序"""
    # 获取所有K线形态识别函数
    cdl_patterns = get_all_cdl_patterns()
    print(f"支持的K线形态数量: {len(cdl_patterns)}")
    print("支持的K线形态列表:", cdl_patterns)

    try:
        # 获取实时数据
        dates, open, high, low, close = get_real_time_data(lookback_days=100)
        
        # 检查数据是否有效
        if any(map(lambda x: not isinstance(x, (np.ndarray)), [dates, open, high, low, close])):
            raise ValueError("数据类型无效")
            
        print(f"\n当前最新K线数据 ({dates[-1]}):")
        print(f"开盘价: {open[-1]:.2f}")
        print(f"最高价: {high[-1]:.2f}")
        print(f"最低价: {low[-1]:.2f}")
        print(f"收盘价: {close[-1]:.2f}")

        # 记录识别到的形态
        detected_patterns = []

        # 遍历每个K线形态
        for pattern in cdl_patterns:
            try:
                # 获取TA-Lib的形态识别函数
                cdl_func = getattr(talib, pattern)
                print(f"\n正在识别 {pattern} 形态...")

                # 调用函数识别形态
                result = cdl_func(open, high, low, close)
                
                # 格式化并检查结果
                pattern_result = format_pattern_result(dates, pattern, result)
                if pattern_result:
                    detected_patterns.append(pattern_result)
                    print(pattern_result)
                    # 如果最近一天有信号，显示通知
                    if result[-1] != 0:
                        signal_type = "看涨" if result[-1] > 0 else "看跌"
                        show_message(f"{pattern}: {signal_type}")

            except Exception as e:
                print(f"识别 {pattern} 形态时出错: {str(e)}")
                continue

        # 输出识别结果摘要
        print("\n识别结果摘要:")
        if detected_patterns:
            print("检测到以下形态:")
            for pattern in detected_patterns:
                print(f"- {pattern}")
        else:
            print("未检测到任何特征形态")

    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        return

    # 等待一段时间后继续下一轮检测
    time.sleep(0.1)

if __name__ == "__main__":
    main()