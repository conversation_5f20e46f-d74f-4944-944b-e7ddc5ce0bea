"""
@Author: JiangXin
@Date: 2025/1/6
@Description: 批量分析最新交易日的K线形态信号
"""
import talib
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import create_engine
import concurrent.futures

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': '123456',
    'database': 'tushare',
    'port': 3306,
    'charset': 'utf8mb4'
}

# 构建数据库连接URL
DB_URL = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}"

# 创建数据库连接引擎
engine = create_engine(DB_URL)

def get_latest_trade_date():
    """获取最新交易日期"""
    query = """
    SELECT MAX(trade_date) as latest_date
    FROM stk_factor
    """
    df = pd.read_sql(query, engine)
    return df['latest_date'].iloc[0]

def get_stock_list():
    """获取股票列表"""
    latest_date = get_latest_trade_date()
    query = f"""
    SELECT DISTINCT ts_code 
    FROM stk_factor 
    WHERE trade_date = '{latest_date}'
    """
    df = pd.read_sql(query, engine)
    return df['ts_code'].tolist(), latest_date

def get_stock_data(ts_code, end_date, lookback_days=100):
    """
    获取股票数据，包含足够的历史数据用于技术指标计算
    
    Args:
        ts_code: 股票代码
        end_date: 结束日期
        lookback_days: 历史数据天数
    """
    try:
        start_date = (pd.to_datetime(end_date) - timedelta(days=lookback_days)).strftime('%Y-%m-%d')
        query = f"""
        SELECT ts_code, trade_date, 
               open_hfq as open, 
               high_hfq as high, 
               low_hfq as low, 
               close_hfq as close
        FROM stk_factor
        WHERE ts_code = '{ts_code}'
        AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        return pd.read_sql(query, engine)
    except Exception as e:
        print(f"获取{ts_code}数据失败: {str(e)}")
        return None

def analyze_latest_patterns(df):
    """
    分析最新交易日的K线形态和技术指标
    """
    if df is None or df.empty or len(df) < 2:
        return None

    # 转换数据
    open_data = df['open'].astype(float).values
    high_data = df['high'].astype(float).values
    low_data = df['low'].astype(float).values
    close_data = df['close'].astype(float).values
    
    # 计算技术指标
    try:
        # MACD
        macd, macd_signal, macd_hist = talib.MACD(close_data)
        # RSI
        rsi6 = talib.RSI(close_data, timeperiod=6)
        rsi12 = talib.RSI(close_data, timeperiod=12)
        rsi24 = talib.RSI(close_data, timeperiod=24)
        # BOLL
        upper, middle, lower = talib.BBANDS(close_data, timeperiod=20)
        # KDJ (使用 STOCH 函数模拟)
        k, d = talib.STOCH(high_data, low_data, close_data, 
                          fastk_period=9, slowk_period=3, slowk_matype=0,
                          slowd_period=3, slowd_matype=0)
        j = 3 * k - 2 * d
        
        # 获取最新的指标值
        tech_indicators = {
            'MACD': {
                'DIFF': round(macd[-1], 4) if not np.isnan(macd[-1]) else None,
                'DEA': round(macd_signal[-1], 4) if not np.isnan(macd_signal[-1]) else None,
                'HIST': round(macd_hist[-1], 4) if not np.isnan(macd_hist[-1]) else None
            },
            'RSI': {
                'RSI6': round(rsi6[-1], 2) if not np.isnan(rsi6[-1]) else None,
                'RSI12': round(rsi12[-1], 2) if not np.isnan(rsi12[-1]) else None,
                'RSI24': round(rsi24[-1], 2) if not np.isnan(rsi24[-1]) else None
            },
            'BOLL': {
                'UPPER': round(upper[-1], 2) if not np.isnan(upper[-1]) else None,
                'MIDDLE': round(middle[-1], 2) if not np.isnan(middle[-1]) else None,
                'LOWER': round(lower[-1], 2) if not np.isnan(lower[-1]) else None
            },
            'KDJ': {
                'K': round(k[-1], 2) if not np.isnan(k[-1]) else None,
                'D': round(d[-1], 2) if not np.isnan(d[-1]) else None,
                'J': round(j[-1], 2) if not np.isnan(j[-1]) else None
            }
        }
        
        # 分析技术指标信号
        signals = []
        
        # MACD信号
        if macd_hist[-1] > 0 and macd_hist[-2] <= 0:
            signals.append("MACD金叉")
        elif macd_hist[-1] < 0 and macd_hist[-2] >= 0:
            signals.append("MACD死叉")
            
        # RSI信号
        if rsi6[-1] < 20:
            signals.append("RSI6超卖")
        elif rsi6[-1] > 80:
            signals.append("RSI6超买")
            
        # KDJ信号
        if k[-1] < 20 and d[-1] < 20:
            signals.append("KDJ超卖")
        elif k[-1] > 80 and d[-1] > 80:
            signals.append("KDJ超买")
        if k[-1] > d[-1] and k[-2] <= d[-2]:
            signals.append("KDJ金叉")
        elif k[-1] < d[-1] and k[-2] >= d[-2]:
            signals.append("KDJ死叉")
            
        # BOLL信号
        if close_data[-1] > upper[-1]:
            signals.append("BOLL上轨突破")
        elif close_data[-1] < lower[-1]:
            signals.append("BOLL下轨突破")
            
    except Exception as e:
        print(f"计算技术指标时出错: {str(e)}")
        tech_indicators = {}
        signals = []
    
    # K线形态分析
    pattern_funcs = [func for func in dir(talib) if func.startswith('CDL')]
    bullish_patterns = []
    bearish_patterns = []
    
    for pattern in pattern_funcs:
        try:
            pattern_func = getattr(talib, pattern)
            result = pattern_func(open_data, high_data, low_data, close_data)
            signal = result[-1]  # 只看最新的信号
            
            if signal != 0:
                pattern_name = pattern[3:]  # 移除'CDL'前缀
                if signal > 0:
                    bullish_patterns.append(pattern_name)
                else:
                    bearish_patterns.append(pattern_name)
        except Exception as e:
            continue
    
    return {
        'ts_code': df['ts_code'].iloc[-1],
        'trade_date': df['trade_date'].iloc[-1],
        'close': round(float(close_data[-1]), 2),
        'bullish_count': len(bullish_patterns),
        'bearish_count': len(bearish_patterns),
        'bullish_patterns': ','.join(bullish_patterns) if bullish_patterns else '',
        'bearish_patterns': ','.join(bearish_patterns) if bearish_patterns else '',
        'tech_indicators': tech_indicators,
        'tech_signals': ','.join(signals) if signals else ''
    }

def process_stock(ts_code, latest_date):
    """处理单个股票"""
    print(f"正在处理: {ts_code}")
    df = get_stock_data(ts_code, latest_date)
    if df is not None and not df.empty:
        return analyze_latest_patterns(df)
    return None

def batch_analyze(max_workers=4):
    """批量分析主函数"""
    # 获取股票列表和最新交易日
    stock_list, latest_date = get_stock_list()
    print(f"最新交易日: {latest_date}")
    print(f"共获取到 {len(stock_list)} 只股票")
    
    all_results = []
    
    # 使用线程池进行并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_stock = {
            executor.submit(process_stock, stock, latest_date): stock 
            for stock in stock_list
        }
        
        for future in concurrent.futures.as_completed(future_to_stock):
            stock = future_to_stock[future]
            try:
                result = future.result()
                if result:
                    all_results.append(result)
            except Exception as e:
                print(f"处理{stock}时出错: {str(e)}")
    
    # 转换为DataFrame并保存
    if all_results:
        results_df = pd.DataFrame(all_results)
        
        # 按上涨信号数量降序排序
        results_df = results_df.sort_values('bullish_count', ascending=False)
        
        # 保存结果
        output_file = f'pattern_analysis_{latest_date}.csv'
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n分析结果已保存到: {output_file}")
        
        # 显示汇总统计
        print("\n信号统计:")
        print(f"股票总数: {len(results_df)}")
        print(f"有上涨信号的股票数: {len(results_df[results_df['bullish_count'] > 0])}")
        print(f"有下跌信号的股票数: {len(results_df[results_df['bearish_count'] > 0])}")
        
        # 显示信号最多的前10只股票
        print("\n上涨信号最多的前10只股票:")
        top_10 = results_df[results_df['bullish_count'] > 0].head(10)
        for _, row in top_10.iterrows():
            print(f"\n股票代码: {row['ts_code']} 收盘价: {row['close']}")
            print(f"上涨信号数: {row['bullish_count']}")
            print(f"上涨形态: {row['bullish_patterns']}")
            print(f"下跌信号数: {row['bearish_count']}")
            if row['bearish_patterns']:
                print(f"下跌形态: {row['bearish_patterns']}")
            print("技术指标:")
            for indicator, values in row['tech_indicators'].items():
                print(f"  {indicator}: {values}")
            if row['tech_signals']:
                print(f"技术指标信号: {row['tech_signals']}")
    else:
        print("没有找到任何形态信号")

if __name__ == "__main__":
    batch_analyze()
