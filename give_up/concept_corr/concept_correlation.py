import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from sqlalchemy import create_engine, text
import sys
import os
import datetime

# 将项目根目录添加到sys.path，以解决ModuleNotFoundError
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from get_data.db_config import get_engine  # 遵从项目规则，引用get_engine函数

def get_industry_members(industry_names: list, db_engine):
    """
    根据行业名称列表，获取所有成分股的代码和行业的映射关系。
    """
    if not industry_names:
        return pd.DataFrame()

    sql = text("SELECT ts_code, name, industry FROM stock_basic WHERE industry IN :industry_names")
    df = pd.read_sql(sql, db_engine, params={'industry_names': tuple(industry_names)})
    
    if df.empty:
        print("错误：在stock_basic表中找不到指定行业。请检查行业名称是否正确。")
        return pd.DataFrame()

    print(f"成功从 'stock_basic' 表获取 {len(df)} 条记录，覆盖 {len(df['industry'].unique())} 个行业。")
    return df[['ts_code', 'industry']].rename(columns={'industry': 'sector_name'})


def get_stock_prices(stock_codes: list, start_date: str, end_date: str, db_engine):
    """
    获取指定股票列表在时间范围内的后复权收盘价。
    """
    if not stock_codes:
        return pd.DataFrame()
        
    sql_prices = text("""
        SELECT ts_code, trade_date, close_hfq
        FROM stk_factor
        WHERE ts_code IN :stock_codes
        AND trade_date BETWEEN :start_date AND :end_date
    """)
    
    # 分块查询以避免单次查询数据量过大
    chunk_size = 500
    all_prices = []
    for i in range(0, len(stock_codes), chunk_size):
        chunk = stock_codes[i:i+chunk_size]
        prices_df = pd.read_sql(sql_prices, db_engine, params={
            'stock_codes': tuple(chunk),
            'start_date': start_date,
            'end_date': end_date
        })
        all_prices.append(prices_df)
        print(f"  已获取 {len(chunk)} 只股票的价格数据...")
    
    if not all_prices:
        return pd.DataFrame()
        
    final_df = pd.concat(all_prices, ignore_index=True)
    print(f"总计获取了从 {start_date} 到 {end_date} 的 {len(final_df)} 条价格记录。")
    return final_df


def main():
    """
    主函数：执行行业板块相关性分析和可视化。
    """
    # --- 1. 配置参数 ---
    # 您可以在这里修改您感兴趣的行业板块列表
    # 常见的行业有：银行, 软件服务, 半导体, 医疗保健, 证券, 保险, 房地产, 工程机械, 电力, 汽车整车, 酿酒
    TARGET_SECTORS = ['银行', '软件服务', '半导体', '证券', '酿酒', '医疗保健']
    
    # 设置分析的时间范围
    END_DATE = datetime.date.today()
    START_DATE = END_DATE - datetime.timedelta(days=365)
    
    # --- 2. 获取数据 ---
    print("开始从数据库获取数据...")
    engine = get_engine()
    industry_members = get_industry_members(TARGET_SECTORS, engine)
    
    if industry_members.empty:
        print("无法获取成分股，程序终止。")
        return

    all_stock_codes = industry_members['ts_code'].unique().tolist()
    
    stock_prices = get_stock_prices(all_stock_codes, START_DATE.strftime('%Y-%m-%d'), END_DATE.strftime('%Y-%m-%d'), engine)

    if stock_prices.empty:
        print("无法获取股价数据，程序终止。")
        return

    # --- 3. 数据处理与计算 ---
    print("开始计算板块日收益率...")
    price_pivot = stock_prices.pivot(index='trade_date', columns='ts_code', values='close_hfq')
    daily_returns = price_pivot.pct_change().dropna(how='all')
    
    sector_returns = {}
    for sector_name in TARGET_SECTORS:
        member_codes = industry_members[industry_members['sector_name'] == sector_name]['ts_code'].tolist()
        valid_codes = [code for code in member_codes if code in daily_returns.columns]
        
        if valid_codes:
            sector_returns[sector_name] = daily_returns[valid_codes].mean(axis=1)

    sector_returns_df = pd.DataFrame(sector_returns).dropna()
    
    if sector_returns_df.empty:
        print("计算出的板块收益率为空，可能是在此期间所有相关股票数据都缺失。程序终止。")
        return
        
    # --- 4. 计算相关性并可视化 ---
    print("计算相关性矩阵并生成热力图...")
    correlation_matrix = sector_returns_df.corr()

    plt.rcParams['font.sans-serif'] = ['STHeiti']
    plt.rcParams['axes.unicode_minus'] = False

    plt.figure(figsize=(12, 10))
    sns.heatmap(
        correlation_matrix,
        annot=True,
        cmap='coolwarm',
        linewidths=.5,
        fmt='.2f'
    )
    plt.title('行业板块年度相关性分析', fontsize=16)
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    # --- 5. 保存图表 ---
    output_dir = 'analyize'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    output_path = os.path.join(output_dir, f'industry_correlation_{timestamp}.png')
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    
    print("-" * 50)
    print("分析完成！")
    print(f"相关性矩阵已计算，热力图已保存至: {os.path.abspath(output_path)}")
    print("相关性矩阵:")
    print(correlation_matrix)
    print("-" * 50)

if __name__ == '__main__':
    main() 