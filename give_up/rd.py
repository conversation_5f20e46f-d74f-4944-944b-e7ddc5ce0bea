"""
@Author: JiangXin
@Date: 2025/1/3 20:43
@Description: 
    这是一个复合因子计算模型，结合了以下几个维度：
    1. 波动率：计算50天滚动波动率
    2. 情绪指标：使用1-3个月的加权强度
    3. 机器学习调整：基于基本面指标的随机森林模型
    4. 宏观经济指标：包含销售和利润增长
"""
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import make_pipeline
from sklearn.model_selection import train_test_split

def calculate_volatility_sentiment_50d_enhanced_macro_ensemble():
    """
    计算增强型波动率-情绪-宏观集成因子
    
    数据处理流程：
    1. 读取日频行情数据和因子数据
    2. 计算50日波动率
    3. 合成增强型情绪指标
    4. 使用机器学习模型进行动态调整
    5. 整合宏观经济指标
    """
    # 加载数据
    daily_pv = pd.read_hdf('daily_pv.h5', key='data')
    daily_f = pd.read_hdf('daily_f.h5', key='data')

    # 计算50日波动率（日收益率的标准差）
    daily_pv['returns'] = daily_pv.groupby('instrument')['$close'].pct_change()
    volatility_50d = daily_pv.groupby('instrument')['returns'].rolling(window=50).std().reset_index(level=0, drop=True)

    # 增强型情绪分析（综合多个情绪来源）
    sentiment_enhanced = daily_f[['Weighted_Strength_1M', 'Weighted_Strength_2M', 'Weighted_Strength_3M']].mean(axis=1)

    # 基于机器学习的动态调整
    # 使用基本面指标构建特征矩阵
    X = daily_f[['E/P', 'B/P', 'S/P']].fillna(0)  # 市盈率、市净率、市销率
    y = daily_f['净利润同比'].fillna(0)
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    # 构建随机森林模型管道，包含数据标准化
    model = make_pipeline(StandardScaler(), RandomForestRegressor(n_estimators=100, random_state=42))
    model.fit(X_train, y_train)
    dynamic_adjustments_ml = model.predict(X)

    # 使用集成方法聚合宏观经济指标
    # 目前使用简单平均，后续可扩展为更复杂的集成方法
    macro_indicators_ensemble = daily_f[['Sales_Growth_TTM', 'Profit_Growth_TTM']].mean(axis=1)

    # 合并所有组件形成最终因子
    factor_values = (volatility_50d + sentiment_enhanced + dynamic_adjustments_ml + macro_indicators_ensemble).fillna(0)
    factor_values = factor_values.reset_index()
    factor_values.columns = ['datetime', 'instrument', 'Volatility_Sentiment_50D_Enhanced_Macro_Ensemble']
    factor_values.set_index(['datetime', 'instrument'], inplace=True)

    # 将结果保存为HDF5文件
    factor_values.to_hdf('result.h5', key='data')

if __name__ == '__main__':
    calculate_volatility_sentiment_50d_enhanced_macro_ensemble()