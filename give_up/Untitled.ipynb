{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4d747ede-2921-409d-9beb-66560ad410da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          A         B         C\n", "A  1.000000  0.112229 -0.770244\n", "B  0.112229  1.000000 -0.439029\n", "C -0.770244 -0.439029  1.000000\n", "          A         B         C\n", "A  1.000000 -0.042424 -0.648485\n", "B -0.042424  1.000000 -0.381818\n", "C -0.648485 -0.381818  1.000000\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# 创建一个示例 DataFrame\n", "data = {\n", "    'A': np.random.rand(10),\n", "    'B': np.random.rand(10),\n", "    'C': np.random.rand(10)\n", "}\n", "df = pd.DataFrame(data)\n", "\n", "# 计算皮尔逊相关系数矩阵\n", "corr_matrix = df.corr()\n", "print(corr_matrix)\n", "\n", "# 计算斯皮尔曼相关系数矩阵\n", "spearman_corr = df.corr(method='spearman')\n", "print(spearman_corr)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "dedc94f1-371b-4908-9793-f674ef30d7b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["皮尔逊相关系数矩阵:\n", "           A         B         C\n", "A  1.000000  0.774597 -1.000000\n", "B  0.774597  1.000000 -0.774597\n", "C -1.000000 -0.774597  1.000000\n", "\n", "斯皮尔曼秩相关系数矩阵:\n", "           A         B         C\n", "A  1.000000  0.737865 -1.000000\n", "B  0.737865  1.000000 -0.737865\n", "C -1.000000 -0.737865  1.000000\n", "\n", "肯德尔秩相关系数矩阵:\n", "          A        B        C\n", "A  1.00000  0.67082 -1.00000\n", "B  0.67082  1.00000 -0.67082\n", "C -1.00000 -0.67082  1.00000\n", "\n", "考虑缺失值（min_periods=3）的相关系数矩阵:\n", "           A         B         C\n", "A  1.000000  0.995871 -1.000000\n", "B  0.995871  1.000000 -0.828079\n", "C -1.000000 -0.828079  1.000000\n", "\n", "仅数值列的相关系数矩阵:\n", "      A    C\n", "A  1.0 -1.0\n", "C -1.0  1.0\n", "\n", "手动选择数值列计算的相关系数矩阵:\n", "      A    C\n", "A  1.0 -1.0\n", "C -1.0  1.0\n"]}], "source": ["import pandas as pd\n", "\n", "# 创建一个示例 DataFrame\n", "data = {'A': [1, 2, 3, 4, 5],\n", "        'B': [2, 4, 5, 4, 5],\n", "        'C': [5, 4, 3, 2, 1]}\n", "df = pd.DataFrame(data)\n", "\n", "# 计算皮尔逊相关系数 (默认)\n", "correlation_matrix = df.corr()\n", "print(\"皮尔逊相关系数矩阵:\\n\", correlation_matrix)\n", "\n", "# 计算斯皮尔曼秩相关系数\n", "spearman_corr = df.corr(method='spearman')\n", "print(\"\\n斯皮尔曼秩相关系数矩阵:\\n\", spearman_corr)\n", "\n", "# 计算肯德尔秩相关系数\n", "kendall_corr = df.corr(method='kendall')\n", "print(\"\\n肯德尔秩相关系数矩阵:\\n\", kendall_corr)\n", "\n", "\n", "# 包含缺失值的示例\n", "data_missing = {'A': [1, 2, None, 4, 5],\n", "                'B': [2, None, 5, 4, 5],\n", "                'C': [5, 4, 3, 2, 1]}\n", "df_missing = pd.DataFrame(data_missing)\n", "\n", "# 使用 min_periods 处理缺失值\n", "correlation_matrix_min_periods = df_missing.corr(min_periods=3)  # 每对列至少需要3个非缺失值\n", "print(\"\\n考虑缺失值（min_periods=3）的相关系数矩阵:\\n\", correlation_matrix_min_periods)\n", "\n", "\n", "# 包含非数值列的示例 (pandas 1.5.0+)\n", "data_mixed = {'A': [1, 2, 3, 4, 5],\n", "              'B': ['a', 'b', 'c', 'd', 'e'],\n", "              'C': [5, 4, 3, 2, 1]}\n", "df_mixed = pd.DataFrame(data_mixed)\n", "\n", "# numeric_only=True 只计算数值列的相关性\n", "try:\n", "    correlation_matrix_numeric = df_mixed.corr(numeric_only=True)\n", "    print(\"\\n仅数值列的相关系数矩阵:\\n\", correlation_matrix_numeric)\n", "except TypeError as e:\n", "    print(f\"出现错误: {e}\")\n", "    print(\"请确保你的 pandas 版本大于等于 1.5.0，或者手动选择数值列进行计算。\")\n", "\n", "# 手动选择数值列计算相关性（兼容旧版本）\n", "df_numeric = df_mixed[['A', 'C']] # 选择 A 和 C 列\n", "correlation_matrix_manual = df_numeric.corr()\n", "print(\"\\n手动选择数值列计算的相关系数矩阵:\\n\", correlation_matrix_manual)\n"]}, {"cell_type": "code", "execution_count": null, "id": "46950c3b-32b0-4dad-88ab-07b6ee2fefc5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}