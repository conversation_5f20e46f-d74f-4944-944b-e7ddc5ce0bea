import time

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'PingFang SC']
plt.rcParams['axes.unicode_minus'] = False

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque
import random
import sys
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

# 检查CUDA是否可用
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"🖥️  使用设备: {device}")
if torch.cuda.is_available():
    print(f"GPU名称: {torch.cuda.get_device_name(0)}")
    print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
    print(f"CUDA计算能力: {torch.cuda.get_device_properties(0).major}.{torch.cuda.get_device_properties(0).minor}")
    print(f"多处理器数量: {torch.cuda.get_device_properties(0).multi_processor_count}")

from sklearn.preprocessing import StandardScaler
import joblib
import os

# 数据文件路径
TRAINING_DATA_DIR = "training_data"

class ConceptTradingEnvironmentGPU:
    """GPU优化的概念板块交易环境"""
    
    def __init__(self, data, lookback_days=20, holding_days=5, device='cuda'):
        self.device = torch.device(device)
        self.data = data.copy()
        self.lookback_days = lookback_days
        self.holding_days = holding_days
        self.current_step = 0
        
        # 获取唯一交易日期数量
        unique_dates = len(data['trade_date'].unique())
        self.max_steps = unique_dates - holding_days - lookback_days
        
        # 状态特征维度 - 增加特征维度以充分利用GPU
        self.feature_cols = [
            'pct_change', 'vol_ratio', 'turnover_rate', 
            'pe_ttm', 'pb_mrq', 'ma5', 'ma10', 'ma20',
            'rsi', 'momentum_5', 'momentum_10', 'momentum_20',
            'bb_upper', 'bb_lower', 'bb_mid', 'macd', 'macd_signal',
            'vol_ma5', 'vol_ma20', 'price_vol_corr'
        ]
        
        # 增加概念数量以充分利用GPU
        self.concept_codes = sorted(data['ts_code'].unique())[:100]  # 增加到100个概念
        self.state_dim = len(self.feature_cols) * len(self.concept_codes)  # 更大的状态空间
        self.action_dim = len(self.concept_codes) + 1  # +1 for no action
        
        # 预处理数据并转换为GPU张量
        self._preprocess_data_to_gpu()
        
        print(f"🚀 GPU环境初始化完成:")
        print(f"   状态维度: {self.state_dim}")
        print(f"   动作维度: {self.action_dim}")
        print(f"   概念数量: {len(self.concept_codes)}")
        print(f"   特征数量: {len(self.feature_cols)}")
        print(f"   GPU设备: {self.device}")
        
        # 初始化状态
        self.reset()
    
    def _preprocess_data_to_gpu(self):
        """预处理数据并转换为GPU张量以加速计算"""
        print("🔄 预处理数据并转移到GPU...")
        
        # 计算增强的技术指标
        processed_data = []
        for concept in self.concept_codes:
            concept_data = self.data[self.data['ts_code'] == concept].copy()
            if len(concept_data) > 0:
                concept_data = self._calculate_enhanced_features(concept_data)
                processed_data.append(concept_data)
        
        self.processed_data = pd.concat(processed_data, ignore_index=True)
        
        # 转换关键数据为GPU张量
        self.dates = sorted(self.processed_data['trade_date'].unique())
        
        # 为每个日期和概念创建特征张量
        self.feature_tensors = {}
        self.price_tensors = {}
        
        for date in self.dates:
            day_data = self.processed_data[self.processed_data['trade_date'] == date]
            
            # 创建特征矩阵 [concepts, features]
            features = []
            prices = []
            
            for concept in self.concept_codes:
                concept_day_data = day_data[day_data['ts_code'] == concept]
                if len(concept_day_data) > 0:
                    feature_values = concept_day_data[self.feature_cols].iloc[0].values
                    price = concept_day_data['close'].iloc[0]
                else:
                    feature_values = np.zeros(len(self.feature_cols))
                    price = 0.0
                
                features.append(feature_values)
                prices.append(price)
            
            # 转换为GPU张量
            self.feature_tensors[date] = torch.FloatTensor(features).to(self.device)
            self.price_tensors[date] = torch.FloatTensor(prices).to(self.device)
        
        print(f"✅ 数据预处理完成，已转移到GPU")
    
    def _calculate_enhanced_features(self, df):
        """计算增强的技术指标特征"""
        df = df.copy()
        df = df.sort_values('trade_date')
        
        # 基础特征
        df['vol_ratio'] = df['vol'] / df['vol'].rolling(20).mean()
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma10'] = df['close'].rolling(10).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        
        # 动量指标
        df['momentum_5'] = df['close'].pct_change(5)
        df['momentum_10'] = df['close'].pct_change(10)
        df['momentum_20'] = df['close'].pct_change(20)
        
        # RSI指标
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        rolling_mean = df['close'].rolling(20).mean()
        rolling_std = df['close'].rolling(20).std()
        df['bb_upper'] = rolling_mean + (rolling_std * 2)
        df['bb_lower'] = rolling_mean - (rolling_std * 2)
        df['bb_mid'] = rolling_mean
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # 成交量指标
        df['vol_ma5'] = df['vol'].rolling(5).mean()
        df['vol_ma20'] = df['vol'].rolling(20).mean()
        
        # 价量相关性
        df['price_vol_corr'] = df['close'].rolling(20).corr(df['vol'])
        
        # 填充缺失值
        df = df.bfill().fillna(0)
        
        return df
    
    def reset(self):
        """重置环境"""
        self.current_step = self.lookback_days
        self.current_position = None
        self.entry_price = 0
        self.total_return = 0
        
        return self._get_state()
    
    def _get_state(self):
        """获取当前状态 - 使用GPU加速"""
        if self.current_step >= len(self.dates):
            return torch.zeros(self.state_dim).to(self.device)
        
        current_date = self.dates[self.current_step]
        
        # 从GPU张量中获取特征
        if current_date in self.feature_tensors:
            features = self.feature_tensors[current_date]
            # 展平为一维张量
            state = features.flatten()
        else:
            state = torch.zeros(self.state_dim).to(self.device)
        
        return state
    
    def step(self, action):
        """执行动作 - GPU加速计算"""
        reward = 0
        done = False
        
        # 解码动作
        if action == len(self.concept_codes):  # 不投资
            selected_concept = None
        else:
            selected_concept = self.concept_codes[action]
        
        # 获取当前和未来日期
        if self.current_step >= len(self.dates) or self.current_step + self.holding_days >= len(self.dates):
            done = True
            return None, 0, done, {'selected_concept': selected_concept}
        
        current_date = self.dates[self.current_step]
        future_date = self.dates[min(self.current_step + self.holding_days, len(self.dates) - 1)]
        
        # 如果选择投资某个概念
        if selected_concept is not None:
            concept_idx = self.concept_codes.index(selected_concept)
            
            # 使用GPU张量计算收益
            if current_date in self.price_tensors and future_date in self.price_tensors:
                current_price = self.price_tensors[current_date][concept_idx]
                future_price = self.price_tensors[future_date][concept_idx]
                
                if current_price > 0:
                    reward = ((future_price - current_price) / current_price).item()
                else:
                    reward = -0.01
            else:
                reward = -0.01
        else:
            reward = 0  # 不投资的奖励为0
        
        # 移动到下一步
        self.current_step += 1
        
        # 检查是否结束
        if self.current_step >= len(self.dates) - self.holding_days:
            done = True
        
        next_state = self._get_state() if not done else None
        
        return next_state, reward, done, {'selected_concept': selected_concept}

class DQNNetworkGPU(nn.Module):
    """GPU优化的深度Q网络 - 更大更深的网络"""
    
    def __init__(self, state_dim, action_dim, hidden_dims=[2048, 1024, 512, 256, 128]):
        super(DQNNetworkGPU, self).__init__()
        
        layers = []
        prev_dim = state_dim
        
        # 构建更深的网络
        for i, hidden_dim in enumerate(hidden_dims):
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),  # 使用LayerNorm替代BatchNorm
                nn.ReLU(),
                nn.Dropout(0.3 if i < len(hidden_dims) - 1 else 0.1)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Xavier初始化权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        return self.network(x)

class DQNAgentGPU:
    """GPU优化的DQN智能体"""
    
    def __init__(self, state_dim, action_dim, lr=1e-4, gamma=0.95, epsilon=1.0, 
                 epsilon_decay=0.995, epsilon_min=0.01, batch_size=512):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.device = device
        self.batch_size = batch_size  # 大幅增加批次大小
        
        # 神经网络 - 更大的网络
        self.q_network = DQNNetworkGPU(state_dim, action_dim).to(self.device)
        self.target_network = DQNNetworkGPU(state_dim, action_dim).to(self.device)
        
        # 使用AdamW优化器，更好的权重衰减
        self.optimizer = optim.AdamW(self.q_network.parameters(), lr=lr, weight_decay=1e-5)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=100, gamma=0.95)
        
        # 初始化目标网络权重
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        # 经验回放 - 增加缓冲区大小
        self.memory = deque(maxlen=100000)
        
        # 更新目标网络
        self.update_target_frequency = 50  # 更频繁的更新
        self.step_count = 0
        
        # 优先经验回放参数
        self.priority_memory = []
        self.priority_alpha = 0.6
        self.priority_beta = 0.4
        self.priority_beta_increment = 0.001
        
        print(f"🤖 GPU优化智能体初始化完成:")
        print(f"   网络参数: {sum(p.numel() for p in self.q_network.parameters()):,}")
        print(f"   批次大小: {self.batch_size}")
        print(f"   缓冲区大小: {self.memory.maxlen}")
        print(f"   设备: {self.device}")
        
        # 计算网络大小
        total_params = sum(p.numel() for p in self.q_network.parameters())
        trainable_params = sum(p.numel() for p in self.q_network.parameters() if p.requires_grad)
        print(f"   总参数: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        
        # 预热GPU
        self._warmup_gpu()
    
    def _warmup_gpu(self):
        """预热GPU以获得稳定的性能"""
        print("🔥 GPU预热中...")
        dummy_state = torch.randn(self.batch_size, self.state_dim).to(self.device)
        
        # 执行几次前向传播预热
        for _ in range(10):
            with torch.no_grad():
                _ = self.q_network(dummy_state)
        
        torch.cuda.synchronize()
        print("✅ GPU预热完成")
    
    def remember(self, state, action, reward, next_state, done):
        """存储经验"""
        # 确保状态是GPU张量
        if isinstance(state, torch.Tensor):
            state = state.cpu().numpy()
        if next_state is not None and isinstance(next_state, torch.Tensor):
            next_state = next_state.cpu().numpy()
            
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state):
        """选择动作"""
        if np.random.random() <= self.epsilon:
            return random.randrange(self.action_dim)
        
        # 确保状态是GPU张量
        if isinstance(state, np.ndarray):
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        else:
            state_tensor = state.unsqueeze(0) if state.dim() == 1 else state
            
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
        return q_values.argmax().item()
    
    def replay(self):
        """经验回放训练 - GPU优化版本"""
        if len(self.memory) < self.batch_size:
            return
        
        # 采样批次
        batch = random.sample(self.memory, self.batch_size)
        
        # 并行预处理批次数据
        states, actions, rewards, next_states, dones = zip(*batch)
        
        # 批量转换为GPU张量
        states = torch.FloatTensor(np.array(states)).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)
        
        # 处理next_states
        valid_next_states = [ns for ns in next_states if ns is not None]
        next_state_indices = [i for i, ns in enumerate(next_states) if ns is not None]
        
        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # 计算目标Q值
        next_q_values = torch.zeros(self.batch_size, device=self.device)
        if len(valid_next_states) > 0:
            valid_next_states_tensor = torch.FloatTensor(np.array(valid_next_states)).to(self.device)
            with torch.no_grad():
                next_q_max = self.target_network(valid_next_states_tensor).max(1)[0]
            
            for idx, orig_idx in enumerate(next_state_indices):
                if not dones[orig_idx]:
                    next_q_values[orig_idx] = next_q_max[idx]
        
        target_q_values = rewards + (self.gamma * next_q_values)
        
        # 计算损失 - 使用Huber损失以提高稳定性
        loss = F.smooth_l1_loss(current_q_values.squeeze(), target_q_values.detach())
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), max_norm=1.0)
        
        self.optimizer.step()
        self.scheduler.step()
        
        # 更新探索率
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        # 更新优先经验回放的beta
        self.priority_beta = min(1.0, self.priority_beta + self.priority_beta_increment)
        
        # 定期更新目标网络
        self.step_count += 1
        if self.step_count % self.update_target_frequency == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())
        
        return loss.item()

class ConceptRotationRLGPU:
    """GPU优化的概念板块轮动强化学习策略"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.agent = None
        self.train_data = None
        self.val_data = None
        self.test_data = None
        
        # 检查训练数据目录
        if not os.path.exists(TRAINING_DATA_DIR):
            raise FileNotFoundError(f"训练数据目录 '{TRAINING_DATA_DIR}' 不存在！")
        
        # 创建保存模型的目录
        self.model_dir = f"models_gpu_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.model_dir, exist_ok=True)
        
        # GPU优化设置
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.enabled = True
            # 设置GPU内存管理
            torch.cuda.empty_cache()
            print("🚀 GPU优化设置完成")
            
            # 显示GPU内存状态
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"💾 GPU总内存: {gpu_memory:.2f} GB")
    
    def load_and_prepare_data(self):
        """并行加载和预处理数据"""
        print("🔄 并行加载和预处理数据...")
        
        # 加载数据文件
        daily_file = os.path.join(TRAINING_DATA_DIR, "ths_daily.csv")
        index_file = os.path.join(TRAINING_DATA_DIR, "ths_index.csv")
        
        print(f"📂 加载数据文件...")
        data = pd.read_csv(daily_file)
        data['trade_date'] = pd.to_datetime(data['trade_date'])
        
        print(f"✅ 数据加载完成: {len(data)} 条记录")
        
        # 并行计算技术指标
        unique_concepts = data['ts_code'].unique()
        
        def process_concept(concept):
            concept_data = data[data['ts_code'] == concept].copy()
            return self._calculate_enhanced_features(concept_data)
        
        print(f"⚙️  并行计算 {len(unique_concepts)} 个概念的技术指标...")
        
        # 使用多进程并行处理
        num_workers = min(mp.cpu_count(), 8)  # 限制进程数以避免内存问题
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            processed_data = list(executor.map(process_concept, unique_concepts))
        
        self.data = pd.concat(processed_data, ignore_index=True)
        print(f"✅ 技术指标计算完成")
        
        # 数据集划分
        self._split_data()
        
        return self.data
    
    def _calculate_enhanced_features(self, df):
        """计算增强的技术指标特征"""
        df = df.copy()
        df = df.sort_values('trade_date')
        
        # 基础特征
        df['vol_ratio'] = df['vol'] / df['vol'].rolling(20).mean()
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma10'] = df['close'].rolling(10).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        
        # 动量指标
        df['momentum_5'] = df['close'].pct_change(5)
        df['momentum_10'] = df['close'].pct_change(10)
        df['momentum_20'] = df['close'].pct_change(20)
        
        # RSI指标
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        rolling_mean = df['close'].rolling(20).mean()
        rolling_std = df['close'].rolling(20).std()
        df['bb_upper'] = rolling_mean + (rolling_std * 2)
        df['bb_lower'] = rolling_mean - (rolling_std * 2)
        df['bb_mid'] = rolling_mean
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # 成交量指标
        df['vol_ma5'] = df['vol'].rolling(5).mean()
        df['vol_ma20'] = df['vol'].rolling(20).mean()
        
        # 价量相关性
        df['price_vol_corr'] = df['close'].rolling(20).corr(df['vol'])
        
        # 填充缺失值
        df = df.bfill().fillna(0)
        
        return df
    
    def _split_data(self):
        """划分训练集、验证集和测试集"""
        dates = sorted(self.data['trade_date'].unique())
        total_days = len(dates)
        
        train_end_idx = int(total_days * 0.6)
        val_end_idx = int(total_days * 0.8)
        
        train_end_date = dates[train_end_idx - 1]
        val_end_date = dates[val_end_idx - 1]
        
        self.train_data = self.data[self.data['trade_date'] <= train_end_date].copy()
        self.val_data = self.data[
            (self.data['trade_date'] > train_end_date) & 
            (self.data['trade_date'] <= val_end_date)
        ].copy()
        self.test_data = self.data[self.data['trade_date'] > val_end_date].copy()
        
        print(f"📊 数据集划分:")
        print(f"   训练集: {len(self.train_data)} 条记录")
        print(f"   验证集: {len(self.val_data)} 条记录")
        print(f"   测试集: {len(self.test_data)} 条记录")
    
    def train_model(self, episodes=1000):
        """GPU优化的模型训练"""
        print("🚀 开始GPU优化训练...")
        
        # 创建GPU优化的训练环境
        train_env = ConceptTradingEnvironmentGPU(self.train_data, device=device)
        
        # 初始化GPU优化的智能体
        self.agent = DQNAgentGPU(
            state_dim=train_env.state_dim,
            action_dim=train_env.action_dim,
            batch_size=512  # 大批次大小充分利用GPU
        )
        
        print(f"🎯 训练配置:")
        print(f"   训练轮次: {episodes}")
        print(f"   状态维度: {train_env.state_dim}")
        print(f"   动作维度: {train_env.action_dim}")
        print(f"   批次大小: {self.agent.batch_size}")
        print(f"   网络参数: {sum(p.numel() for p in self.agent.q_network.parameters()):,}")
        
        # 训练记录
        episode_rewards = []
        episode_losses = []
        best_reward = float('-inf')
        
        # 开始训练
        import time
        training_start_time = time.time()
        
        for episode in range(episodes):
            episode_start_time = time.time()
            
            state = train_env.reset()
            total_reward = 0
            episode_loss = []
            step_count = 0
            
            while True:
                action = self.agent.act(state)
                next_state, reward, done, info = train_env.step(action)
                
                self.agent.remember(state, action, reward, next_state, done)
                
                # 训练网络
                if len(self.agent.memory) >= self.agent.batch_size:
                    loss = self.agent.replay()
                    if loss is not None:
                        episode_loss.append(loss)
                
                total_reward += reward
                step_count += 1
                
                if done:
                    break
                
                state = next_state
            
            episode_rewards.append(total_reward)
            avg_loss = np.mean(episode_loss) if episode_loss else 0
            episode_losses.append(avg_loss)
            
            # 更新最佳奖励
            if total_reward > best_reward:
                best_reward = total_reward
                print(f"🏆 Episode {episode}: 新纪录! 奖励={best_reward:.4f}")
            
            # 每50次训练保存模型检查点
            if episode > 0 and episode % 50 == 0:
                checkpoint_path = os.path.join(self.model_dir, f'checkpoint_episode_{episode}.pth')
                torch.save({
                    'episode': episode,
                    'model_state_dict': self.agent.q_network.state_dict(),
                    'target_state_dict': self.agent.target_network.state_dict(),
                    'optimizer_state_dict': self.agent.optimizer.state_dict(),
                    'scheduler_state_dict': self.agent.scheduler.state_dict(),
                    'epsilon': self.agent.epsilon,
                    'step_count': self.agent.step_count,
                    'episode_rewards': episode_rewards,
                    'episode_losses': episode_losses,
                    'best_reward': best_reward
                }, checkpoint_path)
                print(f"💾 检查点已保存: Episode {episode} -> {checkpoint_path}")
            
            # 详细进度报告
            if episode % 5 == 0:  # 更频繁的报告
                episode_time = time.time() - episode_start_time
                total_time = time.time() - training_start_time
                
                recent_rewards = episode_rewards[-10:] if len(episode_rewards) >= 10 else episode_rewards
                avg_reward = np.mean(recent_rewards)
                
                print(f"Episode {episode:4d} | "
                      f"奖励: {total_reward:7.4f} | "
                      f"10轮均值: {avg_reward:7.4f} | "
                      f"损失: {avg_loss:8.6f} | "
                      f"ε: {self.agent.epsilon:5.3f} | "
                      f"时间: {episode_time:5.2f}s | "
                      f"总计: {total_time/60:5.1f}m")
                
                # GPU内存监控
                if torch.cuda.is_available() and episode % 20 == 0:
                    gpu_memory_used = torch.cuda.memory_allocated() / 1024**3
                    gpu_memory_cached = torch.cuda.memory_reserved() / 1024**3
                    print(f"        GPU内存: 使用={gpu_memory_used:.2f}GB, 缓存={gpu_memory_cached:.2f}GB")
        
        training_time = time.time() - training_start_time
        print(f"\n🎯 训练完成!")
        print(f"   总时间: {training_time/60:.2f}分钟")
        print(f"   最佳奖励: {best_reward:.4f}")
        print(f"   平均每轮: {training_time/episodes:.2f}秒")
        
        # 保存模型
        model_path = os.path.join(self.model_dir, 'dqn_gpu_model.pth')
        torch.save({
            'model_state_dict': self.agent.q_network.state_dict(),
            'target_state_dict': self.agent.target_network.state_dict(),
            'optimizer_state_dict': self.agent.optimizer.state_dict(),
            'scheduler_state_dict': self.agent.scheduler.state_dict(),
            'epsilon': self.agent.epsilon,
            'step_count': self.agent.step_count,
            'episode_rewards': episode_rewards,
            'episode_losses': episode_losses,
            'training_time': training_time
        }, model_path)
        print(f"💾 模型已保存: {model_path}")
        
        return episode_rewards, episode_losses
    
    def load_checkpoint(self, checkpoint_path):
        """加载检查点继续训练"""
        try:
            checkpoint = torch.load(checkpoint_path, map_location=device)
            
            # 重新创建智能体（如果还没有）
            if self.agent is None:
                # 需要先创建环境来获取状态和动作维度
                temp_env = ConceptTradingEnvironmentGPU(self.train_data, device=device)
                self.agent = DQNAgentGPU(
                    state_dim=temp_env.state_dim,
                    action_dim=temp_env.action_dim,
                    batch_size=512
                )
            
            # 加载模型状态
            self.agent.q_network.load_state_dict(checkpoint['model_state_dict'])
            self.agent.target_network.load_state_dict(checkpoint['target_state_dict'])
            self.agent.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.agent.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            self.agent.epsilon = checkpoint['epsilon']
            self.agent.step_count = checkpoint['step_count']
            
            episode = checkpoint['episode']
            best_reward = checkpoint['best_reward']
            episode_rewards = checkpoint['episode_rewards']
            episode_losses = checkpoint['episode_losses']
            
            print(f"✅ 检查点加载成功:")
            print(f"   Episode: {episode}")
            print(f"   最佳奖励: {best_reward:.4f}")
            print(f"   当前探索率: {self.agent.epsilon:.4f}")
            print(f"   训练步数: {self.agent.step_count}")
            
            return episode, best_reward, episode_rewards, episode_losses
            
        except Exception as e:
            print(f"❌ 检查点加载失败: {str(e)}")
            raise e
    
    def continue_training(self, checkpoint_path, additional_episodes=100):
        """从检查点继续训练"""
        print(f"🔄 从检查点继续训练: {checkpoint_path}")
        
        # 加载检查点
        start_episode, best_reward, episode_rewards, episode_losses = self.load_checkpoint(checkpoint_path)
        
        # 创建训练环境
        train_env = ConceptTradingEnvironmentGPU(self.train_data, device=device)
        
        print(f"🎯 继续训练配置:")
        print(f"   起始Episode: {start_episode}")
        print(f"   额外训练轮次: {additional_episodes}")
        print(f"   总目标轮次: {start_episode + additional_episodes}")
        
        # 继续训练
        import time
        training_start_time = time.time()
        
        for episode in range(start_episode + 1, start_episode + additional_episodes + 1):
            episode_start_time = time.time()
            
            state = train_env.reset()
            total_reward = 0
            episode_loss = []
            step_count = 0
            
            while True:
                action = self.agent.act(state)
                next_state, reward, done, info = train_env.step(action)
                
                self.agent.remember(state, action, reward, next_state, done)
                
                # 训练网络
                if len(self.agent.memory) >= self.agent.batch_size:
                    loss = self.agent.replay()
                    if loss is not None:
                        episode_loss.append(loss)
                
                total_reward += reward
                step_count += 1
                
                if done:
                    break
                
                state = next_state
            
            episode_rewards.append(total_reward)
            avg_loss = np.mean(episode_loss) if episode_loss else 0
            episode_losses.append(avg_loss)
            
            # 更新最佳奖励
            if total_reward > best_reward:
                best_reward = total_reward
                print(f"🏆 Episode {episode}: 新纪录! 奖励={best_reward:.4f}")
            
            # 每50次训练保存模型检查点
            if episode % 50 == 0:
                checkpoint_path_new = os.path.join(self.model_dir, f'checkpoint_episode_{episode}.pth')
                torch.save({
                    'episode': episode,
                    'model_state_dict': self.agent.q_network.state_dict(),
                    'target_state_dict': self.agent.target_network.state_dict(),
                    'optimizer_state_dict': self.agent.optimizer.state_dict(),
                    'scheduler_state_dict': self.agent.scheduler.state_dict(),
                    'epsilon': self.agent.epsilon,
                    'step_count': self.agent.step_count,
                    'episode_rewards': episode_rewards,
                    'episode_losses': episode_losses,
                    'best_reward': best_reward
                }, checkpoint_path_new)
                print(f"💾 检查点已保存: Episode {episode} -> {checkpoint_path_new}")
            
            # 详细进度报告
            if episode % 5 == 0:
                episode_time = time.time() - episode_start_time
                total_time = time.time() - training_start_time
                
                recent_rewards = episode_rewards[-10:] if len(episode_rewards) >= 10 else episode_rewards
                avg_reward = np.mean(recent_rewards)
                
                print(f"Episode {episode:4d} | "
                      f"奖励: {total_reward:7.4f} | "
                      f"10轮均值: {avg_reward:7.4f} | "
                      f"损失: {avg_loss:8.6f} | "
                      f"ε: {self.agent.epsilon:5.3f} | "
                      f"时间: {episode_time:5.2f}s | "
                      f"总计: {total_time/60:5.1f}m")
        
        # 保存最终模型
        final_model_path = os.path.join(self.model_dir, f'dqn_gpu_model_final_episode_{episode}.pth')
        torch.save({
            'model_state_dict': self.agent.q_network.state_dict(),
            'target_state_dict': self.agent.target_network.state_dict(),
            'optimizer_state_dict': self.agent.optimizer.state_dict(),
            'scheduler_state_dict': self.agent.scheduler.state_dict(),
            'epsilon': self.agent.epsilon,
            'step_count': self.agent.step_count,
            'episode_rewards': episode_rewards,
            'episode_losses': episode_losses,
            'training_time': training_start_time
        }, final_model_path)
        print(f"💾 最终模型已保存: {final_model_path}")
        
        return episode_rewards, episode_losses
    
    def run_complete_pipeline(self, episodes=200):
        """运行完整的GPU优化训练流程"""
        print("🚀 " + "=" * 70)
        print("    GPU优化概念板块轮动强化学习策略")
        print("=" * 73)
        
        start_time = time.time()
        
        try:
            # 1. 数据加载
            print("\n📂 阶段1: 数据加载和预处理")
            self.load_and_prepare_data()
            
            # 2. 模型训练
            print(f"\n🧠 阶段2: GPU加速模型训练")
            training_rewards, training_losses = self.train_model(episodes=episodes)
            
            total_time = time.time() - start_time
            
            print(f"\n🎉 完整流程完成!")
            print(f"   总耗时: {total_time/60:.2f}分钟")
            print(f"   模型保存目录: {self.model_dir}")
            
            return {
                'training_rewards': training_rewards,
                'training_losses': training_losses,
                'model_dir': self.model_dir,
                'total_time': total_time
            }
            
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
            raise e

def main():
    """主函数"""
    print("🚀 启动GPU优化的概念板块轮动强化学习策略")
    print("-" * 60)
    
    # 显示系统信息
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"设备: {device}")
    
    if torch.cuda.is_available():
        props = torch.cuda.get_device_properties(0)
        print(f"GPU: {props.name}")
        print(f"计算能力: {props.major}.{props.minor}")
        print(f"多处理器: {props.multi_processor_count}")
        print(f"GPU内存: {props.total_memory / 1024**3:.2f} GB")
        try:
            print(f"最大线程/块: {props.max_threads_per_block}")
            print(f"最大块维度: {props.max_block_dimensions}")
        except AttributeError:
            print(f"CUDA计算能力详情: SM_{props.major}.{props.minor}")
    else:
        print("⚠️  CUDA不可用")
    
    print("-" * 60)
    
    # 创建GPU优化的强化学习策略
    rl_strategy = ConceptRotationRLGPU()
    
    # 运行训练 - 调整为200次训练轮次
    results = rl_strategy.run_complete_pipeline(episodes=200)
    
    return results

if __name__ == "__main__":
    results = main()