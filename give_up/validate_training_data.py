"""
训练数据验证脚本
检查导出的训练数据的质量和完整性
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta

def validate_training_data():
    """验证训练数据的质量和完整性"""
    
    print("🔍 开始验证训练数据...")
    print("=" * 60)
    
    data_dir = "training_data"
    
    # 检查目录和文件存在性
    if not os.path.exists(data_dir):
        print(f"❌ 训练数据目录 '{data_dir}' 不存在")
        return False
    
    required_files = {
        "ths_daily.csv": "概念板块日线数据",
        "ths_index.csv": "概念板块基础信息", 
        "trading_calendar.csv": "交易日历",
        "dataset_metadata.json": "数据集元信息"
    }
    
    print("📁 文件存在性检查:")
    for file, desc in required_files.items():
        file_path = os.path.join(data_dir, file)
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / 1024 / 1024
            print(f"  ✅ {file} ({desc}): {size_mb:.2f} MB")
        else:
            print(f"  ❌ {file} ({desc}): 文件不存在")
            return False
    
    try:
        # 加载数据
        print(f"\n📊 数据加载和基础检查:")
        daily_data = pd.read_csv(os.path.join(data_dir, "ths_daily.csv"))
        index_data = pd.read_csv(os.path.join(data_dir, "ths_index.csv"))
        calendar_data = pd.read_csv(os.path.join(data_dir, "trading_calendar.csv"))
        
        print(f"  日线数据: {len(daily_data):,} 条记录")
        print(f"  概念信息: {len(index_data):,} 个概念板块")
        print(f"  交易日历: {len(calendar_data):,} 个交易日")
        
        # 1. 数据类型和格式检查
        print(f"\n🔧 数据类型和格式检查:")
        
        # 转换日期格式
        daily_data['trade_date'] = pd.to_datetime(daily_data['trade_date'])
        calendar_data['cal_date'] = pd.to_datetime(calendar_data['cal_date'].astype(str))
        
        # 检查日期范围
        date_range = {
            'start': daily_data['trade_date'].min(),
            'end': daily_data['trade_date'].max(),
            'days': (daily_data['trade_date'].max() - daily_data['trade_date'].min()).days
        }
        print(f"  ✅ 数据时间范围: {date_range['start'].date()} 到 {date_range['end'].date()} ({date_range['days']} 天)")
        
        # 2. 数据完整性检查
        print(f"\n📈 数据完整性检查:")
        
        # 概念板块覆盖度
        unique_concepts = daily_data['ts_code'].nunique()
        index_concepts = index_data['ts_code'].nunique()
        print(f"  概念板块数量 - 日线数据: {unique_concepts}, 基础信息: {index_concepts}")
        
        if unique_concepts != index_concepts:
            missing_in_daily = set(index_data['ts_code']) - set(daily_data['ts_code'])
            missing_in_index = set(daily_data['ts_code']) - set(index_data['ts_code'])
            if missing_in_daily:
                print(f"  ⚠️  基础信息中有但日线数据中没有的概念: {len(missing_in_daily)} 个")
            if missing_in_index:
                print(f"  ⚠️  日线数据中有但基础信息中没有的概念: {len(missing_in_index)} 个")
        else:
            print(f"  ✅ 概念板块数据一致性良好")
        
        # 数据记录统计
        concept_stats = daily_data.groupby('ts_code').agg({
            'trade_date': ['count', 'min', 'max']
        })
        concept_stats.columns = ['记录数', '开始日期', '结束日期']
        
        print(f"  每个概念的数据记录数:")
        print(f"    平均: {concept_stats['记录数'].mean():.1f}")
        print(f"    最少: {concept_stats['记录数'].min()}")
        print(f"    最多: {concept_stats['记录数'].max()}")
        print(f"    标准差: {concept_stats['记录数'].std():.1f}")
        
        # 3. 数据质量检查
        print(f"\n🔍 数据质量检查:")
        
        # 缺失值检查
        missing_summary = daily_data.isnull().sum()
        critical_columns = ['open', 'high', 'low', 'close', 'vol', 'pct_change']
        
        print(f"  缺失值检查:")
        total_missing = missing_summary.sum()
        print(f"    总缺失值: {total_missing}")
        
        if total_missing > 0:
            print(f"    详细缺失情况:")
            for col, missing_count in missing_summary[missing_summary > 0].items():
                missing_pct = missing_count / len(daily_data) * 100
                print(f"      {col}: {missing_count} ({missing_pct:.2f}%)")
                
                # 关键字段缺失值过多的警告
                if col in critical_columns and missing_pct > 5:
                    print(f"      ⚠️  关键字段 {col} 缺失比例过高!")
        else:
            print(f"    ✅ 无缺失值")
        
        # 异常值检查
        print(f"  异常值检查:")
        
        # 价格数据合理性
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in daily_data.columns:
                negative_count = (daily_data[col] <= 0).sum()
                if negative_count > 0:
                    print(f"    ⚠️  {col}: {negative_count} 个非正值")
                else:
                    print(f"    ✅ {col}: 价格数据正常")
        
        # 涨跌幅合理性 (一般不应超过±20%)
        if 'pct_change' in daily_data.columns:
            extreme_change = daily_data[(daily_data['pct_change'].abs() > 20) & (daily_data['pct_change'].notna())]
            if len(extreme_change) > 0:
                extreme_pct = len(extreme_change) / len(daily_data) * 100
                print(f"    ⚠️  极端涨跌幅(>±20%): {len(extreme_change)} 条记录 ({extreme_pct:.2f}%)")
                
                # 显示最极端的几个案例
                top_extreme = extreme_change.nlargest(5, 'pct_change')[['ts_code', 'trade_date', 'pct_change', 'concept_name']]
                print(f"    最大涨幅案例:")
                for _, row in top_extreme.iterrows():
                    print(f"      {row['concept_name']} ({row['ts_code']}) {row['trade_date'].date()}: {row['pct_change']:.2f}%")
            else:
                print(f"    ✅ 涨跌幅数据合理")
        
        # 4. 数据连续性检查
        print(f"\n📅 数据连续性检查:")
        
        # 与交易日历对比
        trading_dates = set(calendar_data['cal_date'])
        data_dates = set(daily_data['trade_date'])
        
        missing_trading_dates = trading_dates - data_dates
        extra_dates = data_dates - trading_dates
        
        if missing_trading_dates:
            print(f"    ⚠️  缺少的交易日: {len(missing_trading_dates)} 个")
            # 显示最近的几个缺失日期
            recent_missing = sorted(missing_trading_dates, reverse=True)[:5]
            for date in recent_missing:
                print(f"      {date.date()}")
        
        if extra_dates:
            print(f"    ⚠️  多余的日期(非交易日): {len(extra_dates)} 个")
        
        if not missing_trading_dates and not extra_dates:
            print(f"    ✅ 交易日期与交易日历完全一致")
        
        # 5. 概念板块数据一致性
        print(f"\n🏷️  概念板块一致性检查:")
        
        # 检查概念名称一致性
        daily_concepts = daily_data[['ts_code', 'concept_name']].drop_duplicates()
        name_conflicts = daily_concepts.groupby('ts_code').size()
        name_conflicts = name_conflicts[name_conflicts > 1]
        
        if len(name_conflicts) > 0:
            print(f"    ⚠️  概念名称不一致的板块: {len(name_conflicts)} 个")
            for ts_code in name_conflicts.index[:5]:  # 显示前5个
                names = daily_concepts[daily_concepts['ts_code'] == ts_code]['concept_name'].unique()
                print(f"      {ts_code}: {list(names)}")
        else:
            print(f"    ✅ 概念名称一致性良好")
        
        # 6. 生成数据质量报告
        print(f"\n📋 数据质量评分:")
        
        score = 100
        issues = []
        
        # 扣分项
        if total_missing > 0:
            missing_score = min(total_missing / len(daily_data) * 100 * 10, 20)  # 最多扣20分
            score -= missing_score
            issues.append(f"缺失值({total_missing}个)")
        
        if len(extreme_change) > 0:
            extreme_score = min(len(extreme_change) / len(daily_data) * 100 * 5, 10)  # 最多扣10分
            score -= extreme_score
            issues.append(f"极端涨跌幅({len(extreme_change)}个)")
        
        if unique_concepts != index_concepts:
            score -= 10
            issues.append("概念板块数量不一致")
        
        if len(missing_trading_dates) > 0:
            missing_date_score = min(len(missing_trading_dates) / len(calendar_data) * 100 * 5, 15)
            score -= missing_date_score
            issues.append(f"缺少交易日({len(missing_trading_dates)}个)")
        
        score = max(score, 0)  # 不低于0分
        
        print(f"  总体质量评分: {score:.1f}/100")
        
        if score >= 90:
            print(f"  ✅ 数据质量优秀，可以用于训练")
        elif score >= 80:
            print(f"  ⚠️  数据质量良好，建议修复小问题后使用")
        elif score >= 60:
            print(f"  ⚠️  数据质量一般，存在以下问题: {', '.join(issues)}")
        else:
            print(f"  ❌ 数据质量较差，不建议直接使用: {', '.join(issues)}")
        
        # 7. 保存验证报告
        validation_report = {
            "validation_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "data_quality_score": score,
            "issues": issues,
            "statistics": {
                "daily_records": len(daily_data),
                "unique_concepts": unique_concepts,
                "date_range": {
                    "start": date_range['start'].strftime('%Y-%m-%d'),
                    "end": date_range['end'].strftime('%Y-%m-%d'),
                    "days": date_range['days']
                },
                "missing_values": int(total_missing),
                "extreme_changes": len(extreme_change) if 'extreme_change' in locals() else 0
            }
        }
        
        report_file = os.path.join(data_dir, "validation_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(validation_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 验证报告已保存: {report_file}")
        print("=" * 60)
        print("✅ 数据验证完成")
        
        return score >= 60  # 60分以上认为可用
        
    except Exception as e:
        print(f"❌ 数据验证过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 训练数据验证工具")
    print("检查导出的训练数据的质量和完整性")
    print()
    
    success = validate_training_data()
    
    if success:
        print("\n🎉 验证通过！数据可以用于训练。")
    else:
        print("\n⚠️  验证未通过，请检查数据质量后再进行训练。") 