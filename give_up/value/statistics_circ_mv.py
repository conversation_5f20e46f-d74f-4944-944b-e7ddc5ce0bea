# -*- coding: utf-8 -*-
"""
A股流通市值统计脚本（区分行业、板块，并输出统计表和可视化图表）
"""
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sqlalchemy import create_engine
# 设置matplotlib支持中文显示
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
from get_data.db_config import DB_URL

# 1. 获取最新交易日
def get_latest_trade_date(engine):
    sql = "SELECT MAX(trade_date) as last_trade_date FROM daily_basic"
    df = pd.read_sql(sql, engine)
    return df['last_trade_date'].iloc[0]

# 2. 获取最新交易日的流通市值、行业、板块数据
def get_circ_mv_with_info(engine, trade_date):
    sql = f'''
    SELECT a.ts_code, a.circ_mv, b.industry, b.market, b.name
    FROM daily_basic a
    LEFT JOIN stock_basic b ON a.ts_code = b.ts_code
    WHERE a.trade_date = '{trade_date}'
    '''
    df = pd.read_sql(sql, engine)
    return df

# 3. 统计整体和分组市值分布
def get_statistics(df, group_col=None):
    if group_col:
        grouped = df.groupby(group_col)['circ_mv']
        stats = grouped.agg(['count', 'mean', 'median', 'min', 'max', 'std', 'sum'])
        stats['q25'] = grouped.quantile(0.25)
        stats['q75'] = grouped.quantile(0.75)
        stats = stats.sort_values('sum', ascending=False)
    else:
        stats = df['circ_mv'].agg(['count', 'mean', 'median', 'min', 'max', 'std', 'sum'])
        stats['q25'] = df['circ_mv'].quantile(0.25)
        stats['q75'] = df['circ_mv'].quantile(0.75)
    return stats

# 4. 导出统计表为CSV
def export_statistics(df, filename):
    df.to_csv(filename, encoding='utf-8-sig')

# 5. 可视化市值分布
def plot_distribution(df, trade_date):
    plt.figure(figsize=(10,6))
    sns.histplot(df['circ_mv']/1e4, bins=100, kde=True)
    plt.xlabel('流通市值（亿元）')
    plt.title(f'A股流通市值分布（{trade_date}）')
    plt.tight_layout()
    plt.savefig(f'circ_mv_distribution_{trade_date}.png', dpi=200)
    plt.close()

# 6. 可视化行业/板块市值箱线图
def plot_group_box(df, group_col, trade_date):
    plt.figure(figsize=(14,7))
    data = df.copy()
    data = data[data['circ_mv'] > 0]
    # 只显示市值前20的行业/板块
    top_groups = data.groupby(group_col)['circ_mv'].sum().sort_values(ascending=False).head(20).index
    data = data[data[group_col].isin(top_groups)]
    sns.boxplot(x=group_col, y='circ_mv', data=data, showfliers=False)
    plt.xticks(rotation=45, ha='right')
    plt.ylabel('流通市值（万元）')
    plt.title(f'A股{group_col}流通市值箱线图（{trade_date}）')
    plt.tight_layout()
    plt.savefig(f'circ_mv_{group_col}_box_{trade_date}.png', dpi=200)
    plt.close()

# 7. 可视化行业/板块市值曲线分布（KDE）
def plot_kde_by_group(df, group_col, trade_date):
    plt.figure(figsize=(14, 7))
    # 只显示市值前8的行业/板块
    top_groups = df.groupby(group_col)['circ_mv'].sum().sort_values(ascending=False).head(8).index
    for group in top_groups:
        data = df[df[group_col] == group]['circ_mv']
        data = data[data > 0]
        if len(data) > 10:  # 数据太少的不画
            sns.kdeplot(data, label=group)
    plt.xlabel('流通市值（万元）')
    plt.ylabel('密度')
    plt.title(f'A股{group_col}流通市值曲线分布（{trade_date}）')
    plt.legend()
    plt.tight_layout()
    plt.savefig(f'circ_mv_{group_col}_kde_{trade_date}.png', dpi=200)
    plt.close()

if __name__ == '__main__':
    # 创建数据库连接
    engine = create_engine(DB_URL)
    # 获取最新交易日
    trade_date = get_latest_trade_date(engine)
    print(f'最新交易日：{trade_date}')
    # 获取全市场数据
    df = get_circ_mv_with_info(engine, trade_date)
    print(f'股票数量：{len(df)}')

    # 整体统计
    overall_stats = get_statistics(df)
    print('整体流通市值统计：')
    print(overall_stats)
    export_statistics(overall_stats.to_frame().T, f'circ_mv_overall_{trade_date}.csv')

    # 按行业统计
    industry_stats = get_statistics(df, 'industry')
    print('按行业统计：')
    print(industry_stats.head())
    export_statistics(industry_stats, f'circ_mv_by_industry_{trade_date}.csv')

    # 按板块统计
    market_stats = get_statistics(df, 'market')
    print('按板块统计：')
    print(market_stats.head())
    export_statistics(market_stats, f'circ_mv_by_market_{trade_date}.csv')

    # 可视化整体分布
    plot_distribution(df, trade_date)
    # 可视化行业箱线图
    plot_group_box(df, 'industry', trade_date)
    # 可视化板块箱线图
    plot_group_box(df, 'market', trade_date)
    # 可视化行业市值分布曲线
    plot_kde_by_group(df, 'industry', trade_date)
    # 可视化板块市值分布曲线
    plot_kde_by_group(df, 'market', trade_date)
    print('统计表和图表已生成！')
