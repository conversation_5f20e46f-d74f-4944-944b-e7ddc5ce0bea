"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/6/5 11:29
@File: ADX.py
@Version: 1.1
@Description: 实现 ADX指标的计算。
@Update: 2025/7/1 11:29 计算逻辑优化：使用ADX_corrected.py中的计算逻辑，使用Wilder's平滑方法计算ADX指标。
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import font_manager

# 导入数据库配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from get_data.db_config import get_engine

def calculate_adx(df, period=14):
    """
    计算ADX指标
    
    Parameters:
    df: DataFrame, 包含 high, low, close 列的数据
    period: int, 计算周期，默认14
    
    Returns:
    DataFrame: 包含 +DI, -DI, ADX 的数据
    """
    # 确保数据按日期排序
    df = df.sort_values('trade_date').copy()
    
    # 计算True Range (TR)
    df['prev_close'] = df['close'].shift(1)
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['prev_close'])
    df['tr3'] = abs(df['low'] - df['prev_close'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    
    # 计算价格变动
    df['high_diff'] = df['high'] - df['high'].shift(1)
    df['low_diff'] = df['low'].shift(1) - df['low']
    
    # 计算+DM和-DM
    df['+dm'] = np.where((df['high_diff'] > df['low_diff']) & (df['high_diff'] > 0), df['high_diff'], 0)
    df['-dm'] = np.where((df['low_diff'] > df['high_diff']) & (df['low_diff'] > 0), df['low_diff'], 0)
    
    # 计算平滑的TR, +DM, -DM (使用Wilder's平滑方法)
    df['atr'] = df['tr'].ewm(alpha=1/period, adjust=False).mean()
    df['+dm_smooth'] = df['+dm'].ewm(alpha=1/period, adjust=False).mean()
    df['-dm_smooth'] = df['-dm'].ewm(alpha=1/period, adjust=False).mean()
    
    # 计算+DI和-DI
    df['+di'] = 100 * df['+dm_smooth'] / df['atr']
    df['-di'] = 100 * df['-dm_smooth'] / df['atr']
    
    # 计算DX
    df['di_diff'] = abs(df['+di'] - df['-di'])
    df['di_sum'] = df['+di'] + df['-di']
    df['dx'] = 100 * df['di_diff'] / df['di_sum']
    
    # 计算ADX (DX的平滑)
    df['adx'] = df['dx'].ewm(alpha=1/period, adjust=False).mean()
    
    # 保留需要的列
    result_columns = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol', 'amount', 
                     'pct_change', '+di', '-di', 'adx', 'atr']
    
    return df[result_columns].copy()

def get_stock_data(ts_code, start_date, end_date):
    """
    从数据库获取股票数据
    
    Parameters:
    ts_code: str, 股票代码
    start_date: str, 开始日期 (YYYY-MM-DD)
    end_date: str, 结束日期 (YYYY-MM-DD)
    
    Returns:
    DataFrame: 股票价格数据
    """
    engine = get_engine()
    
    sql = f"""
    SELECT 
        ts_code,
        trade_date,
        open,
        high,
        low,
        close,
        vol,
        amount,
        pct_change
    FROM stk_factor 
    WHERE ts_code = '{ts_code}' 
        AND trade_date >= '{start_date}' 
        AND trade_date <= '{end_date}'
    ORDER BY trade_date
    """
    
    try:
        df = pd.read_sql(sql, engine)
        if df.empty:
            print(f"警告: 未找到股票 {ts_code} 在 {start_date} 到 {end_date} 期间的数据")
            return pd.DataFrame()
        
        print(f"成功获取股票 {ts_code} 的数据，共 {len(df)} 条记录")
        return df
        
    except Exception as e:
        print(f"数据库查询错误: {e}")
        return pd.DataFrame()
    finally:
        engine.dispose()

def setup_chinese_font():
    """
    设置中文字体支持
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

def generate_chart(df, ts_code):
    """
    生成股价和ADX指标图表
    
    Parameters:
    df: DataFrame, 包含ADX指标的数据
    ts_code: str, 股票代码
    
    Returns:
    str: 图表文件路径
    """
    setup_chinese_font()
    
    # 确保输出目录存在
    output_dir = "big_a_compass/adx_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 转换日期格式
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
    fig.suptitle(f'{ts_code} - 股价走势与ADX指标分析', fontsize=16, fontweight='bold')
    
    # 主图：股价走势
    ax1.plot(df['trade_date'], df['close'], label='收盘价', color='#1f77b4', linewidth=1.5)
    ax1.set_title('股价走势', fontsize=14, pad=10)
    ax1.set_ylabel('价格 (元)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    # 添加价格统计信息
    price_min = df['close'].min()
    price_max = df['close'].max()
    price_current = df['close'].iloc[-1]
    ax1.text(0.02, 0.98, f'当前价: {price_current:.2f}元\n最高价: {price_max:.2f}元\n最低价: {price_min:.2f}元', 
             transform=ax1.transAxes, fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 副图：ADX指标
    # 绘制+DI和-DI
    ax2.plot(df['trade_date'], df['+di'], label='+DI (正向指标)', color='#2ca02c', linewidth=1.5)
    ax2.plot(df['trade_date'], df['-di'], label='-DI (负向指标)', color='#d62728', linewidth=1.5)
    ax2.plot(df['trade_date'], df['adx'], label='ADX (趋势强度)', color='#ff7f0e', linewidth=2)
    
    # 添加ADX强度区域标识
    ax2.axhline(y=50, color='red', linestyle='--', alpha=0.7, linewidth=1)
    ax2.axhline(y=25, color='orange', linestyle='--', alpha=0.7, linewidth=1)
    ax2.axhline(y=20, color='gray', linestyle='--', alpha=0.7, linewidth=1)
    
    # 添加区域填充
    ax2.fill_between(df['trade_date'], 50, 100, alpha=0.1, color='red', label='极强趋势区域')
    ax2.fill_between(df['trade_date'], 25, 50, alpha=0.1, color='orange', label='强趋势区域')
    ax2.fill_between(df['trade_date'], 20, 25, alpha=0.1, color='yellow', label='中等趋势区域')
    ax2.fill_between(df['trade_date'], 0, 20, alpha=0.1, color='lightgray', label='弱趋势区域')
    
    ax2.set_title('ADX 趋向指标', fontsize=14, pad=10)
    ax2.set_ylabel('指标值', fontsize=12)
    ax2.set_xlabel('日期', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper left', fontsize=10)
    ax2.set_ylim(0, max(100, df['adx'].max() * 1.1))
    
    # 添加ADX统计信息
    adx_current = df['adx'].iloc[-1]
    adx_avg = df['adx'].mean()
    di_plus_current = df['+di'].iloc[-1]
    di_minus_current = df['-di'].iloc[-1]
    
    # 趋势方向判断
    trend_direction = "上涨趋势" if di_plus_current > di_minus_current else "下跌趋势"
    
    # 趋势强度判断
    if adx_current >= 50:
        trend_strength = "极强趋势"
        strength_color = "red"
    elif adx_current >= 25:
        trend_strength = "强趋势"
        strength_color = "orange"
    elif adx_current >= 20:
        trend_strength = "中等趋势"
        strength_color = "gold"
    else:
        trend_strength = "弱趋势"
        strength_color = "gray"
    
    info_text = f'当前ADX: {adx_current:.2f}\n平均ADX: {adx_avg:.2f}\n+DI: {di_plus_current:.2f}\n-DI: {di_minus_current:.2f}\n趋势方向: {trend_direction}\n趋势强度: {trend_strength}'
    ax2.text(0.98, 0.98, info_text, transform=ax2.transAxes, fontsize=10, 
             verticalalignment='top', horizontalalignment='right',
             bbox=dict(boxstyle='round', facecolor=strength_color, alpha=0.3))
    
    # 设置x轴日期格式
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    chart_filename = f"ADX_Chart_{ts_code}_{timestamp}.png"
    chart_filepath = os.path.join(output_dir, chart_filename)
    
    try:
        plt.savefig(chart_filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()  # 关闭图表以释放内存
        print(f"图表已成功保存到: {chart_filepath}")
        return chart_filepath
    except Exception as e:
        print(f"保存图表时出错: {e}")
        plt.close()
        return None

def export_to_csv(df, ts_code, filename=None):
    """
    导出数据到CSV文件
    
    Parameters:
    df: DataFrame, 要导出的数据
    ts_code: str, 股票代码
    filename: str, 文件名（可选）
    """
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"ADX_{ts_code}_{timestamp}.csv"
    
    # 确保目录存在
    output_dir = "big_a_compass/adx_output"
    os.makedirs(output_dir, exist_ok=True)
    
    filepath = os.path.join(output_dir, filename)
    
    try:
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"数据已成功导出到: {filepath}")
        return filepath
    except Exception as e:
        print(f"导出CSV文件时出错: {e}")
        return None

def analyze_adx_signals(df):
    """
    分析ADX信号
    
    Parameters:
    df: DataFrame, 包含ADX指标的数据
    
    Returns:
    dict: 分析结果
    """
    if df.empty:
        return {}
    
    latest = df.iloc[-1]
    
    # ADX强度分类
    if latest['adx'] >= 50:
        trend_strength = "极强趋势"
    elif latest['adx'] >= 25:
        trend_strength = "强趋势"
    elif latest['adx'] >= 20:
        trend_strength = "中等趋势"
    else:
        trend_strength = "弱趋势或震荡"
    
    # 趋势方向
    if latest['+di'] > latest['-di']:
        trend_direction = "上涨趋势"
    else:
        trend_direction = "下跌趋势"
    
    # 计算一些统计信息
    avg_adx = df['adx'].mean()
    max_adx = df['adx'].max()
    min_adx = df['adx'].min()
    
    analysis = {
        '最新ADX值': round(latest['adx'], 2),
        '最新+DI值': round(latest['+di'], 2),
        '最新-DI值': round(latest['-di'], 2),
        '趋势强度': trend_strength,
        '趋势方向': trend_direction,
        '期间平均ADX': round(avg_adx, 2),
        '期间最高ADX': round(max_adx, 2),
        '期间最低ADX': round(min_adx, 2),
        '数据记录数': len(df)
    }
    
    return analysis

def main(ts_code, start_date, end_date, period=5, generate_plot=True):
    """
    主函数：计算ADX指标并导出结果
    
    Parameters:
    ts_code: str, 股票代码，例如 '000001.SZ'
    start_date: str, 开始日期，格式 'YYYY-MM-DD'
    end_date: str, 结束日期，格式 'YYYY-MM-DD'
    period: int, ADX计算周期，默认14
    generate_plot: bool, 是否生成图表，默认True
    
    Returns:
    tuple: (DataFrame, str, str) ADX数据、CSV文件路径和图表文件路径
    """
    print(f"开始计算股票 {ts_code} 的ADX指标")
    print(f"时间范围: {start_date} 到 {end_date}")
    print(f"计算周期: {period}")
    print("-" * 50)
    
    # 1. 获取股票数据
    df = get_stock_data(ts_code, start_date, end_date)
    if df.empty:
        print("无法获取股票数据，请检查股票代码和日期范围")
        return None, None, None
    
    # 2. 计算ADX指标
    print("正在计算ADX指标...")
    try:
        adx_df = calculate_adx(df, period)
        print("ADX指标计算完成")
    except Exception as e:
        print(f"计算ADX指标时出错: {e}")
        return None, None, None
    
    # 3. 分析ADX信号
    analysis = analyze_adx_signals(adx_df)
    print("\nADX分析结果:")
    for key, value in analysis.items():
        print(f"{key}: {value}")
    
    # 4. 导出CSV文件
    print("\n正在导出CSV文件...")
    csv_path = export_to_csv(adx_df, ts_code)
    
    # 5. 生成图表
    chart_path = None
    if generate_plot:
        print("正在生成ADX分析图表...")
        try:
            chart_path = generate_chart(adx_df, ts_code)
        except Exception as e:
            print(f"生成图表时出错: {e}")
            chart_path = None
    
    print("\nADX指标计算和导出完成!")
    return adx_df, csv_path, chart_path

if __name__ == "__main__":
    # 示例使用
    # 可以修改以下参数进行测试
    
    stock_code = "002261.SZ"  # 平安银行
    start_date = "2024-01-01"
    end_date = "2025-06-05"
    period = 5
    
    print("ADX指标计算程序")
    print("=" * 60)
    
    # 交互式输入功能
    use_interactive = input("是否使用交互式输入？(y/n，默认n): ").lower().strip()
    
    if use_interactive == 'y' or use_interactive == 'yes':
        print("\n请输入股票信息:")
        
        # 获取股票代码
        input_stock = input("请输入股票代码 (例如: 000001.SZ): ").strip()
        if input_stock:
            stock_code = input_stock
        
        # 获取开始日期
        input_start = input("请输入开始日期 (YYYY-MM-DD，例如: 2024-01-01): ").strip()
        if input_start:
            start_date = input_start
        
        # 获取结束日期
        input_end = input("请输入结束日期 (YYYY-MM-DD，例如: 2024-12-31): ").strip()
        if input_end:
            end_date = input_end
        
        # 获取计算周期
        input_period = input("请输入ADX计算周期 (默认14): ").strip()
        if input_period and input_period.isdigit():
            period = int(input_period)
    
    print(f"\n使用参数:")
    print(f"股票代码: {stock_code}")
    print(f"开始日期: {start_date}")
    print(f"结束日期: {end_date}")
    print(f"计算周期: {period}")
    print("-" * 60)
    
    # 执行计算
    result_df, csv_file, chart_file = main(stock_code, start_date, end_date, period)
    
    if result_df is not None:
        print(f"\n数据预览 (最近5条记录):")
        print(result_df.tail()[['trade_date', 'close', '+di', '-di', 'adx']].to_string(index=False))
        
        print(f"\n📁 输出文件:")
        print(f"- CSV文件: {csv_file}")
        if chart_file:
            print(f"- 图表文件: {chart_file}")
        print("可以使用Excel等工具查看CSV数据，或直接查看PNG图表")
        
        # 显示一些统计信息
        print(f"\n📊 数据统计:")
        print(f"- 数据总数: {len(result_df)} 条")
        print(f"- 时间跨度: {result_df['trade_date'].min()} 到 {result_df['trade_date'].max()}")
        print(f"- 价格范围: {result_df['close'].min():.2f} - {result_df['close'].max():.2f} 元")
        print(f"- ADX范围: {result_df['adx'].min():.2f} - {result_df['adx'].max():.2f}")
        
        # ADX信号解读
        latest_adx = result_df.iloc[-1]['adx']
        print(f"\n📈 ADX信号解读:")
        print(f"当前ADX值: {latest_adx:.2f}")
        if latest_adx >= 50:
            print("🟢 信号: 极强趋势，适合趋势跟踪策略")
        elif latest_adx >= 25:
            print("🟡 信号: 强趋势，趋势明确")
        elif latest_adx >= 20:
            print("🟠 信号: 中等趋势，需要结合其他指标")
        else:
            print("🔴 信号: 弱趋势或震荡，不适合趋势策略")
            
    else:
        print("计算失败，请检查参数和数据库连接")
        
    print("\n程序执行完成！")
