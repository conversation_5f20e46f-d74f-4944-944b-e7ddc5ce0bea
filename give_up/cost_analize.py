"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2024/12/20 17:46
@Description: 分析筹码的作用
"""

"""
###大概思路：
1、通过 sqlalchemy 连接数据库，获取数据
2、通过 pandas 处理数据，获取特定的数据列
3、通过 matplotlib 绘制图表
"""

import pandas as pd
import sqlalchemy
import matplotlib.pyplot as plt
from matplotlib import rcParams

engine = sqlalchemy.create_engine('mysql+pymysql://root:123456@localhost:3306/tushare?charset=utf8')


def get_cost_data(engine):
    cost_df = pd.read_sql("""
                            SELECT
                            ca.ts_code,
                            ca.trade_date,
                            ca.cost_5pct,
                            ca.cost_50pct,
                            ca.cost_95pct,
                            ca.winner_rate
                        FROM
                            stock_cost_analysis ca
                        WHERE
                            ts_code = '300033.SZ' 
                        ORDER BY
                            trade_date 
                        """, con=engine)
    return cost_df

#通过sqlalchemy连接数据库，获取股票价格数据
def get_price_data(engine):
    price_df = pd.read_sql("""
                            SELECT
                            f.ts_code,
                            f.trade_date,
                            f.close_hfq 
                        FROM
                            stk_factor f 
                        WHERE
                            f.ts_code = '300033.SZ'
                        """, con=engine)
    return price_df

#通过pandas处理数据，获取特定的数据列，把两个表合并成一个表
def merge_data():
    cost_df = get_cost_data(engine)
    price_df = get_price_data(engine)
    merge_df = pd.merge(left=cost_df, right=price_df, on=["ts_code", "trade_date"], how="outer")
    print(merge_df.head())
    print(merge_df.describe())
    return merge_df


# 通过matplotlib绘制图表
def draw_chart(merge_df):
    rcParams['font.sans-serif'] = ['Microsoft YaHei']
    rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 确保数据按日期排序
    merge_df = merge_df.sort_values(by="trade_date")

    # 提取数据
    trade_date = pd.to_datetime(merge_df['trade_date'])  # 转换为日期格式
    cost_5pct = merge_df['cost_5pct']
    cost_50pct = merge_df['cost_50pct']
    cost_95pct = merge_df['cost_95pct']
    winner_rate = merge_df['winner_rate']
    close_price = merge_df['close_hfq']

    # 创建图表
    fig, ax1 = plt.subplots(figsize=(12, 6))

    # 绘制成本数据（左侧 y 轴）
    ax1.plot(trade_date, cost_5pct,label='5%成本')
    ax1.plot(trade_date, cost_50pct,label='50%成本')
    ax1.plot(trade_date, cost_95pct,label='95%成本')

    ax1.set_ylabel('百分位', color='black')
    ax1.tick_params(axis='y', labelcolor='black')
    ax1.legend(loc='upper left')

    #创建右侧 y 轴，绘制股票价格
    ax2 = ax1.twinx()
    ax2.plot(trade_date, winner_rate,label='赢家率',color='red')
    ax2.set_ylabel('赢家率', color='red')
    ax2.tick_params(axis='y', labelcolor='red')
    ax2.legend(loc='upper right')


    # 设置标题和 x 轴标签
    plt.title('300033.SZ 成本与价格分析')
    plt.xlabel('交易日期')

    # 调整布局并显示图表
    fig.tight_layout()
    plt.show()


if __name__ == "__main__":
    merge_df = merge_data()
    draw_chart(merge_df)
