"""
@Author: JiangXin
@Date: 2025/01/05 23:18
@Description: 多因子模型实现
"""

import numpy as np
import pandas as pd


class MultiFactorModel:
    """多因子模型类"""
    
    def __init__(self):
        """初始化多因子模型"""
        pass
    
    def calculate_return(self, alpha_i, beta_ik, factor_k, epsilon_i=0):
        """
        计算多因子模型的收益率
        参数:
        alpha_i: float, 截距项
        beta_ik: numpy.ndarray, 因子敏感度数组，形状为 (K,)
        factor_k: numpy.ndarray, 因子收益率数组，形状为 (K,)
        epsilon_i: float, 残差项，默认为0
        
        返回:
        float: 计算得到的收益率 r_i
        """
        # 确保输入数组维度匹配
        if len(beta_ik) != len(factor_k):
            raise ValueError("因子敏感度数组和因子收益率数组的维度必须相同")
        
        # 计算因子贡献项的和
        factor_sum = np.sum(beta_ik * factor_k)
        
        # 计算总收益率
        r_i = alpha_i + factor_sum + epsilon_i
        
        return r_i
    
    def calculate_portfolio_return(self, alphas, betas, factors, epsilons=None):
        """
        计算投资组合的多因子收益率
        参数:
        alphas: numpy.ndarray, 截距项数组，形状为 (N,)，N为资产数量
        betas: numpy.ndarray, 因子敏感度矩阵，形状为 (N, K)，K为因子数量
        factors: numpy.ndarray, 因子收益率数组，形状为 (K,)
        epsilons: numpy.ndarray, 可选，残差项数组，形状为 (N,)
        
        返回:
        numpy.ndarray: 所有资产的收益率数组
        """
        if epsilons is None:
            epsilons = np.zeros(len(alphas))
            
        # 检查维度
        N = len(alphas)
        K = len(factors)
        if betas.shape != (N, K):
            raise ValueError(f"因子敏感度矩阵维度应为({N}, {K})")
        if len(epsilons) != N:
            raise ValueError(f"残差项数组维度应为{N}")
            
        # 计算所有资产的收益率
        # r_i = α_i + Σ(β_ik * f_k) + ε_i
        factor_returns = np.dot(betas, factors)  # 计算因子贡献
        returns = alphas + factor_returns + epsilons
        
        return returns


def demo():
    """演示多因子模型的使用"""
    # 创建模型实例
    model = MultiFactorModel()
    
    # 单个资产示例
    print("=== 单个资产示例 ===")
    alpha = 0.01  # 截距项
    betas = np.array([0.5, 0.3, 0.2])  # 三个因子的敏感度
    factors = np.array([0.02, -0.01, 0.03])  # 三个因子的收益率
    epsilon = 0.001  # 残差项
    
    return_i = model.calculate_return(alpha, betas, factors, epsilon)
    print(f"单个资产收益率: {return_i:.4f}")
    
    # 投资组合示例
    print("\n=== 投资组合示例 ===")
    n_assets = 3  # 资产数量
    n_factors = 3  # 因子数量
    
    # 生成示例数据
    alphas = np.array([0.01, 0.02, 0.015])  # 各资产的截距项
    betas = np.array([  # 各资产对各因子的敏感度
        [0.5, 0.3, 0.2],
        [0.4, 0.4, 0.2],
        [0.3, 0.3, 0.4]
    ])
    factors = np.array([0.02, -0.01, 0.03])  # 因子收益率
    epsilons = np.array([0.001, -0.001, 0.002])  # 残差项
    
    portfolio_returns = model.calculate_portfolio_return(alphas, betas, factors, epsilons)
    
    # 打印结果
    for i, ret in enumerate(portfolio_returns):
        print(f"资产 {i+1} 收益率: {ret:.4f}")


if __name__ == "__main__":
    demo()
