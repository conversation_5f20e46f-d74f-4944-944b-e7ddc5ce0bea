"""
@Author: Cascade
@Date: 2024/12/26
@Description: 通过 SQLAlchemy 获取股票数据并计算 SKDJ 指标
"""

from typing import Tuple, Optional
import pandas as pd
import matplotlib.pyplot as plt
from sqlalchemy import create_engine, text, Engine
from contextlib import contextmanager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

from get_data.db_config import DB_URL


class SKDJAnalyzer:
    """
    SKDJ指标分析器
    
    参数:
        n (int): RSV计算周期，默认为9
        m (int): 移动平均周期，默认为3
        engine (Optional[Engine]): SQLAlchemy引擎实例，如果为None则自动创建
        
    示例:
        >>> analyzer = SKDJAnalyzer(n=9, m=3)
        >>> df = analyzer.analyze_stock('000001.SZ', '20230101', '20231231')
    """
    
    def __init__(self, n: int = 9, m: int = 3, engine: Optional[Engine] = None):
        """
        初始化SKDJ分析器
        
        参数:
            n (int): RSV计算周期，默认为9
            m (int): 移动平均周期，默认为3
            engine (Optional[Engine]): SQLAlchemy引擎实例，如果为None则自动创建
        """
        self.n = n
        self.m = m
        self.engine = engine or create_engine(DB_URL, pool_size=5, max_overflow=10)
        
    def __del__(self):
        """析构函数，关闭数据库连接"""
        if hasattr(self, 'engine'):
            self.engine.dispose()
            logger.info("数据库连接已关闭")
            
    @contextmanager
    def db_session(self):
        """数据库会话上下文管理器"""
        conn = self.engine.connect()
        try:
            yield conn
        except Exception as e:
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            conn.close()
    
    def get_stock_data(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取后复权的股票数据
        
        参数:
            stock_code (str): 股票代码
            start_date (str): 开始日期，格式YYYYMMDD
            end_date (str): 结束日期，格式YYYYMMDD
            
        返回:
            pd.DataFrame: 包含股票数据的DataFrame
            
        异常:
            ValueError: 如果日期格式不正确或股票代码无效
            DatabaseError: 如果数据库查询失败
        """
        try:
            # 验证日期格式
            pd.to_datetime(start_date, format='%Y%m%d')
            pd.to_datetime(end_date, format='%Y%m%d')
            
            # 验证股票代码格式
            if not isinstance(stock_code, str) or len(stock_code) != 9:
                raise ValueError("股票代码格式应为6位数字+.+2位字母，例如'000001.SZ'")
                
            query = text("""
                SELECT trade_date, 
                       open_hfq as open, 
                       high_hfq as high, 
                       low_hfq as low, 
                       close_hfq as close 
                FROM stk_factor 
                WHERE ts_code = :stock_code 
                AND trade_date BETWEEN :start_date AND :end_date 
                ORDER BY trade_date
            """)
            
            with self.db_session() as conn:
                df = pd.read_sql(query, conn, params={
                    'stock_code': stock_code,
                    'start_date': start_date,
                    'end_date': end_date
                })
                
            if df.empty:
                logger.warning(f"未找到股票代码 {stock_code} 在 {start_date} 到 {end_date} 之间的数据")
                return df
                
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            logger.info(f"成功获取 {stock_code} 从 {start_date} 到 {end_date} 的数据，共 {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            raise

    def calculate_skdj(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算SKDJ指标
        
        参数:
            df (pd.DataFrame): 包含股票数据的DataFrame
            
        返回:
            pd.DataFrame: 包含SKDJ指标的DataFrame
            
        异常:
            ValueError: 如果输入数据不完整或参数无效
        """
        if df.empty:
            logger.warning("输入数据为空，跳过SKDJ计算")
            return df
            
        required_columns = {'open', 'high', 'low', 'close'}
        if not required_columns.issubset(df.columns):
            raise ValueError(f"输入数据缺少必要列，需要: {required_columns}")
            
        try:
            # 计算N日内的最低价和最高价
            low_list = df['low'].rolling(window=self.n, min_periods=1).min()
            high_list = df['high'].rolling(window=self.n, min_periods=1).max()
            
            # 计算RSV值
            rsv = (df['close'] - low_list) / (high_list - low_list) * 100
            rsv = rsv.fillna(50)  # 处理初始NaN值
            
            # 计算K值和D值
            df['K'] = rsv.ewm(alpha=1/self.m, adjust=False).mean()
            df['D'] = df['K'].ewm(alpha=1/self.m, adjust=False).mean()
            
            # 计算J值
            df['J'] = 3 * df['K'] - 2 * df['D']
            
            logger.info("SKDJ指标计算完成")
            return df
            
        except Exception as e:
            logger.error(f"计算SKDJ指标失败: {e}")
            raise

    def plot_analysis(self, df: pd.DataFrame, stock_code: str):
        """绘制分析图表"""
        # 创建子图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), height_ratios=[2, 1])
        
        # 绘制价格走势
        ax1.plot(df['trade_date'], df['close'], label='收盘价', color='black')
        ax1.set_title(f'{stock_code} 股票走势与SKDJ指标')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('价格')
        ax1.grid(True)
        ax1.legend()
        
        # 绘制SKDJ指标
        ax2.plot(df['trade_date'], df['K'], label='K值', color='blue')
        ax2.plot(df['trade_date'], df['D'], label='D值', color='orange')
        ax2.plot(df['trade_date'], df['J'], label='J值', color='green')
        
        # 添加超买超卖区域
        ax2.axhline(y=20, color='r', linestyle='--', alpha=0.3)
        ax2.axhline(y=80, color='r', linestyle='--', alpha=0.3)
        ax2.fill_between(df['trade_date'], 80, 100, color='r', alpha=0.1)
        ax2.fill_between(df['trade_date'], 0, 20, color='g', alpha=0.1)
        
        ax2.set_xlabel('日期')
        ax2.set_ylabel('SKDJ值')
        ax2.grid(True)
        ax2.legend()
        
        plt.tight_layout()
        plt.show()

    def analyze_stock(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """分析股票的SKDJ指标"""
        df = self.get_stock_data(stock_code, start_date, end_date)
        df = self.calculate_skdj(df)
        self.plot_analysis(df, stock_code)
        return df


def main():
    """主函数"""
    analyzer = SKDJAnalyzer(n=9, m=3)
    stock_code = '000001.SZ'
    start_date = '20230101'
    end_date = '20231231'
    
    df = analyzer.analyze_stock(stock_code, start_date, end_date)
    print("SKDJ指标计算完成！")
    return df


if __name__ == '__main__':
    main()
