---
description: 
globs: 
alwaysApply: true
---
# 项目规则
0、所有的回答必须都使用中文！
1、进行数据库连接时，默认通过 sqlalchemy 的 engine 来连接，这里面是数据库连接参数：get_data/db_config.py，可以引用这个文件进行连接。
2、数据库的表结构描述：get_data/database_table, 需要时，你可以参考这个文件来写你的代码。
3、默认情况下，请使用daily_basic表查询对应的实时最新交易价格，比如对方问你某只股票的最新价格，当天的指标数据等。
3、在回测场景下，一般使用stk_factor表的后复权的收盘价进行计算。
4、为了实现更好的回答效果，如果你不了解目前的项目文件及内容，你可以先快速查看下整个workspace的所有文件之后再做回答。如有你有 90% 的把握，这个也不是必须的。
5、我的电脑是m4pro芯片，24GB内存，请考虑到实际的电脑硬件配置，可以考虑使用多进程实现程序的运行最大化效率。
6、如果用图表输出，需要支持中文显示，使用：STHeiti 字体。同时注意，如果在一个图表中有多个数据维度且数值大小差异巨大，需要用左右刻度表示。
7、需要创建文件时，文件名中带上时间信息，比如20250506192836，即当前的日期+时分秒。但是如果是代码类的文件，不需要带有时间信息。比如：.py；.json;.java等文件类型。
