/*
 Navicat Premium Dump SQL

 Source Server         : quant
 Source Server Type    : MySQL
 Source Server Version : 90300 (9.3.0)
 Source Host           : localhost:3306
 Source Schema         : test

 Target Server Type    : MySQL
 Target Server Version : 90300 (9.3.0)
 File Encoding         : 65001

 Date: 12/06/2025 16:13:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for balance_sheet
-- ----------------------------
DROP TABLE IF EXISTS `balance_sheet`;
CREATE TABLE `balance_sheet` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `ann_date` date DEFAULT NULL COMMENT '公告日期',
  `f_ann_date` date DEFAULT NULL COMMENT '实际公告日期',
  `end_date` date NOT NULL COMMENT '报告期',
  `report_type` varchar(4) DEFAULT NULL COMMENT '报表类型',
  `comp_type` varchar(2) DEFAULT NULL COMMENT '公司类型',
  `end_type` varchar(10) DEFAULT NULL COMMENT '报告期类型',
  `total_share` decimal(20,6) DEFAULT NULL COMMENT '期末总股本',
  `cap_rese` decimal(20,6) DEFAULT NULL COMMENT '资本公积金',
  `undistr_porfit` decimal(20,6) DEFAULT NULL COMMENT '未分配利润',
  `surplus_rese` decimal(20,6) DEFAULT NULL COMMENT '盈余公积金',
  `special_rese` decimal(20,6) DEFAULT NULL COMMENT '专项储备',
  `money_cap` decimal(20,6) DEFAULT NULL COMMENT '货币资金',
  `trad_asset` decimal(20,6) DEFAULT NULL COMMENT '交易性金融资产',
  `notes_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收票据',
  `accounts_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收账款',
  `oth_receiv` decimal(20,6) DEFAULT NULL COMMENT '其他应收款',
  `prepayment` decimal(20,6) DEFAULT NULL COMMENT '预付款项',
  `div_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收股利',
  `int_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收利息',
  `inventories` decimal(20,6) DEFAULT NULL COMMENT '存货',
  `amor_exp` decimal(20,6) DEFAULT NULL COMMENT '待摊费用',
  `nca_within_1y` decimal(20,6) DEFAULT NULL COMMENT '一年内到期的非流动资产',
  `sett_rsrv` decimal(20,6) DEFAULT NULL COMMENT '结算备付金',
  `loanto_oth_bank_fi` decimal(20,6) DEFAULT NULL COMMENT '拆出资金',
  `premium_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收保费',
  `reinsur_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收分保账款',
  `reinsur_res_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收分保合同准备金',
  `pur_resale_fa` decimal(20,6) DEFAULT NULL COMMENT '买入返售金融资产',
  `oth_cur_assets` decimal(20,6) DEFAULT NULL COMMENT '其他流动资产',
  `total_cur_assets` decimal(20,6) DEFAULT NULL COMMENT '流动资产合计',
  `fa_avail_for_sale` decimal(20,6) DEFAULT NULL COMMENT '可供出售金融资产',
  `htm_invest` decimal(20,6) DEFAULT NULL COMMENT '持有至到期投资',
  `lt_eqt_invest` decimal(20,6) DEFAULT NULL COMMENT '长期股权投资',
  `invest_real_estate` decimal(20,6) DEFAULT NULL COMMENT '投资性房地产',
  `time_deposits` decimal(20,6) DEFAULT NULL COMMENT '定期存款',
  `oth_assets` decimal(20,6) DEFAULT NULL COMMENT '其他资产',
  `lt_rec` decimal(20,6) DEFAULT NULL COMMENT '长期应收款',
  `fix_assets` decimal(20,6) DEFAULT NULL COMMENT '固定资产',
  `cip` decimal(20,6) DEFAULT NULL COMMENT '在建工程',
  `const_materials` decimal(20,6) DEFAULT NULL COMMENT '工程物资',
  `fixed_assets_disp` decimal(20,6) DEFAULT NULL COMMENT '固定资产清理',
  `produc_bio_assets` decimal(20,6) DEFAULT NULL COMMENT '生产性生物资产',
  `oil_and_gas_assets` decimal(20,6) DEFAULT NULL COMMENT '油气资产',
  `intan_assets` decimal(20,6) DEFAULT NULL COMMENT '无形资产',
  `r_and_d` decimal(20,6) DEFAULT NULL COMMENT '研发支出',
  `goodwill` decimal(20,6) DEFAULT NULL COMMENT '商誉',
  `lt_amor_exp` decimal(20,6) DEFAULT NULL COMMENT '长期待摊费用',
  `defer_tax_assets` decimal(20,6) DEFAULT NULL COMMENT '递延所得税资产',
  `decr_in_disbur` decimal(20,6) DEFAULT NULL COMMENT '发放贷款及垫款',
  `oth_nca` decimal(20,6) DEFAULT NULL COMMENT '其他非流动资产',
  `total_nca` decimal(20,6) DEFAULT NULL COMMENT '非流动资产合计',
  `cash_reser_cb` decimal(20,6) DEFAULT NULL COMMENT '现金及存放中央银行款项',
  `depos_in_oth_bfi` decimal(20,6) DEFAULT NULL COMMENT '存放同业和其它金融机构款项',
  `prec_metals` decimal(20,6) DEFAULT NULL COMMENT '贵金属',
  `deriv_assets` decimal(20,6) DEFAULT NULL COMMENT '衍生金融资产',
  `rr_reins_une_prem` decimal(20,6) DEFAULT NULL COMMENT '应收分保未到期责任准备金',
  `rr_reins_outstd_cla` decimal(20,6) DEFAULT NULL COMMENT '应收分保未决赔款准备金',
  `rr_reins_lins_liab` decimal(20,6) DEFAULT NULL COMMENT '应收分保寿险责任准备金',
  `rr_reins_lthins_liab` decimal(20,6) DEFAULT NULL COMMENT '应收分保长期健康险责任准备金',
  `refund_depos` decimal(20,6) DEFAULT NULL COMMENT '存出保证金',
  `ph_pledge_loans` decimal(20,6) DEFAULT NULL COMMENT '保户质押贷款',
  `refund_cap_depos` decimal(20,6) DEFAULT NULL COMMENT '存出资本保证金',
  `indep_acct_assets` decimal(20,6) DEFAULT NULL COMMENT '独立账户资产',
  `client_depos` decimal(20,6) DEFAULT NULL COMMENT '客户资金存款',
  `client_prov` decimal(20,6) DEFAULT NULL COMMENT '客户备付金',
  `transac_seat_fee` decimal(20,6) DEFAULT NULL COMMENT '交易席位费',
  `invest_as_receiv` decimal(20,6) DEFAULT NULL COMMENT '应收款项类投资',
  `total_assets` decimal(20,6) DEFAULT NULL COMMENT '资产总计',
  `lt_borr` decimal(20,6) DEFAULT NULL COMMENT '长期借款',
  `st_borr` decimal(20,6) DEFAULT NULL COMMENT '短期借款',
  `cb_borr` decimal(20,6) DEFAULT NULL COMMENT '向中央银行借款',
  `depos_ib_deposits` decimal(20,6) DEFAULT NULL COMMENT '吸收存款及同业存放',
  `loan_oth_bank` decimal(20,6) DEFAULT NULL COMMENT '拆入资金',
  `trading_fl` decimal(20,6) DEFAULT NULL COMMENT '交易性金融负债',
  `notes_payable` decimal(20,6) DEFAULT NULL COMMENT '应付票据',
  `acct_payable` decimal(20,6) DEFAULT NULL COMMENT '应付账款',
  `adv_receipts` decimal(20,6) DEFAULT NULL COMMENT '预收款项',
  `sold_for_repur_fa` decimal(20,6) DEFAULT NULL COMMENT '卖出回购金融资产款',
  `comm_payable` decimal(20,6) DEFAULT NULL COMMENT '应付手续费及佣金',
  `payroll_payable` decimal(20,6) DEFAULT NULL COMMENT '应付职工薪酬',
  `taxes_payable` decimal(20,6) DEFAULT NULL COMMENT '应交税费',
  `int_payable` decimal(20,6) DEFAULT NULL COMMENT '应付利息',
  `div_payable` decimal(20,6) DEFAULT NULL COMMENT '应付股利',
  `oth_payable` decimal(20,6) DEFAULT NULL COMMENT '其他应付款',
  `acc_exp` decimal(20,6) DEFAULT NULL COMMENT '预提费用',
  `deferred_inc` decimal(20,6) DEFAULT NULL COMMENT '递延收益',
  `st_bonds_payable` decimal(20,6) DEFAULT NULL COMMENT '应付短期债券',
  `payable_to_reinsurer` decimal(20,6) DEFAULT NULL COMMENT '应付分保账款',
  `rsrv_insur_cont` decimal(20,6) DEFAULT NULL COMMENT '保险合同准备金',
  `acting_trading_sec` decimal(20,6) DEFAULT NULL COMMENT '代理买卖证券款',
  `acting_uw_sec` decimal(20,6) DEFAULT NULL COMMENT '代理承销证券款',
  `non_cur_liab_due_1y` decimal(20,6) DEFAULT NULL COMMENT '一年内到期的非流动负债',
  `oth_cur_liab` decimal(20,6) DEFAULT NULL COMMENT '其他流动负债',
  `total_cur_liab` decimal(20,6) DEFAULT NULL COMMENT '流动负债合计',
  `bond_payable` decimal(20,6) DEFAULT NULL COMMENT '应付债券',
  `lt_payable` decimal(20,6) DEFAULT NULL COMMENT '长期应付款',
  `specific_payables` decimal(20,6) DEFAULT NULL COMMENT '专项应付款',
  `estimated_liab` decimal(20,6) DEFAULT NULL COMMENT '预计负债',
  `defer_tax_liab` decimal(20,6) DEFAULT NULL COMMENT '递延所得税负债',
  `defer_inc_non_cur_liab` decimal(20,6) DEFAULT NULL COMMENT '递延收益-非流动负债',
  `oth_ncl` decimal(20,6) DEFAULT NULL COMMENT '其他非流动负债',
  `total_ncl` decimal(20,6) DEFAULT NULL COMMENT '非流动负债合计',
  `depos_oth_bfi` decimal(20,6) DEFAULT NULL COMMENT '同业和其它金融机构存放款项',
  `deriv_liab` decimal(20,6) DEFAULT NULL COMMENT '衍生金融负债',
  `depos` decimal(20,6) DEFAULT NULL COMMENT '吸收存款',
  `agency_bus_liab` decimal(20,6) DEFAULT NULL COMMENT '代理业务负债',
  `oth_liab` decimal(20,6) DEFAULT NULL COMMENT '其他负债',
  `prem_receiv_adva` decimal(20,6) DEFAULT NULL COMMENT '预收保费',
  `depos_received` decimal(20,6) DEFAULT NULL COMMENT '存入保证金',
  `ph_invest` decimal(20,6) DEFAULT NULL COMMENT '保户储金及投资款',
  `reser_une_prem` decimal(20,6) DEFAULT NULL COMMENT '未到期责任准备金',
  `reser_outstd_claims` decimal(20,6) DEFAULT NULL COMMENT '未决赔款准备金',
  `reser_lins_liab` decimal(20,6) DEFAULT NULL COMMENT '寿险责任准备金',
  `reser_lthins_liab` decimal(20,6) DEFAULT NULL COMMENT '长期健康险责任准备金',
  `indept_acc_liab` decimal(20,6) DEFAULT NULL COMMENT '独立账户负债',
  `pledge_borr` decimal(20,6) DEFAULT NULL COMMENT '质押借款',
  `indem_payable` decimal(20,6) DEFAULT NULL COMMENT '应付赔付款',
  `policy_div_payable` decimal(20,6) DEFAULT NULL COMMENT '应付保单红利',
  `total_liab` decimal(20,6) DEFAULT NULL COMMENT '负债合计',
  `treasury_share` decimal(20,6) DEFAULT NULL COMMENT '减:库存股',
  `ordin_risk_reser` decimal(20,6) DEFAULT NULL COMMENT '一般风险准备',
  `forex_differ` decimal(20,6) DEFAULT NULL COMMENT '外币报表折算差额',
  `invest_loss_unconf` decimal(20,6) DEFAULT NULL COMMENT '未确认的投资损失',
  `minority_int` decimal(20,6) DEFAULT NULL COMMENT '少数股东权益',
  `total_hldr_eqy_exc_min_int` decimal(20,6) DEFAULT NULL COMMENT '股东权益合计(不含少数股东权益)',
  `total_hldr_eqy_inc_min_int` decimal(20,6) DEFAULT NULL COMMENT '股东权益合计(含少数股东权益)',
  `total_liab_hldr_eqy` decimal(20,6) DEFAULT NULL COMMENT '负债及股东权益总计',
  `lt_payroll_payable` decimal(20,6) DEFAULT NULL COMMENT '长期应付职工薪酬',
  `oth_comp_income` decimal(20,6) DEFAULT NULL COMMENT '其他综合收益',
  `oth_eqt_tools` decimal(20,6) DEFAULT NULL COMMENT '其他权益工具',
  `oth_eqt_tools_p_shr` decimal(20,6) DEFAULT NULL COMMENT '其他权益工具(优先股)',
  `lending_funds` decimal(20,6) DEFAULT NULL COMMENT '融出资金',
  `acc_receivable` decimal(20,6) DEFAULT NULL COMMENT '应收款项',
  `st_fin_payable` decimal(20,6) DEFAULT NULL COMMENT '应付短期融资款',
  `payables` decimal(20,6) DEFAULT NULL COMMENT '应付款项',
  `hfs_assets` decimal(20,6) DEFAULT NULL COMMENT '持有待售的资产',
  `hfs_sales` decimal(20,6) DEFAULT NULL COMMENT '持有待售的负债',
  `cost_fin_assets` decimal(20,6) DEFAULT NULL COMMENT '以摊余成本计量的金融资产',
  `fair_value_fin_assets` decimal(20,6) DEFAULT NULL COMMENT '以公允价值计量且其变动计入其他综合收益的金融资产',
  `cip_total` decimal(20,6) DEFAULT NULL COMMENT '在建工程(合计)(元)',
  `oth_pay_total` decimal(20,6) DEFAULT NULL COMMENT '其他应付款(合计)(元)',
  `long_pay_total` decimal(20,6) DEFAULT NULL COMMENT '长期应付款(合计)(元)',
  `debt_invest` decimal(20,6) DEFAULT NULL COMMENT '债权投资(元)',
  `oth_debt_invest` decimal(20,6) DEFAULT NULL COMMENT '其他债权投资(元)',
  `oth_eq_invest` decimal(20,6) DEFAULT NULL COMMENT '其他权益工具投资(元)',
  `oth_illiq_fin_assets` decimal(20,6) DEFAULT NULL COMMENT '其他非流动金融资产(元)',
  `oth_eq_ppbond` decimal(20,6) DEFAULT NULL COMMENT '其他权益工具:永续债(元)',
  `receiv_financing` decimal(20,6) DEFAULT NULL COMMENT '应收款项融资',
  `use_right_assets` decimal(20,6) DEFAULT NULL COMMENT '使用权资产',
  `lease_liab` decimal(20,6) DEFAULT NULL COMMENT '租赁负债',
  `contract_assets` decimal(20,6) DEFAULT NULL COMMENT '合同资产',
  `contract_liab` decimal(20,6) DEFAULT NULL COMMENT '合同负债',
  `accounts_receiv_bill` decimal(20,6) DEFAULT NULL COMMENT '应收票据及应收账款',
  `accounts_pay` decimal(20,6) DEFAULT NULL COMMENT '应付票据及应付账款',
  `oth_rcv_total` decimal(20,6) DEFAULT NULL COMMENT '其他应收款(合计)（元）',
  `fix_assets_total` decimal(20,6) DEFAULT NULL COMMENT '固定资产(合计)(元)',
  `update_flag` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新标识',
  PRIMARY KEY (`ts_code`,`end_date`,`update_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公司资产负债表';

-- ----------------------------
-- Table structure for cashflow
-- ----------------------------
DROP TABLE IF EXISTS `cashflow`;
CREATE TABLE `cashflow` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `ann_date` date DEFAULT NULL COMMENT '公告日期',
  `f_ann_date` date DEFAULT NULL COMMENT '实际公告日期',
  `end_date` date NOT NULL COMMENT '报告期',
  `comp_type` varchar(2) DEFAULT NULL COMMENT '公司类型',
  `report_type` varchar(4) DEFAULT NULL COMMENT '报表类型',
  `end_type` varchar(10) DEFAULT NULL COMMENT '报告期类型',
  `net_profit` decimal(20,6) DEFAULT NULL COMMENT '净利润',
  `finan_exp` decimal(20,6) DEFAULT NULL COMMENT '财务费用',
  `c_fr_sale_sg` decimal(20,6) DEFAULT NULL COMMENT '销售商品、提供劳务收到的现金',
  `recp_tax_rends` decimal(20,6) DEFAULT NULL COMMENT '收到的税费返还',
  `n_depos_incr_fi` decimal(20,6) DEFAULT NULL COMMENT '客户存款和同业存放款项净增加额',
  `n_incr_loans_cb` decimal(20,6) DEFAULT NULL COMMENT '向中央银行借款净增加额',
  `n_inc_borr_oth_fi` decimal(20,6) DEFAULT NULL COMMENT '向其他金融机构拆入资金净增加额',
  `prem_fr_orig_contr` decimal(20,6) DEFAULT NULL COMMENT '收到原保险合同保费取得的现金',
  `n_incr_insured_dep` decimal(20,6) DEFAULT NULL COMMENT '保户储金净增加额',
  `n_reinsur_prem` decimal(20,6) DEFAULT NULL COMMENT '收到再保业务现金净额',
  `n_incr_disp_tfa` decimal(20,6) DEFAULT NULL COMMENT '处置交易性金融资产净增加额',
  `ifc_cash_incr` decimal(20,6) DEFAULT NULL COMMENT '收取利息和手续费净增加额',
  `n_incr_disp_faas` decimal(20,6) DEFAULT NULL COMMENT '处置可供出售金融资产净增加额',
  `n_incr_loans_oth_bank` decimal(20,6) DEFAULT NULL COMMENT '拆入资金净增加额',
  `n_cap_incr_repur` decimal(20,6) DEFAULT NULL COMMENT '回购业务资金净增加额',
  `c_fr_oth_operate_a` decimal(20,6) DEFAULT NULL COMMENT '收到其他与经营活动有关的现金',
  `c_inf_fr_operate_a` decimal(20,6) DEFAULT NULL COMMENT '经营活动现金流入小计',
  `c_paid_goods_s` decimal(20,6) DEFAULT NULL COMMENT '购买商品、接受劳务支付的现金',
  `c_paid_to_for_empl` decimal(20,6) DEFAULT NULL COMMENT '支付给职工以及为职工支付的现金',
  `c_paid_for_taxes` decimal(20,6) DEFAULT NULL COMMENT '支付的各项税费',
  `n_incr_clt_loan_adv` decimal(20,6) DEFAULT NULL COMMENT '客户贷款及垫款净增加额',
  `n_incr_dep_cbob` decimal(20,6) DEFAULT NULL COMMENT '存放央行和同业款项净增加额',
  `c_pay_claims_orig_inco` decimal(20,6) DEFAULT NULL COMMENT '支付原保险合同赔付款项的现金',
  `pay_handling_chrg` decimal(20,6) DEFAULT NULL COMMENT '支付手续费的现金',
  `pay_comm_insur_plcy` decimal(20,6) DEFAULT NULL COMMENT '支付保单红利的现金',
  `oth_cash_pay_oper_act` decimal(20,6) DEFAULT NULL COMMENT '支付其他与经营活动有关的现金',
  `st_cash_out_act` decimal(20,6) DEFAULT NULL COMMENT '经营活动现金流出小计',
  `n_cashflow_act` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额',
  `oth_recp_ral_inv_act` decimal(20,6) DEFAULT NULL COMMENT '收到其他与投资活动有关的现金',
  `c_disp_withdrwl_invest` decimal(20,6) DEFAULT NULL COMMENT '收回投资收到的现金',
  `c_recp_return_invest` decimal(20,6) DEFAULT NULL COMMENT '取得投资收益收到的现金',
  `n_recp_disp_fiolta` decimal(20,6) DEFAULT NULL COMMENT '处置固定资产、无形资产和其他长期资产收回的现金净额',
  `n_recp_disp_sobu` decimal(20,6) DEFAULT NULL COMMENT '处置子公司及其他营业单位收到的现金净额',
  `stot_inflows_inv_act` decimal(20,6) DEFAULT NULL COMMENT '投资活动现金流入小计',
  `c_pay_acq_const_fiolta` decimal(20,6) DEFAULT NULL COMMENT '购建固定资产、无形资产和其他长期资产支付的现金',
  `c_paid_invest` decimal(20,6) DEFAULT NULL COMMENT '投资支付的现金',
  `n_disp_subs_oth_biz` decimal(20,6) DEFAULT NULL COMMENT '取得子公司及其他营业单位支付的现金净额',
  `oth_pay_ral_inv_act` decimal(20,6) DEFAULT NULL COMMENT '支付其他与投资活动有关的现金',
  `n_incr_pledge_loan` decimal(20,6) DEFAULT NULL COMMENT '质押贷款净增加额',
  `stot_out_inv_act` decimal(20,6) DEFAULT NULL COMMENT '投资活动现金流出小计',
  `n_cashflow_inv_act` decimal(20,6) DEFAULT NULL COMMENT '投资活动产生的现金流量净额',
  `c_recp_borrow` decimal(20,6) DEFAULT NULL COMMENT '取得借款收到的现金',
  `proc_issue_bonds` decimal(20,6) DEFAULT NULL COMMENT '发行债券收到的现金',
  `oth_cash_recp_ral_fnc_act` decimal(20,6) DEFAULT NULL COMMENT '收到其他与筹资活动有关的现金',
  `stot_cash_in_fnc_act` decimal(20,6) DEFAULT NULL COMMENT '筹资活动现金流入小计',
  `free_cashflow` decimal(20,6) DEFAULT NULL COMMENT '企业自由现金流量',
  `c_prepay_amt_borr` decimal(20,6) DEFAULT NULL COMMENT '偿还债务支付的现金',
  `c_pay_dist_dpcp_int_exp` decimal(20,6) DEFAULT NULL COMMENT '分配股利、利润或偿付利息支付的现金',
  `incl_dvd_profit_paid_sc_ms` decimal(20,6) DEFAULT NULL COMMENT '其中:子公司支付给少数股东的股利、利润',
  `oth_cashpay_ral_fnc_act` decimal(20,6) DEFAULT NULL COMMENT '支付其他与筹资活动有关的现金',
  `stot_cashout_fnc_act` decimal(20,6) DEFAULT NULL COMMENT '筹资活动现金流出小计',
  `n_cash_flows_fnc_act` decimal(20,6) DEFAULT NULL COMMENT '筹资活动产生的现金流量净额',
  `eff_fx_flu_cash` decimal(20,6) DEFAULT NULL COMMENT '汇率变动对现金的影响',
  `n_incr_cash_cash_equ` decimal(20,6) DEFAULT NULL COMMENT '现金及现金等价物净增加额',
  `c_cash_equ_beg_period` decimal(20,6) DEFAULT NULL COMMENT '期初现金及现金等价物余额',
  `c_cash_equ_end_period` decimal(20,6) DEFAULT NULL COMMENT '期末现金及现金等价物余额',
  `c_recp_cap_contrib` decimal(20,6) DEFAULT NULL COMMENT '吸收投资收到的现金',
  `incl_cash_rec_saims` decimal(20,6) DEFAULT NULL COMMENT '其中:子公司吸收少数股东投资收到的现金',
  `uncon_invest_loss` decimal(20,6) DEFAULT NULL COMMENT '未确认投资损失',
  `prov_depr_assets` decimal(20,6) DEFAULT NULL COMMENT '加:资产减值准备',
  `depr_fa_coga_dpba` decimal(20,6) DEFAULT NULL COMMENT '固定资产折旧、油气资产折耗、生产性生物资产折旧',
  `amort_intang_assets` decimal(20,6) DEFAULT NULL COMMENT '无形资产摊销',
  `lt_amort_deferred_exp` decimal(20,6) DEFAULT NULL COMMENT '长期待摊费用摊销',
  `decr_deferred_exp` decimal(20,6) DEFAULT NULL COMMENT '待摊费用减少',
  `incr_acc_exp` decimal(20,6) DEFAULT NULL COMMENT '预提费用增加',
  `loss_disp_fiolta` decimal(20,6) DEFAULT NULL COMMENT '处置固定、无形资产和其他长期资产的损失',
  `loss_scr_fa` decimal(20,6) DEFAULT NULL COMMENT '固定资产报废损失',
  `loss_fv_chg` decimal(20,6) DEFAULT NULL COMMENT '公允价值变动损失',
  `invest_loss` decimal(20,6) DEFAULT NULL COMMENT '投资损失',
  `decr_def_inc_tax_assets` decimal(20,6) DEFAULT NULL COMMENT '递延所得税资产减少',
  `incr_def_inc_tax_liab` decimal(20,6) DEFAULT NULL COMMENT '递延所得税负债增加',
  `decr_inventories` decimal(20,6) DEFAULT NULL COMMENT '存货的减少',
  `decr_oper_payable` decimal(20,6) DEFAULT NULL COMMENT '经营性应收项目的减少',
  `incr_oper_payable` decimal(20,6) DEFAULT NULL COMMENT '经营性应付项目的增加',
  `others` decimal(20,6) DEFAULT NULL COMMENT '其他',
  `im_net_cashflow_oper_act` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额(间接法)',
  `conv_debt_into_cap` decimal(20,6) DEFAULT NULL COMMENT '债务转为资本',
  `conv_copbonds_due_within_1y` decimal(20,6) DEFAULT NULL COMMENT '一年内到期的可转换公司债券',
  `fa_fnc_leases` decimal(20,6) DEFAULT NULL COMMENT '融资租入固定资产',
  `im_n_incr_cash_equ` decimal(20,6) DEFAULT NULL COMMENT '现金及现金等价物净增加额(间接法)',
  `net_dism_capital_add` decimal(20,6) DEFAULT NULL COMMENT '拆出资金净增加额',
  `net_cash_rece_sec` decimal(20,6) DEFAULT NULL COMMENT '代理买卖证券收到的现金净额(元)',
  `credit_impa_loss` decimal(20,6) DEFAULT NULL COMMENT '信用减值损失',
  `use_right_asset_dep` decimal(20,6) DEFAULT NULL COMMENT '使用权资产折旧',
  `oth_loss_asset` decimal(20,6) DEFAULT NULL COMMENT '其他资产减值损失',
  `end_bal_cash` decimal(20,6) DEFAULT NULL COMMENT '现金的期末余额',
  `beg_bal_cash` decimal(20,6) DEFAULT NULL COMMENT '减:现金的期初余额',
  `end_bal_cash_equ` decimal(20,6) DEFAULT NULL COMMENT '加:现金等价物的期末余额',
  `beg_bal_cash_equ` decimal(20,6) DEFAULT NULL COMMENT '减:现金等价物的期初余额',
  `update_flag` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新标志',
  PRIMARY KEY (`ts_code`,`end_date`,`update_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公司现金流量表';

-- ----------------------------
-- Table structure for financial_indicator
-- ----------------------------
DROP TABLE IF EXISTS `financial_indicator`;
CREATE TABLE `financial_indicator` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS代码',
  `ann_date` date NOT NULL COMMENT '公告日期',
  `end_date` date NOT NULL COMMENT '报告期',
  `eps` decimal(20,6) DEFAULT NULL COMMENT '基本每股收益',
  `dt_eps` decimal(20,6) DEFAULT NULL COMMENT '稀释每股收益',
  `total_revenue_ps` decimal(20,6) DEFAULT NULL COMMENT '每股营业总收入',
  `revenue_ps` decimal(20,6) DEFAULT NULL COMMENT '每股营业收入',
  `capital_rese_ps` decimal(20,6) DEFAULT NULL COMMENT '每股资本公积',
  `surplus_rese_ps` decimal(20,6) DEFAULT NULL COMMENT '每股盈余公积',
  `undist_profit_ps` decimal(20,6) DEFAULT NULL COMMENT '每股未分配利润',
  `extra_item` decimal(20,6) DEFAULT NULL COMMENT '非经常性损益',
  `profit_dedt` decimal(20,6) DEFAULT NULL COMMENT '扣除非经常性损益后的净利润（扣非净利润）',
  `gross_margin` decimal(20,6) DEFAULT NULL COMMENT '毛利',
  `current_ratio` decimal(20,6) DEFAULT NULL COMMENT '流动比率',
  `quick_ratio` decimal(20,6) DEFAULT NULL COMMENT '速动比率',
  `cash_ratio` decimal(20,6) DEFAULT NULL COMMENT '保守速动比率',
  `invturn_days` decimal(20,6) DEFAULT NULL COMMENT '存货周转天数',
  `arturn_days` decimal(20,6) DEFAULT NULL COMMENT '应收账款周转天数',
  `inv_turn` decimal(20,6) DEFAULT NULL COMMENT '存货周转率',
  `ar_turn` decimal(20,6) DEFAULT NULL COMMENT '应收账款周转率',
  `ca_turn` decimal(20,6) DEFAULT NULL COMMENT '流动资产周转率',
  `fa_turn` decimal(20,6) DEFAULT NULL COMMENT '固定资产周转率',
  `assets_turn` decimal(20,6) DEFAULT NULL COMMENT '总资产周转率',
  `op_income` decimal(20,6) DEFAULT NULL COMMENT '经营活动净收益',
  `valuechange_income` decimal(20,6) DEFAULT NULL COMMENT '价值变动净收益',
  `interst_income` decimal(20,6) DEFAULT NULL COMMENT '利息费用',
  `daa` decimal(20,6) DEFAULT NULL COMMENT '折旧与摊销',
  `ebit` decimal(20,6) DEFAULT NULL COMMENT '息税前利润',
  `ebitda` decimal(20,6) DEFAULT NULL COMMENT '息税折旧摊销前利润',
  `fcff` decimal(20,6) DEFAULT NULL COMMENT '企业自由现金流量',
  `fcfe` decimal(20,6) DEFAULT NULL COMMENT '股权自由现金流量',
  `current_exint` decimal(20,6) DEFAULT NULL COMMENT '无息流动负债',
  `noncurrent_exint` decimal(20,6) DEFAULT NULL COMMENT '无息非流动负债',
  `interestdebt` decimal(20,6) DEFAULT NULL COMMENT '带息债务',
  `netdebt` decimal(20,6) DEFAULT NULL COMMENT '净债务',
  `tangible_asset` decimal(20,6) DEFAULT NULL COMMENT '有形资产',
  `working_capital` decimal(20,6) DEFAULT NULL COMMENT '营运资金',
  `networking_capital` decimal(20,6) DEFAULT NULL COMMENT '营运流动资本',
  `invest_capital` decimal(20,6) DEFAULT NULL COMMENT '全部投入资本',
  `retained_earnings` decimal(20,6) DEFAULT NULL COMMENT '留存收益',
  `diluted2_eps` decimal(20,6) DEFAULT NULL COMMENT '期末摊薄每股收益',
  `bps` decimal(20,6) DEFAULT NULL COMMENT '每股净资产',
  `ocfps` decimal(20,6) DEFAULT NULL COMMENT '每股经营活动产生的现金流量净额',
  `retainedps` decimal(20,6) DEFAULT NULL COMMENT '每股留存收益',
  `cfps` decimal(20,6) DEFAULT NULL COMMENT '每股现金流量净额',
  `ebit_ps` decimal(20,6) DEFAULT NULL COMMENT '每股息税前利润',
  `fcff_ps` decimal(20,6) DEFAULT NULL COMMENT '每股企业自由现金流量',
  `fcfe_ps` decimal(20,6) DEFAULT NULL COMMENT '每股股东自由现金流量',
  `netprofit_margin` decimal(20,6) DEFAULT NULL COMMENT '销售净利率',
  `grossprofit_margin` decimal(20,6) DEFAULT NULL COMMENT '销售毛利率',
  `cogs_of_sales` decimal(20,6) DEFAULT NULL COMMENT '销售成本率',
  `expense_of_sales` decimal(20,6) DEFAULT NULL COMMENT '销售期间费用率',
  `profit_to_gr` decimal(20,6) DEFAULT NULL COMMENT '净利润/营业总收入',
  `saleexp_to_gr` decimal(20,6) DEFAULT NULL COMMENT '销售费用/营业总收入',
  `adminexp_of_gr` decimal(20,6) DEFAULT NULL COMMENT '管理费用/营业总收入',
  `finaexp_of_gr` decimal(20,6) DEFAULT NULL COMMENT '财务费用/营业总收入',
  `impai_ttm` decimal(20,6) DEFAULT NULL COMMENT '资产减值损失/营业总收入',
  `gc_of_gr` decimal(20,6) DEFAULT NULL COMMENT '营业总成本/营业总收入',
  `op_of_gr` decimal(20,6) DEFAULT NULL COMMENT '营业利润/营业总收入',
  `ebit_of_gr` decimal(20,6) DEFAULT NULL COMMENT '息税前利润/营业总收入',
  `roe` decimal(20,6) DEFAULT NULL COMMENT '净资产收益率',
  `roe_waa` decimal(20,6) DEFAULT NULL COMMENT '加权平均净资产收益率',
  `roe_dt` decimal(20,6) DEFAULT NULL COMMENT '净资产收益率(扣除非经常损益)',
  `roa` decimal(20,6) DEFAULT NULL COMMENT '总资产报酬率',
  `npta` decimal(20,6) DEFAULT NULL COMMENT '总资产净利润',
  `roic` decimal(20,6) DEFAULT NULL COMMENT '投入资本回报率',
  `roe_yearly` decimal(20,6) DEFAULT NULL COMMENT '年化净资产收益率',
  `roa2_yearly` decimal(20,6) DEFAULT NULL COMMENT '年化总资产报酬率',
  `roe_avg` decimal(20,6) DEFAULT NULL COMMENT '平均净资产收益率(增发条件)',
  `opincome_of_ebt` decimal(20,6) DEFAULT NULL COMMENT '经营活动净收益/利润总额',
  `investincome_of_ebt` decimal(20,6) DEFAULT NULL COMMENT '价值变动净收益/利润总额',
  `n_op_profit_of_ebt` decimal(20,6) DEFAULT NULL COMMENT '营业外收支净额/利润总额',
  `tax_to_ebt` decimal(20,6) DEFAULT NULL COMMENT '所得税/利润总额',
  `dtprofit_to_profit` decimal(20,6) DEFAULT NULL COMMENT '扣除非经常损益后的净利润/净利润',
  `salescash_to_or` decimal(20,6) DEFAULT NULL COMMENT '销售商品提供劳务收到的现金/营业收入',
  `ocf_to_or` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额/营业收入',
  `ocf_to_opincome` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额/经营活动净收益',
  `capitalized_to_da` decimal(20,6) DEFAULT NULL COMMENT '资本支出/折旧和摊销',
  `debt_to_assets` decimal(20,6) DEFAULT NULL COMMENT '资产负债率',
  `assets_to_eqt` decimal(20,6) DEFAULT NULL COMMENT '权益乘数',
  `dp_assets_to_eqt` decimal(20,6) DEFAULT NULL COMMENT '权益乘数(杜邦分析)',
  `ca_to_assets` decimal(20,6) DEFAULT NULL COMMENT '流动资产/总资产',
  `nca_to_assets` decimal(20,6) DEFAULT NULL COMMENT '非流动资产/总资产',
  `tbassets_to_totalassets` decimal(20,6) DEFAULT NULL COMMENT '有形资产/总资产',
  `int_to_talcap` decimal(20,6) DEFAULT NULL COMMENT '带息债务/全部投入资本',
  `eqt_to_talcapital` decimal(20,6) DEFAULT NULL COMMENT '归属于母公司的股东权益/全部投入资本',
  `currentdebt_to_debt` decimal(20,6) DEFAULT NULL COMMENT '流动负债/负债合计',
  `longdeb_to_debt` decimal(20,6) DEFAULT NULL COMMENT '非流动负债/负债合计',
  `ocf_to_shortdebt` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额/流动负债',
  `debt_to_eqt` decimal(20,6) DEFAULT NULL COMMENT '产权比率',
  `eqt_to_debt` decimal(20,6) DEFAULT NULL COMMENT '归属于母公司的股东权益/负债合计',
  `eqt_to_interestdebt` decimal(20,6) DEFAULT NULL COMMENT '归属于母公司的股东权益/带息债务',
  `tangibleasset_to_debt` decimal(20,6) DEFAULT NULL COMMENT '有形资产/负债合计',
  `tangasset_to_intdebt` decimal(20,6) DEFAULT NULL COMMENT '有形资产/带息债务',
  `tangibleasset_to_netdebt` decimal(20,6) DEFAULT NULL COMMENT '有形资产/净债务',
  `ocf_to_debt` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额/负债合计',
  `ocf_to_interestdebt` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额/带息债务',
  `ocf_to_netdebt` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额/净债务',
  `ebit_to_interest` decimal(20,6) DEFAULT NULL COMMENT '已获利息倍数(EBIT/利息费用)',
  `longdebt_to_workingcapital` decimal(20,6) DEFAULT NULL COMMENT '长期债务与营运资金比率',
  `ebitda_to_debt` decimal(20,6) DEFAULT NULL COMMENT '息税折旧摊销前利润/负债合计',
  `turn_days` decimal(20,6) DEFAULT NULL COMMENT '营业周期',
  `roa_yearly` decimal(20,6) DEFAULT NULL COMMENT '年化总资产净利率',
  `roa_dp` decimal(20,6) DEFAULT NULL COMMENT '总资产净利率(杜邦分析)',
  `fixed_assets` decimal(20,6) DEFAULT NULL COMMENT '固定资产合计',
  `profit_prefin_exp` decimal(20,6) DEFAULT NULL COMMENT '扣除财务费用前营业利润',
  `non_op_profit` decimal(20,6) DEFAULT NULL COMMENT '非营业利润',
  `op_to_ebt` decimal(20,6) DEFAULT NULL COMMENT '营业利润／利润总额',
  `nop_to_ebt` decimal(20,6) DEFAULT NULL COMMENT '非营业利润／利润总额',
  `ocf_to_profit` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额／营业利润',
  `cash_to_liqdebt` decimal(20,6) DEFAULT NULL COMMENT '货币资金／流动负债',
  `cash_to_liqdebt_withinterest` decimal(20,6) DEFAULT NULL COMMENT '货币资金／带息流动负债',
  `op_to_liqdebt` decimal(20,6) DEFAULT NULL COMMENT '营业利润／流动负债',
  `op_to_debt` decimal(20,6) DEFAULT NULL COMMENT '营业利润／负债合计',
  `roic_yearly` decimal(20,6) DEFAULT NULL COMMENT '年化投入资本回报率',
  `total_fa_trun` decimal(20,6) DEFAULT NULL COMMENT '固定资产合计周转率',
  `profit_to_op` decimal(20,6) DEFAULT NULL COMMENT '利润总额／营业收入',
  `q_opincome` decimal(20,6) DEFAULT NULL COMMENT '经营活动单季度净收益',
  `q_investincome` decimal(20,6) DEFAULT NULL COMMENT '价值变动单季度净收益',
  `q_dtprofit` decimal(20,6) DEFAULT NULL COMMENT '扣除非经常损益后的单季度净利润',
  `q_eps` decimal(20,6) DEFAULT NULL COMMENT '每股收益(单季度)',
  `q_netprofit_margin` decimal(20,6) DEFAULT NULL COMMENT '销售净利率(单季度)',
  `q_gsprofit_margin` decimal(20,6) DEFAULT NULL COMMENT '销售毛利率(单季度)',
  `q_exp_to_sales` decimal(20,6) DEFAULT NULL COMMENT '销售期间费用率(单季度)',
  `q_profit_to_gr` decimal(20,6) DEFAULT NULL COMMENT '净利润／营业总收入(单季度)',
  `q_saleexp_to_gr` decimal(20,6) DEFAULT NULL COMMENT '销售费用／营业总收入 (单季度)',
  `q_adminexp_to_gr` decimal(20,6) DEFAULT NULL COMMENT '管理费用／营业总收入 (单季度)',
  `q_finaexp_to_gr` decimal(20,6) DEFAULT NULL COMMENT '财务费用／营业总收入 (单季度)',
  `q_impair_to_gr_ttm` decimal(20,6) DEFAULT NULL COMMENT '资产减值损失／营业总收入(单季度)',
  `q_gc_to_gr` decimal(20,6) DEFAULT NULL COMMENT '营业总成本／营业总收入 (单季度)',
  `q_op_to_gr` decimal(20,6) DEFAULT NULL COMMENT '营业利润／营业总收入(单季度)',
  `q_roe` decimal(20,6) DEFAULT NULL COMMENT '净资产收益率(单季度)',
  `q_dt_roe` decimal(20,6) DEFAULT NULL COMMENT '净资产单季度收益率(扣除非经常损益)',
  `q_npta` decimal(20,6) DEFAULT NULL COMMENT '总资产净利润(单季度)',
  `q_opincome_to_ebt` decimal(20,6) DEFAULT NULL COMMENT '经营活动净收益／利润总额(单季度)',
  `q_investincome_to_ebt` decimal(20,6) DEFAULT NULL COMMENT '价值变动净收益／利润总额(单季度)',
  `q_dtprofit_to_profit` decimal(20,6) DEFAULT NULL COMMENT '扣除非经常损益后的净利润／净利润(单季度)',
  `q_salescash_to_or` decimal(20,6) DEFAULT NULL COMMENT '销售商品提供劳务收到的现金／营业收入(单季度)',
  `q_ocf_to_sales` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额／营业收入(单季度)',
  `q_ocf_to_or` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额／经营活动净收益(单季度)',
  `basic_eps_yoy` decimal(20,6) DEFAULT NULL COMMENT '基本每股收益同比增长率(%)',
  `dt_eps_yoy` decimal(20,6) DEFAULT NULL COMMENT '稀释每股收益同比增长率(%)',
  `cfps_yoy` decimal(20,6) DEFAULT NULL COMMENT '每股经营活动产生的现金流量净额同比增长率(%)',
  `op_yoy` decimal(20,6) DEFAULT NULL COMMENT '营业利润同比增长率(%)',
  `ebt_yoy` decimal(20,6) DEFAULT NULL COMMENT '利润总额同比增长率(%)',
  `netprofit_yoy` decimal(20,6) DEFAULT NULL COMMENT '归属母公司股东的净利润同比增长率(%)',
  `dt_netprofit_yoy` decimal(20,6) DEFAULT NULL COMMENT '归属母公司股东的净利润-扣除非经常损益同比增长率(%)',
  `ocf_yoy` decimal(20,6) DEFAULT NULL COMMENT '经营活动产生的现金流量净额同比增长率(%)',
  `roe_yoy` decimal(20,6) DEFAULT NULL COMMENT '净资产收益率(摊薄)同比增长率(%)',
  `bps_yoy` decimal(20,6) DEFAULT NULL COMMENT '每股净资产相对年初增长率(%)',
  `assets_yoy` decimal(20,6) DEFAULT NULL COMMENT '资产总计相对年初增长率(%)',
  `eqt_yoy` decimal(20,6) DEFAULT NULL COMMENT '归属母公司的股东权益相对年初增长率(%)',
  `tr_yoy` decimal(20,6) DEFAULT NULL COMMENT '营业总收入同比增长率(%)',
  `or_yoy` decimal(20,6) DEFAULT NULL COMMENT '营业收入同比增长率(%)',
  `q_gr_yoy` decimal(20,6) DEFAULT NULL COMMENT '营业总收入同比增长率(%)(单季度)',
  `q_gr_qoq` decimal(20,6) DEFAULT NULL COMMENT '营业总收入环比增长率(%)(单季度)',
  `q_sales_yoy` decimal(20,6) DEFAULT NULL COMMENT '营业收入同比增长率(%)(单季度)',
  `q_sales_qoq` decimal(20,6) DEFAULT NULL COMMENT '营业收入环比增长率(%)(单季度)',
  `q_op_yoy` decimal(20,6) DEFAULT NULL COMMENT '营业利润同比增长率(%)(单季度)',
  `q_op_qoq` decimal(20,6) DEFAULT NULL COMMENT '营业利润环比增长率(%)(单季度)',
  `q_profit_yoy` decimal(20,6) DEFAULT NULL COMMENT '净利润同比增长率(%)(单季度)',
  `q_profit_qoq` decimal(20,6) DEFAULT NULL COMMENT '净利润环比增长率(%)(单季度)',
  `q_netprofit_yoy` decimal(20,6) DEFAULT NULL COMMENT '归属母公司股东的净利润同比增长率(%)(单季度)',
  `q_netprofit_qoq` decimal(20,6) DEFAULT NULL COMMENT '归属母公司股东的净利润环比增长率(%)(单季度)',
  `equity_yoy` decimal(20,6) DEFAULT NULL COMMENT '净资产同比增长率',
  `rd_exp` decimal(20,6) DEFAULT NULL COMMENT '研发费用',
  `update_flag` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新标识',
  PRIMARY KEY (`ts_code`,`ann_date`,`end_date`,`update_flag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公司财务指标数据';

-- ----------------------------
-- Table structure for holder
-- ----------------------------
DROP TABLE IF EXISTS `holder`;
CREATE TABLE `holder` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS股票代码',
  `ann_date` date DEFAULT NULL COMMENT '公告日期',
  `end_date` date NOT NULL COMMENT '报告期',
  `holder_name` varchar(100) NOT NULL COMMENT '股东名称',
  `hold_amount` decimal(20,6) DEFAULT NULL COMMENT '持有数量',
  `hold_ratio` decimal(20,6) DEFAULT NULL COMMENT '占总股本比例(%)',
  `hold_float_ratio` decimal(20,6) DEFAULT NULL COMMENT '占流通股本比例(%)',
  `hold_change` decimal(20,6) DEFAULT NULL COMMENT '持股变动',
  `holder_type` varchar(20) DEFAULT NULL COMMENT '股东性质',
  PRIMARY KEY (`ts_code`,`end_date`,`holder_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公司股东表';

-- ----------------------------
-- Table structure for income
-- ----------------------------
DROP TABLE IF EXISTS `income`;
CREATE TABLE `income` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS代码',
  `ann_date` date DEFAULT NULL COMMENT '公告日期',
  `f_ann_date` date DEFAULT NULL COMMENT '实际公告日期',
  `end_date` date NOT NULL COMMENT '报告期',
  `report_type` varchar(4) DEFAULT NULL COMMENT '报告类型',
  `comp_type` varchar(2) DEFAULT NULL COMMENT '公司类型',
  `end_type` varchar(10) DEFAULT NULL COMMENT '报告期类型',
  `basic_eps` decimal(20,6) DEFAULT NULL COMMENT '基本每股收益',
  `diluted_eps` decimal(20,6) DEFAULT NULL COMMENT '稀释每股收益',
  `total_revenue` decimal(20,6) DEFAULT NULL COMMENT '营业总收入',
  `revenue` decimal(20,6) DEFAULT NULL COMMENT '营业收入',
  `int_income` decimal(20,6) DEFAULT NULL COMMENT '利息收入',
  `prem_earned` decimal(20,6) DEFAULT NULL COMMENT '已赚保费',
  `comm_income` decimal(20,6) DEFAULT NULL COMMENT '手续费及佣金收入',
  `n_commis_income` decimal(20,6) DEFAULT NULL COMMENT '手续费及佣金净收入',
  `n_oth_income` decimal(20,6) DEFAULT NULL COMMENT '其他经营净收益',
  `n_oth_b_income` decimal(20,6) DEFAULT NULL COMMENT '加:其他业务净收益',
  `prem_income` decimal(20,6) DEFAULT NULL COMMENT '保险业务收入',
  `out_prem` decimal(20,6) DEFAULT NULL COMMENT '减:分出保费',
  `une_prem_reser` decimal(20,6) DEFAULT NULL COMMENT '提取未到期责任准备金',
  `reins_income` decimal(20,6) DEFAULT NULL COMMENT '其中:分保费收入',
  `n_sec_tb_income` decimal(20,6) DEFAULT NULL COMMENT '代理买卖证券业务净收入',
  `n_sec_uw_income` decimal(20,6) DEFAULT NULL COMMENT '证券承销业务净收入',
  `n_asset_mg_income` decimal(20,6) DEFAULT NULL COMMENT '受托客户资产管理业务净收入',
  `oth_b_income` decimal(20,6) DEFAULT NULL COMMENT '其他业务收入',
  `fv_value_chg_gain` decimal(20,6) DEFAULT NULL COMMENT '加:公允价值变动净收益',
  `invest_income` decimal(20,6) DEFAULT NULL COMMENT '加:投资净收益',
  `ass_invest_income` decimal(20,6) DEFAULT NULL COMMENT '其中:对联营企业和合营企业的投资收益',
  `forex_gain` decimal(20,6) DEFAULT NULL COMMENT '加:汇兑净收益',
  `total_cogs` decimal(20,6) DEFAULT NULL COMMENT '营业总成本',
  `oper_cost` decimal(20,6) DEFAULT NULL COMMENT '减:营业成本',
  `int_exp` decimal(20,6) DEFAULT NULL COMMENT '减:利息支出',
  `comm_exp` decimal(20,6) DEFAULT NULL COMMENT '减:手续费及佣金支出',
  `biz_tax_surchg` decimal(20,6) DEFAULT NULL COMMENT '减:营业税金及附加',
  `sell_exp` decimal(20,6) DEFAULT NULL COMMENT '减:销售费用',
  `admin_exp` decimal(20,6) DEFAULT NULL COMMENT '减:管理费用',
  `fin_exp` decimal(20,6) DEFAULT NULL COMMENT '减:财务费用',
  `assets_impair_loss` decimal(20,6) DEFAULT NULL COMMENT '减:资产减值损失',
  `prem_refund` decimal(20,6) DEFAULT NULL COMMENT '退保金',
  `compens_payout` decimal(20,6) DEFAULT NULL COMMENT '赔付总支出',
  `reser_insur_liab` decimal(20,6) DEFAULT NULL COMMENT '提取保险责任准备金',
  `div_payt` decimal(20,6) DEFAULT NULL COMMENT '保户红利支出',
  `reins_exp` decimal(20,6) DEFAULT NULL COMMENT '分保费用',
  `oper_exp` decimal(20,6) DEFAULT NULL COMMENT '营业支出',
  `compens_payout_refu` decimal(20,6) DEFAULT NULL COMMENT '减:摊回赔付支出',
  `insur_reser_refu` decimal(20,6) DEFAULT NULL COMMENT '减:摊回保险责任准备金',
  `reins_cost_refund` decimal(20,6) DEFAULT NULL COMMENT '减:摊回分保费用',
  `other_bus_cost` decimal(20,6) DEFAULT NULL COMMENT '其他业务成本',
  `operate_profit` decimal(20,6) DEFAULT NULL COMMENT '营业利润',
  `non_oper_income` decimal(20,6) DEFAULT NULL COMMENT '加:营业外收入',
  `non_oper_exp` decimal(20,6) DEFAULT NULL COMMENT '减:营业外支出',
  `nca_disploss` decimal(20,6) DEFAULT NULL COMMENT '其中:减:非流动资产处置净损失',
  `total_profit` decimal(20,6) DEFAULT NULL COMMENT '利润总额',
  `income_tax` decimal(20,6) DEFAULT NULL COMMENT '所得税费用',
  `n_income` decimal(20,6) DEFAULT NULL COMMENT '净利润(含少数股东损益)',
  `n_income_attr_p` decimal(20,6) DEFAULT NULL COMMENT '净利润(不含少数股东损益)',
  `minority_gain` decimal(20,6) DEFAULT NULL COMMENT '少数股东损益',
  `oth_compr_income` decimal(20,6) DEFAULT NULL COMMENT '其他综合收益',
  `t_compr_income` decimal(20,6) DEFAULT NULL COMMENT '综合收益总额',
  `compr_inc_attr_p` decimal(20,6) DEFAULT NULL COMMENT '归属于母公司(或股东)的综合收益总额',
  `compr_inc_attr_m_s` decimal(20,6) DEFAULT NULL COMMENT '归属于少数股东的综合收益总额',
  `ebit` decimal(20,6) DEFAULT NULL COMMENT '息税前利润',
  `ebitda` decimal(20,6) DEFAULT NULL COMMENT '息税折旧摊销前利润',
  `insurance_exp` decimal(20,6) DEFAULT NULL COMMENT '保险业务支出',
  `undist_profit` decimal(20,6) DEFAULT NULL COMMENT '年初未分配利润',
  `distable_profit` decimal(20,6) DEFAULT NULL COMMENT '可分配利润',
  `rd_exp` decimal(20,6) DEFAULT NULL COMMENT '研发费用',
  `fin_exp_int_exp` decimal(20,6) DEFAULT NULL COMMENT '财务费用:利息费用',
  `fin_exp_int_inc` decimal(20,6) DEFAULT NULL COMMENT '财务费用:利息收入',
  `transfer_surplus_rese` decimal(20,6) DEFAULT NULL COMMENT '盈余公积转入',
  `transfer_housing_imprest` decimal(20,6) DEFAULT NULL COMMENT '住房周转金转入',
  `transfer_oth` decimal(20,6) DEFAULT NULL COMMENT '其他转入',
  `adj_lossgain` decimal(20,6) DEFAULT NULL COMMENT '调整以前年度损益',
  `withdra_legal_surplus` decimal(20,6) DEFAULT NULL COMMENT '提取法定盈余公积',
  `withdra_legal_pubfund` decimal(20,6) DEFAULT NULL COMMENT '提取法定公益金',
  `withdra_biz_devfund` decimal(20,6) DEFAULT NULL COMMENT '提取企业发展基金',
  `withdra_rese_fund` decimal(20,6) DEFAULT NULL COMMENT '提取储备基金',
  `withdra_oth_ersu` decimal(20,6) DEFAULT NULL COMMENT '提取任意盈余公积金',
  `workers_welfare` decimal(20,6) DEFAULT NULL COMMENT '职工奖金福利',
  `distr_profit_shrhder` decimal(20,6) DEFAULT NULL COMMENT '可供股东分配的利润',
  `prfshare_payable_dvd` decimal(20,6) DEFAULT NULL COMMENT '应付优先股股利',
  `comshare_payable_dvd` decimal(20,6) DEFAULT NULL COMMENT '应付普通股股利',
  `capit_comstock_div` decimal(20,6) DEFAULT NULL COMMENT '转作股本的普通股股利',
  `net_after_nr_lp_correct` decimal(20,6) DEFAULT NULL COMMENT '扣除非经常性损益后的净利润（更正前）',
  `oth_income` decimal(20,6) DEFAULT NULL COMMENT '其他收益',
  `asset_disp_income` decimal(20,6) DEFAULT NULL COMMENT '资产处置收益',
  `continued_net_profit` decimal(20,6) DEFAULT NULL COMMENT '持续经营净利润',
  `end_net_profit` decimal(20,6) DEFAULT NULL COMMENT '终止经营净利润',
  `credit_impa_loss` decimal(20,6) DEFAULT NULL COMMENT '信用减值损失',
  `net_expo_hedging_benefits` decimal(20,6) DEFAULT NULL COMMENT '净敞口套期收益',
  `oth_impair_loss_assets` decimal(20,6) DEFAULT NULL COMMENT '其他资产减值损失',
  `total_opcost` decimal(20,6) DEFAULT NULL COMMENT '营业总成本2',
  `amodcost_fin_assets` decimal(20,6) DEFAULT NULL COMMENT '以摊余成本计量的金融资产终止确认收益',
  `update_flag` varchar(2) NOT NULL COMMENT '更新标识',
  PRIMARY KEY (`ts_code`,`end_date`,`update_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公司利润表';

-- ----------------------------
-- Table structure for ths_index
-- ----------------------------
DROP TABLE IF EXISTS `ths_index`;
CREATE TABLE `ths_index` (
  `ts_code` varchar(20) NOT NULL COMMENT '代码',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `count` int DEFAULT NULL COMMENT '成分个数',
  `exchange` varchar(20) DEFAULT NULL COMMENT '交易所',
  `list_date` date DEFAULT NULL COMMENT '上市日期',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '指数类型 N-概念指数 I-行业指数 R-地域指数 S-同花顺特色指数 ST-同花顺风格指数 TH-同花顺主题指数 BB-同花顺宽基指数',
  PRIMARY KEY (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='同花顺行业概念板块';

-- ----------------------------
-- Table structure for trading_calendar
-- ----------------------------
DROP TABLE IF EXISTS `trading_calendar`;
CREATE TABLE `trading_calendar` (
  `exchange` varchar(10) NOT NULL COMMENT '交易所 SSE上交所 SZSE深交所',
  `cal_date` char(8) NOT NULL COMMENT '日历日期',
  `is_open` tinyint(1) NOT NULL COMMENT '是否交易 0休市 1交易',
  `pretrade_date` char(8) DEFAULT NULL COMMENT '上一个交易日',
  PRIMARY KEY (`exchange`,`cal_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交易日历表';

-- ----------------------------
-- Table structure for sw_index_daily
-- ----------------------------
DROP TABLE IF EXISTS `sw_index_daily`;
CREATE TABLE `sw_index_daily` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `ts_code` varchar(12) NOT NULL COMMENT '指数代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `name` varchar(50) NOT NULL COMMENT '指数名称',
  `open` decimal(10,2) DEFAULT NULL COMMENT '开盘点位',
  `low` decimal(10,2) DEFAULT NULL COMMENT '最低点位',
  `high` decimal(10,2) DEFAULT NULL COMMENT '最高点位',
  `close` decimal(10,2) DEFAULT NULL COMMENT '收盘点位',
  `change` decimal(10,2) DEFAULT NULL COMMENT '涨跌点位',
  `pct_change` decimal(6,2) DEFAULT NULL COMMENT '涨跌幅（%）',
  `vol` decimal(20,2) DEFAULT NULL COMMENT '成交量（万股）',
  `amount` decimal(20,2) DEFAULT NULL COMMENT '成交额（万元）',
  `pe` decimal(10,4) DEFAULT NULL COMMENT '市盈率',
  `pb` decimal(10,4) DEFAULT NULL COMMENT '市净率',
  `float_mv` decimal(20,2) DEFAULT NULL COMMENT '流通市值（万元）',
  `total_mv` decimal(20,2) DEFAULT NULL COMMENT '总市值（万元）',
  `weight` decimal(10,6) DEFAULT NULL COMMENT '权重',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_ts_date` (`ts_code`,`trade_date`),
  KEY `idx_trade_date` (`trade_date`),
  KEY `idx_ts_code` (`ts_code`)
) ENGINE=InnoDB AUTO_INCREMENT=647433 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='申万行业指数每日行情表';

-- ----------------------------
-- Table structure for stock_fund_flow
-- ----------------------------
DROP TABLE IF EXISTS `stock_fund_flow`;
CREATE TABLE `stock_fund_flow` (
  `ts_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'TS代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `buy_sm_vol` int NOT NULL COMMENT '小单买入量（手）',
  `buy_sm_amount` decimal(20,2) NOT NULL COMMENT '小单买入金额（万元）',
  `sell_sm_vol` int NOT NULL COMMENT '小单卖出量（手）',
  `sell_sm_amount` decimal(20,2) NOT NULL COMMENT '小单卖出金额（万元）',
  `buy_md_vol` int NOT NULL COMMENT '中单买入量（手）',
  `buy_md_amount` decimal(20,2) NOT NULL COMMENT '中单买入金额（万元）',
  `sell_md_vol` int NOT NULL COMMENT '中单卖出量（手）',
  `sell_md_amount` decimal(20,2) NOT NULL COMMENT '中单卖出金额（万元）',
  `buy_lg_vol` int NOT NULL COMMENT '大单买入量（手）',
  `buy_lg_amount` decimal(20,2) NOT NULL COMMENT '大单买入金额（万元）',
  `sell_lg_vol` int NOT NULL COMMENT '大单卖出量（手）',
  `sell_lg_amount` decimal(20,2) NOT NULL COMMENT '大单卖出金额（万元）',
  `buy_elg_vol` int NOT NULL COMMENT '特大单买入量（手）',
  `buy_elg_amount` decimal(20,2) NOT NULL COMMENT '特大单买入金额（万元）',
  `sell_elg_vol` int NOT NULL COMMENT '特大单卖出量（手）',
  `sell_elg_amount` decimal(20,2) NOT NULL COMMENT '特大单卖出金额（万元）',
  `net_mf_vol` int NOT NULL COMMENT '净流入量（手）',
  `net_mf_amount` decimal(20,2) NOT NULL COMMENT '净流入额（万元）',
  `trade_count` int NOT NULL COMMENT '交易笔数',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票资金流向数据表';

-- ----------------------------
-- Table structure for daily_basic
-- ----------------------------
DROP TABLE IF EXISTS `daily_basic`;
CREATE TABLE `daily_basic` (
  `ts_code` varchar(10) NOT NULL COMMENT 'TS股票代码，如：000001.SZ',
  `trade_date` date NOT NULL COMMENT '交易日期，格式：YYYY-MM-DD',
  `close` decimal(10,2) DEFAULT NULL COMMENT '当日收盘价，单位：元',
  `turnover_rate` decimal(5,2) DEFAULT NULL COMMENT '换手率，单位：%',
  `turnover_rate_f` decimal(5,2) DEFAULT NULL COMMENT '换手率(自由流通股)，单位：%',
  `volume_ratio` decimal(5,2) DEFAULT NULL COMMENT '量比，即成交量/5日平均成交量',
  `pe` decimal(10,2) DEFAULT NULL COMMENT '市盈率（总市值/净利润）',
  `pe_ttm` decimal(10,2) DEFAULT NULL COMMENT '市盈率（TTM），以最近12个月净利润为基础计算',
  `pb` decimal(10,2) DEFAULT NULL COMMENT '市净率（总市值/净资产）',
  `ps` decimal(10,2) DEFAULT NULL COMMENT '市销率（总市值/营业收入）',
  `ps_ttm` decimal(10,2) DEFAULT NULL COMMENT '市销率（TTM），以最近12个月营业收入为基础计算',
  `dv_ratio` decimal(5,2) DEFAULT NULL COMMENT '股息率，单位：%',
  `dv_ttm` decimal(5,2) DEFAULT NULL COMMENT '股息率（TTM），以最近12个月派息为基础计算，单位：%',
  `total_share` bigint NOT NULL COMMENT '总股本，单位：万股',
  `float_share` bigint DEFAULT NULL COMMENT '流通股本，单位：万股',
  `free_share` bigint DEFAULT NULL COMMENT '自由流通股本，单位：万股',
  `total_mv` decimal(20,2) DEFAULT NULL COMMENT '总市值，单位：万元',
  `circ_mv` decimal(20,2) DEFAULT NULL COMMENT '流通市值，单位：万元',
  `limit_status` int DEFAULT NULL COMMENT '涨跌停状态，1：涨停，-1：跌停，0：其他',
  PRIMARY KEY (`ts_code`,`trade_date`),
  KEY `idx_trade_date` (`trade_date`),
  KEY `idx_ts_code` (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票每日基本面指标';

-- ----------------------------
-- Table structure for stk_factor
-- ----------------------------
DROP TABLE IF EXISTS `stk_factor`;
CREATE TABLE `stk_factor` (
  `ts_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '股票代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `close` decimal(10,2) DEFAULT NULL COMMENT '收盘价',
  `open` decimal(10,2) DEFAULT NULL COMMENT '开盘价',
  `high` decimal(10,2) DEFAULT NULL COMMENT '最高价',
  `low` decimal(10,2) DEFAULT NULL COMMENT '最低价',
  `pre_close` decimal(10,2) DEFAULT NULL COMMENT '昨收价',
  `change` decimal(10,2) DEFAULT NULL COMMENT '涨跌额',
  `pct_change` decimal(10,4) DEFAULT NULL COMMENT '涨跌幅',
  `vol` bigint DEFAULT NULL COMMENT '成交量（手）',
  `amount` decimal(20,2) DEFAULT NULL COMMENT '成交额（千元）',
  `adj_factor` decimal(20,6) DEFAULT NULL COMMENT '复权因子',
  `open_hfq` decimal(10,2) DEFAULT NULL COMMENT '开盘价后复权',
  `open_qfq` decimal(10,2) DEFAULT NULL COMMENT '开盘价前复权',
  `close_hfq` decimal(10,2) DEFAULT NULL COMMENT '收盘价后复权',
  `close_qfq` decimal(10,2) DEFAULT NULL COMMENT '收盘价前复权',
  `high_hfq` decimal(10,2) DEFAULT NULL COMMENT '最高价后复权',
  `high_qfq` decimal(10,2) DEFAULT NULL COMMENT '最高价前复权',
  `low_hfq` decimal(10,2) DEFAULT NULL COMMENT '最低价后复权',
  `low_qfq` decimal(10,2) DEFAULT NULL COMMENT '最低价前复权',
  `pre_close_hfq` decimal(10,2) DEFAULT NULL COMMENT '昨收价后复权',
  `pre_close_qfq` decimal(10,2) DEFAULT NULL COMMENT '昨收价前复权',
  `macd_dif` decimal(10,6) DEFAULT NULL COMMENT 'MACD_DIF (基于前复权价格计算)',
  `macd_dea` decimal(10,6) DEFAULT NULL COMMENT 'MACD_DEA',
  `macd` decimal(10,6) DEFAULT NULL COMMENT 'MACD',
  `kdj_k` decimal(10,6) DEFAULT NULL COMMENT 'KDJ_K',
  `kdj_d` decimal(10,6) DEFAULT NULL COMMENT 'KDJ_D',
  `kdj_j` decimal(10,6) DEFAULT NULL COMMENT 'KDJ_J',
  `rsi_6` decimal(10,6) DEFAULT NULL COMMENT 'RSI_6',
  `rsi_12` decimal(10,6) DEFAULT NULL COMMENT 'RSI_12',
  `rsi_24` decimal(10,6) DEFAULT NULL COMMENT 'RSI_24',
  `boll_upper` decimal(10,6) DEFAULT NULL COMMENT 'BOLL_UPPER',
  `boll_mid` decimal(10,6) DEFAULT NULL COMMENT 'BOLL_MID',
  `boll_lower` decimal(10,6) DEFAULT NULL COMMENT 'BOLL_LOWER',
  `cci` decimal(10,6) DEFAULT NULL COMMENT 'CCI',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票交易数据表（复权）';

-- ----------------------------
-- Table structure for ths_daily
-- ----------------------------
DROP TABLE IF EXISTS `ths_daily`;
CREATE TABLE `ths_daily` (
  `ts_code` varchar(20) NOT NULL COMMENT 'TS指数代码',
  `trade_date` date NOT NULL COMMENT '交易日',
  `open` decimal(20,6) DEFAULT NULL COMMENT '开盘点位',
  `high` decimal(20,6) DEFAULT NULL COMMENT '最高点位',
  `low` decimal(20,6) DEFAULT NULL COMMENT '最低点位',
  `close` decimal(20,6) DEFAULT NULL COMMENT '收盘点位',
  `pre_close` decimal(20,6) DEFAULT NULL COMMENT '昨日收盘点',
  `avg_price` decimal(20,6) DEFAULT NULL COMMENT '平均点位',
  `change` decimal(20,6) DEFAULT NULL COMMENT '涨跌点位',
  `pct_change` decimal(20,6) DEFAULT NULL COMMENT '涨跌幅',
  `vol` double DEFAULT NULL,
  `turnover_rate` decimal(20,6) DEFAULT NULL COMMENT '换手率',
  `total_mv` double DEFAULT NULL,
  `float_mv` double DEFAULT NULL,
  `pe_ttm` decimal(20,6) DEFAULT NULL COMMENT 'PE TTM',
  `pb_mrq` decimal(20,6) DEFAULT NULL COMMENT 'PB MRQ',
  PRIMARY KEY (`ts_code`,`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='同花顺板块指数行情';

-- ----------------------------
-- Table structure for factors_money_focus_data
-- ----------------------------
DROP TABLE IF EXISTS `factors_money_focus_data`;
CREATE TABLE `factors_money_focus_data` (
  `ts_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'TS代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `main_money_ratio` decimal(10,4) NOT NULL COMMENT '主力资金占比',
  `retail_money_ratio` decimal(10,4) NOT NULL COMMENT '散户资金占比',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ts_code`,`trade_date`),
  KEY `idx_trade_date` (`trade_date`),
  KEY `idx_ts_code` (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='资金集中度数据表';

-- ----------------------------
-- Table structure for stock_basic
-- ----------------------------
DROP TABLE IF EXISTS `stock_basic`;
CREATE TABLE `stock_basic` (
  `ts_code` varchar(10) NOT NULL COMMENT 'TS代码',
  `symbol` varchar(6) NOT NULL COMMENT '股票代码',
  `name` varchar(50) NOT NULL COMMENT '股票名称',
  `fullname` varchar(100) DEFAULT NULL COMMENT '股票全称',
  `enname` varchar(100) DEFAULT NULL COMMENT '英文全称',
  `cnspell` varchar(20) DEFAULT NULL COMMENT '拼音缩写',
  `area` varchar(30) DEFAULT NULL COMMENT '地域',
  `industry` varchar(50) DEFAULT NULL COMMENT '所属行业',
  `market` varchar(20) DEFAULT NULL COMMENT '市场类型',
  `exchange` varchar(10) DEFAULT NULL COMMENT '交易所代码',
  `curr_type` varchar(10) DEFAULT 'CNY' COMMENT '交易货币',
  `list_status` char(1) DEFAULT NULL COMMENT '上市状态: L上市 D退市 P暂停上市',
  `list_date` date DEFAULT NULL COMMENT '上市日期',
  `delist_date` date DEFAULT NULL COMMENT '退市日期',
  `is_hs` char(1) DEFAULT NULL COMMENT '是否沪深港通标的: N否 H沪股通 S深股通',
  `act_name` varchar(100) DEFAULT NULL COMMENT '实控人名称',
  `act_ent_type` varchar(50) DEFAULT NULL COMMENT '实控人企业性质',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ts_code`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `idx_symbol` (`symbol`),
  KEY `idx_name` (`name`),
  KEY `idx_industry` (`industry`),
  KEY `idx_list_status` (`list_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='股票基础信息表';

-- ----------------------------
-- Table structure for sw_member
-- ----------------------------
DROP TABLE IF EXISTS `sw_member`;
CREATE TABLE `sw_member` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `l1_code` varchar(12) NOT NULL COMMENT '一级行业代码',
  `l1_name` varchar(50) NOT NULL COMMENT '一级行业名称',
  `l2_code` varchar(12) NOT NULL COMMENT '二级行业代码',
  `l2_name` varchar(50) NOT NULL COMMENT '二级行业名称',
  `l3_code` varchar(12) NOT NULL COMMENT '三级行业代码',
  `l3_name` varchar(50) NOT NULL COMMENT '三级行业名称',
  `ts_code` varchar(12) NOT NULL COMMENT '成分股票代码',
  `name` varchar(50) NOT NULL COMMENT '成分股票名称',
  `in_date` date NOT NULL COMMENT '纳入日期',
  `out_date` date DEFAULT NULL COMMENT '剔除日期（空为未剔除）',
  `is_new` char(1) NOT NULL DEFAULT 'Y' COMMENT '是否最新：Y是，N否',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_l3_ts_in` (`l3_code`,`ts_code`,`in_date`),
  KEY `idx_ts_code` (`ts_code`)
) ENGINE=InnoDB AUTO_INCREMENT=11631 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='申万行业成分股';

SET FOREIGN_KEY_CHECKS = 1;


-- ----------------------------
-- Table structure for adx_data
-- ----------------------------
CREATE TABLE `adx_data` (
  `ts_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'TS代码',
  `trade_date` date NOT NULL COMMENT '交易日期',
  `adx` decimal(10,4) NOT NULL COMMENT 'ADX值',
  `pdi` decimal(10,4) NOT NULL COMMENT '+DI值（正向指标）',
  `mdi` decimal(10,4) NOT NULL COMMENT '-DI值（负向指标）',
  `period` int NOT NULL COMMENT 'ADX计算周期（如14日）',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ts_code`,`trade_date`,`period`),
  KEY `idx_trade_date` (`trade_date`),
  KEY `idx_ts_code` (`ts_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='ADX因子数据表';