"""
@Author: <PERSON><PERSON>in
@Date: 2024/12/10 14:11
@Description: 获取股票每日的基本信息
"""
import time
import pandas as pd
# 导入tushare
import tushare as ts
from sqlalchemy import create_engine, inspect

from db_config import DB_URL, TUSHARE_TOKEN

#循环更新数据，指定一个开始时间及结束时间，trade_date在这里循环
def update_daily_basic(start_date, end_date):
    for trade_date in pd.date_range(start=start_date, end=end_date).strftime("%Y%m%d").tolist():
        print(f"开始处理----{trade_date}----个股收盘后基本数据 ")
        time.sleep(0.1)
        get_daily_basic(trade_date)

def get_daily_basic(trade_date):
    try:
        #初始化pro接口
        pro = ts.pro_api(TUSHARE_TOKEN)

        # 拉取数据
        df = pro.daily_basic(**{
            "ts_code": "",
            "trade_date": trade_date,
            "start_date": "",
            "end_date": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "ts_code",
            "trade_date",
            "close",
            "turnover_rate",
            "turnover_rate_f",
            "volume_ratio",
            "pe",
            "pe_ttm",
            "pb",
            "ps",
            "ps_ttm",
            "dv_ratio",
            "dv_ttm",
            "total_share",
            "float_share",
            "free_share",
            "total_mv",
            "circ_mv",
            "limit_status"
        ])

        # 判断数据是否为空
        if df.empty:
            print(f"{trade_date} 的daily_basic 数据为空，请检查")
            return {'msg': '没有数据'}


        # 创建数据库连接
        engine = create_engine(DB_URL)

        # 判断数据库是否存在，如果不存在则返回False
        inspector = inspect(engine)
        if 'daily_basic' in inspector.get_table_names():
            df.to_sql('daily_basic', engine, index=False, if_exists='append',chunksize=5000)
            # return {'msg': '更新成功','总数量': len(df),'data': df}
        else:
            print(f"{trade_date} 的daily_basic处理失败，请检查")
            return {'msg': '更新失败','success': False}

    except Exception as e:
        print("Error: ", e)


def get_last_trade_date():
    try:
        # 创建数据库连接
        engine = create_engine(DB_URL)

        # 查询最新交易日期
        sql = "SELECT MAX(trade_date) as last_trade_date FROM daily_basic"
        df = pd.read_sql(sql, engine)

        # 获取结果
        last_trade_date = df['last_trade_date'].iloc[0]

        return last_trade_date

    except Exception as e:
        print("Error getting last trade date: ", e)
        return None


if __name__ == '__main__':
    last_date = get_last_trade_date()
    if last_date is not None:
        # 获取最新交易日期的后一天
        start_date = (last_date + pd.Timedelta(days=1)).strftime('%Y%m%d')
        # end_date 为当前的时间
        end_date = pd.Timestamp.now().strftime('%Y%m%d')
        update_daily_basic(start_date, end_date)
    else:
        print("获取最新交易日期失败")