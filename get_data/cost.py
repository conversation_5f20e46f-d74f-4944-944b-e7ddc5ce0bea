"""
@Author: Jiang<PERSON>in
@Date: 2024/12/20 17:13
@Description: 获取股票筹码及胜率
"""
import time
import pandas as pd
# 导入tushare
import tushare as ts
from sqlalchemy import create_engine, inspect

from db_config import DB_URL, TUSHARE_TOKEN

#循环更新数据，指定一个开始时间及结束时间，trade_date在这里循环
def get_stock_cost_daily_with_period(start_date, end_date):
    for trade_date in pd.date_range(start=start_date, end=end_date).strftime("%Y%m%d").tolist():
        print(f"开始处理----{trade_date}----筹码数据")
        time.sleep(0.2)
        get_stock_cost_daily(trade_date)

def get_stock_cost_daily(trade_date):
    try:
        #初始化pro接口
        pro = ts.pro_api(TUSHARE_TOKEN)

        # 拉取数据
        # 拉取数据
        df = pro.cyq_perf(**{
            "ts_code": "",
            "trade_date": trade_date,
            "start_date": "",
            "end_date": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "ts_code",
            "trade_date",
            "his_low",
            "his_high",
            "cost_5pct",
            "cost_15pct",
            "cost_50pct",
            "cost_85pct",
            "cost_95pct",
            "weight_avg",
            "winner_rate"
        ])

        # 判断数据是否为空
        if df.empty:
            print(f"{trade_date} 没有返回筹码数据，结束！")
            return None
        else:
            # 创建数据库连接
            engine = create_engine(DB_URL)

            # 判断数据库是否存在，如果不存在则返回False
            inspector = inspect(engine)
            if 'stock_cost_analysis' in inspector.get_table_names():
                df.to_sql('stock_cost_analysis', engine, index=False, if_exists='append',chunksize=5000)
                print(f"{trade_date} 的筹码数据处理成功，搞下一个》》》")
                # return {'msg': '更新成功','总数量': len(df),'data': df}
            else:
                print(f"{trade_date} 的筹码数据处理失败，请检查")
                return {'msg': '更新失败','success': False}

    except Exception as e:
        print("Error: ", e)

def get_last_trade_date():
    try:
        # 创建数据库连接
        engine = create_engine(DB_URL)

        # 查询最新交易日期
        sql = "SELECT MAX(trade_date) as last_trade_date FROM stock_cost_analysis"
        df = pd.read_sql(sql, engine)

        # 获取结果
        last_trade_date = df['last_trade_date'].iloc[0]

        return last_trade_date

    except Exception as e:
        print("Error getting last trade date: ", e)
        return None

if __name__ == '__main__':
    last_date = get_last_trade_date()
    if last_date is not None:
        # 获取最新交易日期的后一天
        start_date = (last_date + pd.Timedelta(days=1)).strftime('%Y%m%d')
        # end_date 为当前的时间
        end_date = pd.Timestamp.now().strftime('%Y%m%d')
        get_stock_cost_daily_with_period(start_date, end_date)
    else:
        print("获取最新交易日期失败")

