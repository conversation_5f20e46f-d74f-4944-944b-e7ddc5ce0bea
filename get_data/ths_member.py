"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/20 18:07
@File: ths_member.py
@Version: 1.1
@Description: 同花顺概念板块成分。2025-06-04：废弃该文件，因为下载的数据不全。
"""
# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text  # 导入 text
import time  # 导入 time 模块
from db_config import TUSHARE_TOKEN, DB_CONFIG, DB_URL, get_engine

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)

# 将获取到的股票代码遍历调用pro.income接口获取收入报告
def save_ths_index():

    try:

        # 注意：请确保 db_config.py 中的 get_engine 是一个函数，而不是一个 Engine 实例。
        # 如果 get_engine() 调用持续出现 TypeError，问题很可能在 db_config.py 或此文件其他地方对 get_engine 的错误赋值。
        engine = get_engine()

        df = pro.ths_member(**{
            "ts_code": "",
            "con_code": "",
            "offset": "",
            "limit": ""
        }, fields=[
            "ts_code",
            "con_code",
            "con_name",
            "weight",
            "in_date",
            "out_date",
            "is_new"
        ])

        # 保存到数据库表
        if not df.empty:
            try:
                #保存数据
                df.to_sql('ths_member', engine, if_exists='append', index=False, chunksize=3000)
                print(f"成功保存报告，共{len(df)}条记录")
            except Exception as e:
                print(f"保存数据到数据库时发生错误: {e}")
                return None
        else:
            # 这段理论上不会执行了，因为前面已经continue了df.empty的情况
            print(f"警告:没有数据需要保存，跳过。")
            return None
    except Exception as e:
        error_message = f"处理过程中发生错误: {e}"
        print(error_message)
        return False


if __name__ == "__main__":
    save_ths_index()