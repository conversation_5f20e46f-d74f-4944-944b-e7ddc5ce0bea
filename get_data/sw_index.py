"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/6/11 15:56
@File: sw_index.py
@Version: 1.0
@Description: 未保存到数据库中，可以直接查看 csv 文件。
"""
import tushare as ts
# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 拉取数据
df = pro.index_classify(**{
    "index_code": "",
    "level": "",
    "src": "",
    "parent_code": "",
    "limit": "",
    "offset": ""
}, fields=[
    "index_code",
    "industry_name",
    "level",
    "industry_code",
    "is_pub",
    "parent_code",
    "src"
])
print(df)
df.to_csv("sw_index.csv", index=False)
