"""
@Author: Jiang<PERSON>in
@Date: 2024/12/10 17:56
@Description: 获取个股后复权的数据
"""

import time
import pandas as pd
# 导入tushare
import tushare as ts
from sqlalchemy import create_engine, inspect

from db_config import DB_URL, TUSHARE_TOKEN

#循环更新数据，指定一个开始时间及结束时间，trade_date在这里循环
def get_stk_factor_with_period(start_date, end_date):
    for trade_date in pd.date_range(start=start_date, end=end_date).strftime("%Y%m%d").tolist():
        print(f"开始处理----{trade_date}----复权因子数据 ")
        time.sleep(0.2)
        get_hfq_rights_price_daily(trade_date)

def get_hfq_rights_price_daily(trade_date):
    try:
        #初始化pro接口
        pro = ts.pro_api(TUSHARE_TOKEN)

        # 拉取数据
        # 拉取数据
        df = pro.stk_factor(**{
            "ts_code": "",
            "start_date": "",
            "end_date": "",
            "trade_date": trade_date,
            "limit": "",
            "offset": ""
        }, fields=[
            "ts_code",
            "trade_date",
            "close",
            "open",
            "high",
            "low",
            "pre_close",
            "change",
            "pct_change",
            "vol",
            "amount",
            "adj_factor",
            "open_hfq",
            "open_qfq",
            "close_hfq",
            "close_qfq",
            "high_hfq",
            "high_qfq",
            "low_hfq",
            "low_qfq",
            "pre_close_hfq",
            "pre_close_qfq",
            "macd_dif",
            "macd_dea",
            "macd",
            "kdj_k",
            "kdj_d",
            "kdj_j",
            "rsi_6",
            "rsi_12",
            "rsi_24",
            "boll_upper",
            "boll_mid",
            "boll_lower",
            "cci"
        ])

        # 判断数据是否为空
        if df.empty:
            print(f"{trade_date} 的 stk_factor 数据为空，请检查")
            return None
        else:
            # 创建数据库连接
            engine = create_engine(DB_URL)

            # 判断数据库是否存在，如果不存在则返回False
            inspector = inspect(engine)
            if 'stk_factor' in inspector.get_table_names():
                df.to_sql('stk_factor', engine, index=False, if_exists='append',chunksize=5000)
                # return {'msg': '更新成功','总数量': len(df),'data': df}
            else:
                print(f"{trade_date} 的stk_factor处理失败，请检查")
                return {'msg': '更新失败','success': False}

    except Exception as e:
        print("Error: ", e)


def get_last_trade_date():
    try:
        # 创建数据库连接
        engine = create_engine(DB_URL)

        # 查询最新交易日期
        sql = "SELECT MAX(trade_date) as last_trade_date FROM stk_factor"
        df = pd.read_sql(sql, engine)

        # 获取结果
        last_trade_date = df['last_trade_date'].iloc[0]

        return last_trade_date

    except Exception as e:
        print("Error getting last trade date: ", e)
        return None

if __name__ == '__main__':
    last_date = get_last_trade_date()
    if last_date is not None:
        # 获取最新交易日期的后一天
        start_date = (last_date + pd.Timedelta(days=1)).strftime('%Y%m%d')
        # end_date 为当前的时间
        end_date = pd.Timestamp.now().strftime('%Y%m%d')
        get_stk_factor_with_period(start_date, end_date)
    else:
        print("获取最新交易日期失败")
