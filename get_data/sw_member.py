"""
获取申万行业成份股数据.
不知道更新的频率，只插入了一次。
"""


# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy.exc import IntegrityError
from get_data.db_config import get_engine,DB_URL
from sqlalchemy import create_engine, inspect, text


# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 循环获取申万行业成份股数据
def get_sw_member_data():
    limit = 3000  # tushare pro 单次最大返回条数
    offset = 0
    dfs = []
    
    print("开始获取全部申万行业成份股数据...")
    
    while True:
        params = {
            "l1_code": "",
            "l2_code": "",
            "l3_code": "",
            "is_new": "",
            "ts_code": "",
            "src": "",
            "limit": limit,
            "offset": offset
        }

        try:
            df_chunk = pro.index_member_all(**params, fields=[
                "l1_code",
                "l1_name", 
                "l2_code",
                "l2_name",
                "l3_code",
                "l3_name",
                "ts_code",
                "name",
                "in_date",
                "out_date",
                "is_new"
            ])

            # 若本次没有数据，则跳出循环
            if df_chunk.empty:
                print("没有更多数据，退出循环")
                break

            dfs.append(df_chunk)
            print(f"获取到 {len(df_chunk)} 条记录，总偏移量: {offset}")

            offset += limit
            
            # 如果返回的数据少于limit，说明已经是最后一页
            if len(df_chunk) < limit:
                print("已获取所有数据")
                break
                
            # 避免offset超过限制
            if offset >= 100000:
                print("达到offset限制，停止获取")
                break
                
        except Exception as e:
            print(f"获取数据时出错: {e}")
            break
    
    return dfs

def save_to_db(df):
    """
    将DataFrame保存到数据库
    """
    if df.empty:
        print("没有数据需要保存")
        return

    # 替换df中的None为数据库可以接受的NULL
    df = df.where(pd.notnull(df), None)
    
    # 将out_date为空字符串的替换为None
    df['out_date'] = df['out_date'].replace({"": None})
    
    # 为保证数据唯一性，采用先删除后插入的策略
    # 根据唯一键 (l3_code, ts_code, in_date)
    engine = create_engine(DB_URL)
    with engine.connect() as conn:
        trans = conn.begin()
        try:
            # 分块处理，防止SQL语句过长
            chunk_size = 500 
            for i in range(0, len(df), chunk_size):
                chunk = df[i:i + chunk_size]
                
                # 构造唯一键元组用于删除查询
                keys_to_delete = [
                    f"('{row.l3_code}', '{row.ts_code}', '{row.in_date}')" 
                    for row in chunk.itertuples()
                ]
                
                if keys_to_delete:
                    # 先删除已存在的记录
                    delete_sql = f"""
                    DELETE FROM sw_member 
                    WHERE (l3_code, ts_code, in_date) IN ({','.join(keys_to_delete)})
                    """
                    conn.execute(text(delete_sql))

                # 插入新数据
                chunk.to_sql('sw_member', con=conn, if_exists='append', index=False)
            
            trans.commit()
            print(f"成功向数据库同步 {len(df)} 条记录")

        except Exception as e:
            trans.rollback()
            print(f"数据保存到数据库时出错: {e}")


# 执行数据获取
dfs = get_sw_member_data()

# 合并全部数据
df = pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()

# 去除重复数据（如果有的话）
if not df.empty:
    df = df.drop_duplicates()
    df = df.sort_values(['l1_code', 'l2_code', 'l3_code', 'ts_code'])

# 数据清洗和保存
if not df.empty:
    save_to_db(df)
else:
    print("未能获取到任何数据")
