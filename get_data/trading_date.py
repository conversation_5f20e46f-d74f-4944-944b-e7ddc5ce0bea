"""
@Author: JiangXin
@Date: 2024/12/10 11:40
@Description: 获取交易日历数据并存入数据库
"""

import time
import pandas as pd
# 导入tushare
import tushare as ts
from sqlalchemy import create_engine, inspect

from db_config import DB_URL, TUSHARE_TOKEN


def get_trading_date():
    try:
        #初始化pro接口
        pro = ts.pro_api(TUSHARE_TOKEN)

        # 拉取数据
        df = pro.trade_cal(**{
            "exchange": "",
            "cal_date": "",
            "start_date": "",
            "end_date": "",
            "is_open": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "exchange",
            "cal_date",
            "is_open",
            "pretrade_date"
        ])

        # 判断数据是否为空
        if df.empty:
            print(f"没有交易日历数据！！")
            return None
        else:
            # 创建数据库连接
            engine = create_engine(DB_URL)

            # 判断数据库是否存在，如果不存在则返回False
            inspector = inspect(engine)
            if 'trading_calendar' in inspector.get_table_names():
                # 获取已存在的数据
                existing_data = pd.read_sql('SELECT exchange, cal_date FROM trading_calendar', engine)
                
                # 创建复合键以进行比较
                df['composite_key'] = df['exchange'] + '-' + df['cal_date']
                existing_data['composite_key'] = existing_data['exchange'] + '-' + existing_data['cal_date']
                
                # 找出需要新增的记录（在新数据中存在但在旧数据中不存在的记录）
                to_insert = df[~df['composite_key'].isin(existing_data['composite_key'])]
                
                # 删除临时列
                to_insert = to_insert.drop('composite_key', axis=1)
                
                if not to_insert.empty:
                    # 只插入新记录
                    to_insert.to_sql('trading_calendar', engine, index=False, if_exists='append', chunksize=5000)
                    print(f"新增了 {len(to_insert)} 条交易日历数据")
                else:
                    print("没有新的交易日历数据需要更新")
            else:
                print(f"trading_calendar表不存在，请先创建表")
                return {'msg': '更新失败','success': False}

    except Exception as e:
        print("Error: ", e)

if __name__ == '__main__':
    get_trading_date()