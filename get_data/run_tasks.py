"""
@Author: Jiang<PERSON>in
@Description: 运行数据获取任务
"""
import os
import time
import subprocess
from datetime import datetime

# 配置要执行的脚本列表及其超时时间（秒）
TASKS_TO_RUN = [
    {"script": "stock_info.py", "timeout": 300},            # 股票基本信息，5分钟超时
    #{"script": "margin_trading_data.py", "timeout": 3600},  # 融资融券行情，目前没啥作用，暂停拉取
    {"script": "money_flow.py", "timeout": 3600},           # 获取个股资金流数据，1小时超时
    #{"script": "No_adj_price_daily.py", "timeout": 3600},   # 除权后行情，1小时超时
    {"script": "daily_basic.py", "timeout": 1800},          # 每日基本面数据，30分钟超时
    {"script": "stk_factor.py", "timeout": 1800},           # 获取个股后复权的数据，30分钟超时
    #{"script": "cost.py", "timeout": 1800},                 # 获取筹码数据，目前没啥作用，暂停拉取
    #{"script": "trading_date.py", "timeout": 300},         # 交易日历，
    # {"script": "ths_daily.py", "timeout": 1800},              #获取同花顺概念及板块每日行情
    # {"script": "ths_index.py", "timeout": 1800},              #同花顺概念板块信息
    {"script": "/Users/<USER>/Desktop/Quant_trade/light_sys_0.1/get_data/sw_daily.py", "timeout": 1800},      #申万行业日行情
    {"script": "/Users/<USER>/Desktop/Quant_trade/light_sys_0.1/get_data/sw_member.py", "timeout": 1800},     #申万行业成分股
    # 计算ATR
    {"script": "/Users/<USER>/Desktop/Quant_trade/light_sys_0.1/calculate_factors/atr/atr_calculate_incremental_mp_fixed.py", "timeout": 7200},
    # 计算ADX
    {"script":"/Users/<USER>/Desktop/Quant_trade/light_sys_0.1/calculate_factors/adx/adx_incremental_calculator.py","timeout":7200},
    {"script": "/Users/<USER>/Desktop/Quant_trade/light_sys_0.1/calculate_factors/output_analysis/money.py", "timeout": 1800}   #计算主力资金
]

def log_message(message, is_error=False):
    """记录日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_type = "ERROR" if is_error else "INFO"
    print(f"[{timestamp}] {log_type}: {message}")

def run_script(script_path, timeout):
    """运行单个脚本，带有超时和错误处理"""
    start_time = time.time()
    try:
        # 对于ATR计算任务，使用更简单的方式避免缓冲问题
        if "atr_calculate_incremental_mp_fixed.py" in script_path:
            log_message("开始执行ATR计算任务（无输出缓冲模式）...")
            process = subprocess.Popen(
                ["python", "-u", script_path],  # -u 参数禁用输出缓冲
                stdout=None,  # 直接输出到控制台
                stderr=None,
                universal_newlines=True
            )
            
            # 等待进程完成或超时
            while True:
                return_code = process.poll()
                if return_code is not None:
                    elapsed_time = time.time() - start_time
                    if return_code == 0:
                        log_message(f"ATR任务完成，耗时: {elapsed_time:.2f}秒")
                        return True
                    else:
                        log_message(f"ATR任务失败，返回码: {return_code}", True)
                        return False
                
                # 检查是否超时
                if time.time() - start_time > timeout:
                    process.terminate()  # 使用terminate而不是kill，更温和
                    time.sleep(5)  # 给进程5秒时间清理
                    if process.poll() is None:
                        process.kill()  # 如果还没结束，强制kill
                    log_message(f"ATR任务超时（{timeout}秒），强制终止", True)
                    return False
                
                # 每5秒检查一次状态
                time.sleep(5)
        else:
            # 其他任务使用原来的方式
            process = subprocess.Popen(
                ["python", script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 等待进程完成或超时
            while True:
                return_code = process.poll()
                if return_code is not None:
                    if return_code == 0:
                        elapsed_time = time.time() - start_time
                        log_message(f"任务完成，耗时: {elapsed_time:.2f}秒")
                        return True
                    else:
                        _, stderr = process.communicate()
                        log_message(f"任务失败，错误信息: {stderr}", True)
                        return False
                
                # 检查是否超时
                if time.time() - start_time > timeout:
                    process.kill()
                    log_message(f"任务超时（{timeout}秒），强制终止", True)
                    return False
                
                # 读取并打印实时输出
                output = process.stdout.readline()
                if output:
                    print(output.strip())
            
    except Exception as e:
        log_message(f"执行出错: {str(e)}", True)
        return False

def main():
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    log_message("开始执行数据更新任务...")
    
    # 记录总体执行结果
    results = []
    
    for task in TASKS_TO_RUN:
        script = task["script"]
        timeout = task["timeout"]
        
        log_message(f"\n开始执行任务: {script}")
        
        # 如果是绝对路径，直接使用；如果是相对路径，拼接到当前目录
        if os.path.isabs(script):
            script_path = script
        else:
            script_path = os.path.join(current_dir, script)
        
        if not os.path.exists(script_path):
            log_message(f"脚本文件不存在: {script_path}", True)
            continue
        
        success = run_script(script_path, timeout)
        results.append({
            "script": script,
            "success": success,
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    
    # 打印执行总结
    log_message("\n=== 执行总结 ===")
    for result in results:
        status = "成功" if result["success"] else "失败"
        log_message(f"{result['script']}: {status} ({result['time']})")

if __name__ == "__main__":
    main()
