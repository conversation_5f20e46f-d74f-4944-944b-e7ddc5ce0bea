"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/20 18:23
@File: ths_daily_first.py
@Version: 1.0
@Description: 同花顺板块行情概念行情数据
"""

# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text  # 导入 text
import time  # 导入 time 模块
from datetime import datetime, timedelta # 新增导入
from db_config import TUSHARE_TOKEN, DB_CONFIG, DB_URL, get_engine

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)

# 将获取到的股票代码遍历调用pro.income接口获取收入报告
def save_ths_daily(loop_start_date_str="20240101", loop_end_date_str="20250520"): # 新函数签名
    try:
        engine = get_engine()
        api_limit_per_call = 3000  # 每次API调用获取的记录上限（用于分页）

        current_date = datetime.strptime(loop_start_date_str, "%Y%m%d")
        final_date = datetime.strptime(loop_end_date_str, "%Y%m%d")

        while current_date <= final_date:
            day_str_for_api = current_date.strftime("%Y%m%d")
            print(f"开始处理日期: {day_str_for_api}")
            
            current_day_offset = 0
            total_records_for_day = 0

            while True: # 内层循环，处理当天的分页数据
                print(f"  正在获取日期 {day_str_for_api} 的数据, offset: {current_day_offset}, limit: {api_limit_per_call}")

                df = pro.ths_daily(**{
                    "ts_code": "",  # 获取所有概念/板块的行情数据
                    "trade_date": day_str_for_api, # 使用 trade_date 指定单日获取
                    "limit": str(api_limit_per_call),
                    "offset": str(current_day_offset) # 使用 offset 进行分页
                }, fields=[
                    "ts_code",
                    "trade_date",
                    "open",
                    "high",
                    "low",
                    "close",
                    "pre_close",
                    "avg_price",
                    "change",
                    "pct_change",
                    "vol",
                    "turnover_rate",
                    "total_mv",
                    "float_mv",
                    "pe_ttm",
                    "pb_mrq"
                ])

                if not df.empty:
                    num_records_fetched = len(df)
                    total_records_for_day += num_records_fetched
                    print(f"  日期 {day_str_for_api}, offset {current_day_offset}: 获取到 {num_records_fetched} 条数据。")
                    
                    try:
                        #保存数据
                        df.to_sql('ths_daily', engine, if_exists='append', index=False, chunksize=3000)
                        print(f"  成功将 {num_records_fetched} 条记录 (日期 {day_str_for_api}, offset {current_day_offset}) 保存到数据库。")
                    except Exception as e:
                        print(f"  保存日期 {day_str_for_api}, offset {current_day_offset} 的数据到数据库时发生错误: {e}")
                        # 如果单次保存失败，可以选择跳过这批数据并尝试获取下一页，或中断当天的处理
                        # 这里选择中断当天的处理，防止无限循环或数据不一致
                        break # 跳出内层循环，处理下一天
                    
                    current_day_offset += num_records_fetched # 更新offset以便获取下一批数据
                    
                    # 如果获取到的数据少于 limit，通常意味着这是当前日期的最后一页了。
                    if num_records_fetched < api_limit_per_call:
                        print(f"  日期 {day_str_for_api}: 获取到的数据量 {num_records_fetched} 小于限制 {api_limit_per_call}，此日期数据已全部获取。")
                        break # 跳出内层循环，此日期处理完毕
                else:
                    print(f"  日期 {day_str_for_api}, offset {current_day_offset}: 未获取到更多数据。此日期处理完毕。")
                    break # 跳出内层循环，此日期处理完毕
                
                time.sleep(0.2) # API 分页调用间歇，可以适当调整

            print(f"日期 {day_str_for_api} 处理完成，共获取 {total_records_for_day} 条记录。")
            current_date += timedelta(days=1) # 处理下一天
            time.sleep(0.5) # 不同日期 API 调用间歇，保持友好

        print(f"数据获取完成：已处理从 {loop_start_date_str} 到 {loop_end_date_str} 的日期。")

    except ValueError as ve:
        print(f"日期格式错误: {ve}. 请确保日期为 YYYYMMDD 格式。")
    except Exception as e:
        error_message = f"处理过程中发生未预料的错误: {e}"
        print(error_message)
        # return False # 根据需要决定是否返回值

if __name__ == "__main__":
    # target_start_date = "20240101"
    # print(f"开始从 {target_start_date} 获取同花顺板块概念行情数据...")
    # save_ths_daily(initial_start_date=target_start_date)
    # print("数据获取任务结束。")
    
    # 新的主执行逻辑
    start_fetch_date = "20240101"
    end_fetch_date = "20250520" # 用户指定的结束日期
    print(f"开始从 {start_fetch_date} 至 {end_fetch_date} 逐日获取同花顺板块/概念行情数据...")
    save_ths_daily(loop_start_date_str=start_fetch_date, loop_end_date_str=end_fetch_date)
    print("数据获取任务结束。")