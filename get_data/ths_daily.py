"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/20 18:17
@File: ths_daily.py
@Version: 1.0
@Description: 用于首次获取同花顺概念及板块每日行情，后续通过 ths_daily.py模块获取
"""

import time
import pandas as pd
# 导入tushare
import tushare as ts
from PIL.ImageChops import offset
from sqlalchemy import create_engine, inspect

from db_config import DB_URL, TUSHARE_TOKEN
from get_data.db_config import get_engine


# 循环更新数据，指定一个开始时间及结束时间，trade_date在这里循环
def get_ths_daily_period(start_date, end_date):
    for trade_date in pd.date_range(start=start_date, end=end_date).strftime("%Y%m%d").tolist():
        print(f"开始处理----{trade_date}----同花顺概念及板块股票数据")
        time.sleep(0.2)
        get_ths_daily(trade_date)


def get_ths_daily(trade_date):
    try:
        # 初始化pro接口
        pro = ts.pro_api(TUSHARE_TOKEN)
        engine = get_engine()

        # 拉取数据
        df = pro.ths_daily(**{
            "ts_code": "",  # 获取所有概念/板块的行情数据
            "trade_date": trade_date,  # 使用 trade_date 指定单日获取
            "limit": "3000",  #正常就1200多条，3000没有问题
            "offset": "0"  # 使用 offset 进行分页
        }, fields=[
            "ts_code",
            "trade_date",
            "open",
            "high",
            "low",
            "close",
            "pre_close",
            "avg_price",
            "change",
            "pct_change",
            "vol",
            "turnover_rate",
            "total_mv",
            "float_mv",
            "pe_ttm",
            "pb_mrq"
        ])

        if not df.empty:
            try:
                # 保存数据
                df.to_sql('ths_daily', engine, if_exists='append', index=False, chunksize=3000)
                print(f"成功将 {len(df)} 条记录 (日期 {trade_date}) 保存到数据库。")
            except Exception as e:
                print(f"  保存日期 {trade_date}的数据到数据库时发生错误: {e}")
                # 如果单次保存失败，可以选择跳过这批数据并尝试获取下一页，或中断当天的处理
                # 这里选择中断当天的处理，防止无限循环或数据不一致
                return None

            time.sleep(0.2)  # API 分页调用间歇，可以适当调整
            return None
        return True

    except Exception as e:
        print("Error: ", e)
        return None


# 获取ths_daily数据库中最新的交易日期
def get_last_trade_date():
    try:
        # 创建数据库连接
        engine = create_engine(DB_URL)

        # 查询最新交易日期
        sql = "SELECT MAX(trade_date) as last_trade_date FROM ths_daily"
        df = pd.read_sql(sql, engine)

        # 获取结果
        last_trade_date = df['last_trade_date'].iloc[0]

        return last_trade_date

    except Exception as e:
        print("Error getting last trade date: ", e)
        return None


if __name__ == '__main__':
    last_date = get_last_trade_date()
    if last_date is not None:
        # 获取最新交易日期的后一天
        start_date = (last_date + pd.Timedelta(days=1)).strftime('%Y%m%d')
        # end_date 为当前的时间
        end_date = pd.Timestamp.now().strftime('%Y%m%d')
        get_ths_daily_period(start_date, end_date)
    else:
        print("获取最新交易日期失败")