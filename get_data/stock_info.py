import sys
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text
from db_config import DB_URL, TUSHARE_TOKEN

# 初始化Tushare
pro = ts.pro_api(TUSHARE_TOKEN)

def get_stock_basic():
    # 获取股票数据
    df = pro.stock_basic(fields=[
        "ts_code", "symbol", "name", "area", "industry", "fullname",
        "enname", "cnspell", "market", "exchange", "curr_type",
        "list_status", "list_date", "delist_date", "is_hs",
        "act_name", "act_ent_type"
    ])

    # 创建数据库连接
    engine = create_engine(DB_URL)

    # 获取数据库中已存在的数据
    existing_data = pd.read_sql('SELECT * FROM stock_basic', engine)

    # 找出需要新增的行（在新数据中存在但在旧数据中不存在的记录）
    to_insert = df[~df['ts_code'].isin(existing_data['ts_code'])]

    # 找出需要更新的行（在新数据和旧数据中都存在的记录）
    existing_ts_codes = df['ts_code'].isin(existing_data['ts_code'])
    to_update = df[existing_ts_codes]

    # 使用事务管理更新和插入操作
    with engine.connect() as connection:
        with connection.begin():  # 开启事务
            # 更新已存在的数据
            if not to_update.empty:
                # 批量删除旧数据
                ts_codes_to_delete = tuple(to_update['ts_code'].tolist())
                delete_sql = text(f"DELETE FROM stock_basic WHERE ts_code IN {ts_codes_to_delete}")
                connection.execute(delete_sql)
                # 插入新数据
                to_update.to_sql('stock_basic', connection, if_exists='append', index=False)
                print(f"更新了 {len(to_update)} 条数据")

            # 插入新数据
            if not to_insert.empty:
                to_insert.to_sql('stock_basic', connection, if_exists='append', index=False)
                print(f"新增了 {len(to_insert)} 条数据")

if __name__ == '__main__':
    get_stock_basic()