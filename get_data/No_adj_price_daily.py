"""
@Author: JiangXin
@Date: 2024/12/10 14:11
@Description: 获取除权后的个股价格信息
"""

import time
import pandas as pd
# 导入tushare
import tushare as ts
from sqlalchemy import create_engine, inspect

from db_config import DB_URL, TUSHARE_TOKEN

#循环更新数据，指定一个开始时间及结束时间，trade_date在这里循环
def get_no_rights_price_daily_with_period(start_date, end_date):
    for trade_date in pd.date_range(start=start_date, end=end_date).strftime("%Y%m%d").tolist():
        print(f"开始处理----{trade_date}----除权后股票数据")
        time.sleep(0.2)
        get_no_rights_price_daily(trade_date)

def get_no_rights_price_daily(trade_date):
    try:
        #初始化pro接口
        pro = ts.pro_api(TUSHARE_TOKEN)

        # 拉取数据
        df = pro.daily(**{
            "ts_code": "",
            "trade_date": trade_date,
            "start_date": "",
            "end_date": "",
            "offset": "",
            "limit": ""
        }, fields=[
            "ts_code",
            "trade_date",
            "open",
            "high",
            "low",
            "close",
            "pre_close",
            "change",
            "pct_chg",
            "vol",
            "amount"
        ])

        # 判断数据是否为空
        if df.empty:
            print(f"{trade_date} 没有返回数据，结束！")
            return None
        else:
            # 创建数据库连接
            engine = create_engine(DB_URL)

            # 判断数据库是否存在，如果不存在则返回False
            inspector = inspect(engine)
            if 'no_rights_price_daily' in inspector.get_table_names():
                df.to_sql('no_rights_price_daily', engine, index=False, if_exists='append',chunksize=5000)
                # return {'msg': '更新成功','总数量': len(df),'data': df}
            else:
                print(f"{trade_date} 的no_rights_price_daily处理失败，请检查")
                return {'msg': '更新失败','success': False}

    except Exception as e:
        print("Error: ", e)

#获取no_rights_price_daily数据库中最新的交易日期
def get_last_trade_date():
    try:
        # 创建数据库连接
        engine = create_engine(DB_URL)
        
        # 查询最新交易日期
        sql = "SELECT MAX(trade_date) as last_trade_date FROM no_rights_price_daily"
        df = pd.read_sql(sql, engine)
        
        # 获取结果
        last_trade_date = df['last_trade_date'].iloc[0]
        
        return last_trade_date

    except Exception as e:
        print("Error getting last trade date: ", e)
        return None

if __name__ == '__main__':
    last_date = get_last_trade_date()
    if last_date is not None:
        # 获取最新交易日期的后一天
        start_date = (last_date + pd.Timedelta(days=1)).strftime('%Y%m%d')
        # end_date 为当前的时间
        end_date = pd.Timestamp.now().strftime('%Y%m%d')
        get_no_rights_price_daily_with_period(start_date, end_date)
    else:
        print("获取最新交易日期失败")