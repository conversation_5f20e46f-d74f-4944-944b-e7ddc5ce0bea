"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/16 14:19
@File:
@Version: 1.0
@Description:获取现金流量表
"""

# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text  # 导入 text
import time  # 导入 time 模块
from db_config import TUSHARE_TOKEN, DB_CONFIG, DB_URL, get_engine

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)


# 从stock_basic表中获取股票代码，用于遍历获取收入报告
def get_stock_list():
    try:
        # 创建数据库连接
        engine = get_engine()

        # 查询所有股票代码
        sql = "SELECT ts_code FROM stock_basic"
        df = pd.read_sql(sql, engine)

        if df is not None:
            # 获取结果
            stock_list = df['ts_code']
        else:
            raise Exception("获取股票代码失败")

        return stock_list

    except Exception as e:
        print("Error getting stock list: ", e)
        return None


# 将获取到的股票代码遍历调用pro.income接口获取流量报告
def save_cashflow_report(stock_list):
    stock = None  # 初始化 stock
    processed_count = 0  # processed_count 已在外部初始化
    try:
        total_stocks = len(stock_list)
        # 注意：请确保 db_config.py 中的 get_engine 是一个函数，而不是一个 Engine 实例。
        # 如果 get_engine() 调用持续出现 TypeError，问题很可能在 db_config.py 或此文件其他地方对 get_engine 的错误赋值。
        engine = get_engine()
        for current_stock_data in stock_list:  # 使用新变量名以避免混淆
            stock = current_stock_data  # 在循环内为 stock 赋值
            processed_count += 1
            print(f"正在处理: {stock} ({processed_count}/{total_stocks})")
            df = pro.cashflow(**{
                "ts_code": stock,
                "ann_date": "",
                "f_ann_date": "",
                "start_date": "20150331",
                "end_date": "",
                "period": "",
                "report_type": "",
                "comp_type": "",
                "is_calc": "",
                "limit": "",
                "offset": ""
            }, fields=[
                "ts_code",
                "ann_date",
                "f_ann_date",
                "end_date",
                "comp_type",
                "report_type",
                "end_type",
                "net_profit",
                "finan_exp",
                "c_fr_sale_sg",
                "recp_tax_rends",
                "n_depos_incr_fi",
                "n_incr_loans_cb",
                "n_inc_borr_oth_fi",
                "prem_fr_orig_contr",
                "n_incr_insured_dep",
                "n_reinsur_prem",
                "n_incr_disp_tfa",
                "ifc_cash_incr",
                "n_incr_disp_faas",
                "n_incr_loans_oth_bank",
                "n_cap_incr_repur",
                "c_fr_oth_operate_a",
                "c_inf_fr_operate_a",
                "c_paid_goods_s",
                "c_paid_to_for_empl",
                "c_paid_for_taxes",
                "n_incr_clt_loan_adv",
                "n_incr_dep_cbob",
                "c_pay_claims_orig_inco",
                "pay_handling_chrg",
                "pay_comm_insur_plcy",
                "oth_cash_pay_oper_act",
                "st_cash_out_act",
                "n_cashflow_act",
                "oth_recp_ral_inv_act",
                "c_disp_withdrwl_invest",
                "c_recp_return_invest",
                "n_recp_disp_fiolta",
                "n_recp_disp_sobu",
                "stot_inflows_inv_act",
                "c_pay_acq_const_fiolta",
                "c_paid_invest",
                "n_disp_subs_oth_biz",
                "oth_pay_ral_inv_act",
                "n_incr_pledge_loan",
                "stot_out_inv_act",
                "n_cashflow_inv_act",
                "c_recp_borrow",
                "proc_issue_bonds",
                "oth_cash_recp_ral_fnc_act",
                "stot_cash_in_fnc_act",
                "free_cashflow",
                "c_prepay_amt_borr",
                "c_pay_dist_dpcp_int_exp",
                "incl_dvd_profit_paid_sc_ms",
                "oth_cashpay_ral_fnc_act",
                "stot_cashout_fnc_act",
                "n_cash_flows_fnc_act",
                "eff_fx_flu_cash",
                "n_incr_cash_cash_equ",
                "c_cash_equ_beg_period",
                "c_cash_equ_end_period",
                "c_recp_cap_contrib",
                "incl_cash_rec_saims",
                "uncon_invest_loss",
                "prov_depr_assets",
                "depr_fa_coga_dpba",
                "amort_intang_assets",
                "lt_amort_deferred_exp",
                "decr_deferred_exp",
                "incr_acc_exp",
                "loss_disp_fiolta",
                "loss_scr_fa",
                "loss_fv_chg",
                "invest_loss",
                "decr_def_inc_tax_assets",
                "incr_def_inc_tax_liab",
                "decr_inventories",
                "decr_oper_payable",
                "incr_oper_payable",
                "others",
                "im_net_cashflow_oper_act",
                "conv_debt_into_cap",
                "conv_copbonds_due_within_1y",
                "fa_fnc_leases",
                "im_n_incr_cash_equ",
                "net_dism_capital_add",
                "net_cash_rece_sec",
                "credit_impa_loss",
                "use_right_asset_dep",
                "oth_loss_asset",
                "end_bal_cash",
                "beg_bal_cash",
                "end_bal_cash_equ",
                "beg_bal_cash_equ",
                "update_flag"
            ])

            # 检查数据是否为空
            if df is None or df.empty:
                print(f"警告: {stock} 没有获取到任何现数据")
                continue  # 跳过当前股票的处理

            # 不再需要根据 update_flag 筛选:
            # df = df[df['update_flag'] == 1]

            time.sleep(0.1)  # 加入延时

            # 保存到数据库表：income中
            if not df.empty:
                try:
                    # 首先删除该股票已有的所有利润表数据
                    with engine.connect() as connection:
                        connection.execute(text(f"DELETE FROM cashflow WHERE ts_code = :ts_code"), {"ts_code": stock})
                        connection.commit()

                    # 然后追加新获取的数据
                    df.to_sql('cashflow', engine, if_exists='append', index=False, chunksize=3000)
                    print(f"成功保存 {stock} 的报告，共{len(df)}条记录")
                except Exception as e:
                    print(f"保存 {stock} 数据到数据库时发生错误: {e}")
            else:
                # 这段理论上不会执行了，因为前面已经continue了df.empty的情况
                print(f"警告: {stock} 没有现金流量数据需要保存，跳过。")

        # 当所有股票都处理完毕后，可以考虑返回一个状态，例如成功处理的数量
        # 如果 stock_list 为空，也会执行到这里
        if total_stocks > 0:
            print(
                f"所有股票处理完毕。成功处理 {processed_count} 只中的 {processed_count} 只（此计数包括获取数据失败的情况）。")
        else:
            print("股票列表为空，未处理任何股票。")
        return True  # 或者根据实际成功保存的数量返回更详细的信息

    except TypeError as te:  # 单独捕获 TypeError，更明确地提示问题所在
        print(f"发生类型错误: {te}。请检查 get_engine 是否为可调用函数。")
        # 此时 stock 可能未定义，不应在错误信息中打印 stock
        return False
    except Exception as e:
        error_message = f"处理过程中发生错误 (在第 {processed_count} 只股票{f': {stock}' if stock else ''}): {e}"
        print(error_message)
        return False


if __name__ == "__main__":
    stock_list = get_stock_list()
    save_cashflow_report(stock_list)