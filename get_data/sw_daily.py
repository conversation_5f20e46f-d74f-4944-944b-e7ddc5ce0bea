"""
@Author: ji<PERSON><PERSON>
@Date: 2025/6/11 16:46
@File: sw_daily.py
@Version: 1.0
@Description: 获取申万行业指数日线行情数据，并增量更新到数据库
"""

"""
以下一级行业数据有日行情：
801030.SI
801040.SI
801050.SI
801710.SI
801720.SI
801730.SI
801890.SI
801740.SI
801880.SI
801110.SI
801130.SI
801140.SI
801200.SI
801010.SI
801120.SI
801210.SI
801150.SI
801160.SI
801170.SI
801180.SI
801080.SI
801750.SI
801760.SI
801770.SI
801780.SI
801790.SI
801230.SI

其他二级、三级，分别有 35%，50% 的行业没有行情数据
"""
# 导入tushare
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time
from sqlalchemy import create_engine, text

# 从项目配置中导入数据库和token信息
from get_data.db_config import DB_URL, TUSHARE_TOKEN

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)


def get_last_trade_date_from_db():
	"""从数据库获取最新的交易日期"""
	engine = create_engine(DB_URL)
	try:
		# 使用inspect检查表是否存在
		inspector = text(f"SHOW TABLES LIKE 'sw_index_daily'")
		with engine.connect() as conn:
			result = conn.execute(inspector).scalar()
			if not result:
				print("表 'sw_index_daily' 不存在，将从头开始获取数据。")
				return None
		
		with engine.connect() as conn:
			query = "SELECT MAX(trade_date) as last_date FROM sw_index_daily"
			result_df = pd.read_sql(query, conn)
			if pd.isna(result_df.iloc[0]['last_date']):
				return None
			return pd.to_datetime(result_df.iloc[0]['last_date'])
	except Exception as e:
		print(f"查询最新交易日期失败: {e}")
		return None


def save_to_db(df, trade_date):
	"""
	将单日的数据保存到数据库，采用先删除后插入的方式
	"""
	if df.empty:
		print(f"日期 {trade_date} 没有数据需要保存。")
		return

	engine = create_engine(DB_URL)
	with engine.connect() as conn:
		trans = conn.begin()
		try:
			# 先删除当天的数据，防止重复
			delete_sql = text("DELETE FROM sw_index_daily WHERE trade_date = :trade_date")
			conn.execute(delete_sql, {"trade_date": trade_date})

			# 插入新数据
			df.to_sql('sw_index_daily', con=conn, if_exists='append', index=False, chunksize=5000)

			trans.commit()
			print(f"成功将 {len(df)} 条记录 (日期 {trade_date}) 同步到数据库。")
		except Exception as e:
			trans.rollback()
			print(f"保存日期 {trade_date} 的数据到数据库时发生错误: {e}")


def get_sw_daily(trade_date):
	"""
	获取指定日期的全量申万行业指数行情数据，并处理分页
	"""
	limit = 3000
	offset = 0
	all_data = []

	print(f"开始获取----{trade_date}----的申万行业日线数据...")

	while True:
		try:
			df_chunk = pro.sw_daily(**{
				"trade_date": trade_date,
				"limit": limit,
				"offset": offset
			}, fields=[
				"ts_code", "trade_date", "name", "open", "low", "high", "close",
				"change", "pct_change", "vol", "amount", "pe", "pb",
				"float_mv", "total_mv", "weight"
			])

			if df_chunk.empty:
				break

			all_data.append(df_chunk)
			offset += len(df_chunk)

			if len(df_chunk) < limit:
				break  # 已经取完所有数据

			time.sleep(0.2)  # 尊重API调用频率

		except Exception as e:
			print(f"获取日期 {trade_date} 数据时出错 (offset: {offset}): {e}")
			return  # 当天数据获取失败，直接返回

	if not all_data:
		print(f"日期 {trade_date} 没有获取到数据。")
		return

	full_df = pd.concat(all_data, ignore_index=True)
	save_to_db(full_df, trade_date)


def get_sw_daily_period(start_date, end_date):
	"""
	循环获取指定时间段内每一天的日线数据
	"""
	trade_cal = pro.trade_cal(start_date=start_date, end_date=end_date)
	trade_dates = trade_cal[trade_cal['is_open'] == 1]['cal_date'].tolist()

	for trade_date in trade_dates:
		get_sw_daily(trade_date)
		time.sleep(0.5)  # 控制每日获取的频率


if __name__ == '__main__':
	# 1. 确定开始日期
	last_date = get_last_trade_date_from_db()
	if last_date is not None:
		start_date = (last_date + timedelta(days=1)).strftime('%Y%m%d')
		print(f"数据库中最新数据日期为: {last_date.strftime('%Y-%m-%d')}, 从 {start_date} 开始增量更新。")
	else:
		# 如果数据库为空，从一个较早的日期开始获取
		start_date = '20200101'
		print("数据库为空或表不存在，从 20200101 开始全量获取。")

	# 2. 确定结束日期
	end_date = datetime.now().strftime('%Y%m%d')

	# 3. 执行数据获取
	if start_date > end_date:
		print("数据已是最新，无需更新。")
	else:
		get_sw_daily_period(start_date, end_date)
