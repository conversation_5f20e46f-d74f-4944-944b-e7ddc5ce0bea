"""
@Author: ji<PERSON><PERSON>
@Date: 2025/5/20 17:23
@File: financial_indicator.py
@Version: 1.0
@Description: 年报主要财务指标
"""

# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text  # 导入 text
import time  # 导入 time 模块
from db_config import TUSHARE_TOKEN, DB_CONFIG, DB_URL, get_engine

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)


# 从stock_basic表中获取股票代码，用于遍历获取收入报告
def get_stock_list():
    try:
        # 创建数据库连接
        engine = get_engine()

        # 查询所有股票代码
        sql = "SELECT ts_code FROM stock_basic"
        df = pd.read_sql(sql, engine)

        if df is not None:
            # 获取结果
            stock_list = df['ts_code']
        else:
            raise Exception("获取股票代码失败")

        return stock_list

    except Exception as e:
        print("Error getting stock list: ", e)
        return None


# 将获取到的股票代码遍历调用pro.income接口获取收入报告
def save_financial_report(stock_list):
    stock = None  # 初始化 stock
    processed_count = 0  # processed_count 已在外部初始化
    try:
        total_stocks = len(stock_list)
        # 注意：请确保 db_config.py 中的 get_engine 是一个函数，而不是一个 Engine 实例。
        # 如果 get_engine() 调用持续出现 TypeError，问题很可能在 db_config.py 或此文件其他地方对 get_engine 的错误赋值。
        engine = get_engine()
        for current_stock_data in stock_list:  # 使用新变量名以避免混淆
            stock = current_stock_data  # 在循环内为 stock 赋值
            processed_count += 1
            print(f"正在处理: {stock} ({processed_count}/{total_stocks})")
            df = pro.fina_indicator(**{
                "ts_code": stock,
                "ann_date": "",
                "start_date": "",
                "end_date": "",
                "period": "",
                "update_flag": "",
                "limit": "",
                "offset": ""
            }, fields=[
                "ts_code",
                "ann_date",
                "end_date",
                "eps",
                "dt_eps",
                "total_revenue_ps",
                "revenue_ps",
                "capital_rese_ps",
                "surplus_rese_ps",
                "undist_profit_ps",
                "extra_item",
                "profit_dedt",
                "gross_margin",
                "current_ratio",
                "quick_ratio",
                "cash_ratio",
                "ar_turn",
                "ca_turn",
                "fa_turn",
                "assets_turn",
                "op_income",
                "ebit",
                "ebitda",
                "fcff",
                "fcfe",
                "current_exint",
                "noncurrent_exint",
                "interestdebt",
                "netdebt",
                "tangible_asset",
                "working_capital",
                "networking_capital",
                "invest_capital",
                "retained_earnings",
                "diluted2_eps",
                "bps",
                "ocfps",
                "retainedps",
                "cfps",
                "ebit_ps",
                "fcff_ps",
                "fcfe_ps",
                "netprofit_margin",
                "grossprofit_margin",
                "cogs_of_sales",
                "expense_of_sales",
                "profit_to_gr",
                "saleexp_to_gr",
                "adminexp_of_gr",
                "finaexp_of_gr",
                "impai_ttm",
                "gc_of_gr",
                "op_of_gr",
                "ebit_of_gr",
                "roe",
                "roe_waa",
                "roe_dt",
                "roa",
                "npta",
                "roic",
                "roe_yearly",
                "roa2_yearly",
                "debt_to_assets",
                "assets_to_eqt",
                "dp_assets_to_eqt",
                "ca_to_assets",
                "nca_to_assets",
                "tbassets_to_totalassets",
                "int_to_talcap",
                "eqt_to_talcapital",
                "currentdebt_to_debt",
                "longdeb_to_debt",
                "ocf_to_shortdebt",
                "debt_to_eqt",
                "eqt_to_debt",
                "eqt_to_interestdebt",
                "tangibleasset_to_debt",
                "tangasset_to_intdebt",
                "tangibleasset_to_netdebt",
                "ocf_to_debt",
                "turn_days",
                "roa_yearly",
                "roa_dp",
                "fixed_assets",
                "profit_to_op",
                "q_saleexp_to_gr",
                "q_gc_to_gr",
                "q_roe",
                "q_dt_roe",
                "q_npta",
                "q_ocf_to_sales",
                "basic_eps_yoy",
                "dt_eps_yoy",
                "cfps_yoy",
                "op_yoy",
                "ebt_yoy",
                "netprofit_yoy",
                "dt_netprofit_yoy",
                "ocf_yoy",
                "roe_yoy",
                "bps_yoy",
                "assets_yoy",
                "eqt_yoy",
                "tr_yoy",
                "or_yoy",
                "q_sales_yoy",
                "q_op_qoq",
                "equity_yoy",
                "invturn_days",
                "arturn_days",
                "inv_turn",
                "valuechange_income",
                "interst_income",
                "daa",
                "roe_avg",
                "opincome_of_ebt",
                "investincome_of_ebt",
                "n_op_profit_of_ebt",
                "tax_to_ebt",
                "dtprofit_to_profit",
                "salescash_to_or",
                "ocf_to_or",
                "ocf_to_opincome",
                "capitalized_to_da",
                "ocf_to_interestdebt",
                "ocf_to_netdebt",
                "ebit_to_interest",
                "longdebt_to_workingcapital",
                "ebitda_to_debt",
                "profit_prefin_exp",
                "non_op_profit",
                "op_to_ebt",
                "nop_to_ebt",
                "ocf_to_profit",
                "cash_to_liqdebt",
                "cash_to_liqdebt_withinterest",
                "op_to_liqdebt",
                "op_to_debt",
                "roic_yearly",
                "total_fa_trun",
                "q_opincome",
                "q_investincome",
                "q_dtprofit",
                "q_eps",
                "q_netprofit_margin",
                "q_gsprofit_margin",
                "q_exp_to_sales",
                "q_profit_to_gr",
                "q_adminexp_to_gr",
                "q_finaexp_to_gr",
                "q_impair_to_gr_ttm",
                "q_op_to_gr",
                "q_opincome_to_ebt",
                "q_investincome_to_ebt",
                "q_dtprofit_to_profit",
                "q_salescash_to_or",
                "q_ocf_to_or",
                "q_gr_yoy",
                "q_gr_qoq",
                "q_sales_qoq",
                "q_op_yoy",
                "q_profit_yoy",
                "q_profit_qoq",
                "q_netprofit_yoy",
                "q_netprofit_qoq",
                "rd_exp",
                "update_flag"
            ])

            # 检查数据是否为空
            if df is None or df.empty:
                print(f"警告: {stock} 没有获取到任何财务指标数据")
                continue  # 跳过当前股票的处理

            # 不再需要根据 update_flag 筛选:
            # df = df[df['update_flag'] == 1]

            time.sleep(0.1)  # 加入延时

            # 保存到数据库表：income中
            if not df.empty:
                try:
                    # 首先删除该股票已有的所有利润表数据
                    with engine.connect() as connection:
                        connection.execute(text(f"DELETE FROM financial_indicator WHERE ts_code = :ts_code"), {"ts_code": stock})
                        connection.commit()

                    # 然后追加新获取的数据
                    df.to_sql('financial_indicator', engine, if_exists='append', index=False, chunksize=3000)
                    print(f"成功保存 {stock} 的收入报告，共{len(df)}条记录")
                except Exception as e:
                    print(f"保存 {stock} 数据到数据库时发生错误: {e}")
            else:
                # 这段理论上不会执行了，因为前面已经continue了df.empty的情况
                print(f"警告: {stock} 没有财务指标数据需要保存，跳过。")

        # 当所有股票都处理完毕后，可以考虑返回一个状态，例如成功处理的数量
        # 如果 stock_list 为空，也会执行到这里
        if total_stocks > 0:
            print(
                f"所有股票处理完毕。成功处理 {processed_count} 只中的 {processed_count} 只（此计数包括获取数据失败的情况）。")
        else:
            print("股票列表为空，未处理任何股票。")
        return True  # 或者根据实际成功保存的数量返回更详细的信息

    except TypeError as te:  # 单独捕获 TypeError，更明确地提示问题所在
        print(f"发生类型错误: {te}。请检查 get_engine 是否为可调用函数。")
        # 此时 stock 可能未定义，不应在错误信息中打印 stock
        return False
    except Exception as e:
        error_message = f"处理过程中发生错误 (在第 {processed_count} 只股票{f': {stock}' if stock else ''}): {e}"
        print(error_message)
        return False


if __name__ == "__main__":
    stock_list = get_stock_list()
    save_financial_report(stock_list)