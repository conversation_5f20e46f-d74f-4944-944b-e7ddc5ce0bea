"""
@Author: ji<PERSON><PERSON>
@Date: 2025/5/16 14:19
@File:
@Version: 1.0
@Description: 
"""

# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text # 导入 text
import time # 导入 time 模块
from db_config import TUSHARE_TOKEN,DB_CONFIG,DB_URL,get_engine

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)


#从stock_basic表中获取股票代码，用于遍历获取收入报告
def get_stock_list():
    try:
        # 创建数据库连接
        engine = get_engine()

        # 查询所有股票代码
        sql = "SELECT ts_code FROM stock_basic"
        df = pd.read_sql(sql, engine)
        
        if df is not None:
            # 获取结果
            stock_list = df['ts_code']
        else:
            raise Exception("获取股票代码失败")
        
        return stock_list

    except Exception as e:
        print("Error getting stock list: ", e)
        return None
    
# 将获取到的股票代码遍历调用pro.income接口获取收入报告
def save_income_report(stock_list):
    stock = None # 初始化 stock
    processed_count = 0 # processed_count 已在外部初始化
    try:
        total_stocks = len(stock_list)
        # 注意：请确保 db_config.py 中的 get_engine 是一个函数，而不是一个 Engine 实例。
        # 如果 get_engine() 调用持续出现 TypeError，问题很可能在 db_config.py 或此文件其他地方对 get_engine 的错误赋值。
        engine = get_engine()
        for current_stock_data in stock_list: # 使用新变量名以避免混淆
            stock = current_stock_data # 在循环内为 stock 赋值
            processed_count += 1
            print(f"正在处理: {stock} ({processed_count}/{total_stocks})")
            df = pro.income(**{
                "ts_code": stock,
                "ann_date": "",
                "f_ann_date": "",
                "start_date": "20150331",
                "end_date": "",
                "period": "",
                "report_type": "",
                "comp_type": "",
                "is_calc": "",
                "limit": "",
                "offset": ""
            }, fields=[
                "ts_code",
                "ann_date",
                "f_ann_date",
                "end_date",
                "report_type",
                "comp_type",
                "end_type",
                "basic_eps",
                "diluted_eps",
                "total_revenue",
                "revenue",
                "int_income",
                "prem_earned",
                "comm_income",
                "n_commis_income",
                "n_oth_income",
                "n_oth_b_income",
                "prem_income",
                "out_prem",
                "une_prem_reser",
                "reins_income",
                "n_sec_tb_income",
                "n_sec_uw_income",
                "n_asset_mg_income",
                "oth_b_income",
                "fv_value_chg_gain",
                "invest_income",
                "ass_invest_income",
                "forex_gain",
                "total_cogs",
                "oper_cost",
                "int_exp",
                "comm_exp",
                "biz_tax_surchg",
                "sell_exp",
                "admin_exp",
                "fin_exp",
                "assets_impair_loss",
                "prem_refund",
                "compens_payout",
                "reser_insur_liab",
                "div_payt",
                "reins_exp",
                "oper_exp",
                "compens_payout_refu",
                "insur_reser_refu",
                "reins_cost_refund",
                "other_bus_cost",
                "operate_profit",
                "non_oper_income",
                "non_oper_exp",
                "nca_disploss",
                "total_profit",
                "income_tax",
                "n_income",
                "n_income_attr_p",
                "minority_gain",
                "oth_compr_income",
                "t_compr_income",
                "compr_inc_attr_p",
                "compr_inc_attr_m_s",
                "ebit",
                "ebitda",
                "insurance_exp",
                "undist_profit",
                "distable_profit",
                "rd_exp",
                "fin_exp_int_exp",
                "fin_exp_int_inc",
                "transfer_surplus_rese",
                "transfer_housing_imprest",
                "transfer_oth",
                "adj_lossgain",
                "withdra_legal_surplus",
                "withdra_legal_pubfund",
                "withdra_biz_devfund",
                "withdra_rese_fund",
                "withdra_oth_ersu",
                "workers_welfare",
                "distr_profit_shrhder",
                "prfshare_payable_dvd",
                "comshare_payable_dvd",
                "capit_comstock_div",
                "net_after_nr_lp_correct",
                "oth_income",
                "asset_disp_income",
                "continued_net_profit",
                "end_net_profit",
                "credit_impa_loss",
                "net_expo_hedging_benefits",
                "oth_impair_loss_assets",
                "total_opcost",
                "amodcost_fin_assets",
                "update_flag"
            ])
            
            # 检查数据是否为空
            if df is None or df.empty:
                print(f"警告: {stock} 没有获取到任何利润表数据")
                continue # 跳过当前股票的处理
                
            # 不再需要根据 update_flag 筛选:
            # df = df[df['update_flag'] == 1] 
            
            time.sleep(0.1) # 加入延时

            #保存到数据库表：income中
            if not df.empty:
                try:
                    # 首先删除该股票已有的所有利润表数据
                    with engine.connect() as connection:
                        connection.execute(text(f"DELETE FROM income WHERE ts_code = :ts_code"), {"ts_code": stock})
                        connection.commit()
                    
                    # 然后追加新获取的数据
                    df.to_sql('income', engine, if_exists='append', index=False, chunksize=3000)
                    print(f"成功保存 {stock} 的收入报告，共{len(df)}条记录")
                except Exception as e:
                    print(f"保存 {stock} 数据到数据库时发生错误: {e}")
            else:
                # 这段理论上不会执行了，因为前面已经continue了df.empty的情况
                print(f"警告: {stock} 没有利润表数据需要保存，跳过。")
        
        # 当所有股票都处理完毕后，可以考虑返回一个状态，例如成功处理的数量
        # 如果 stock_list 为空，也会执行到这里
        if total_stocks > 0:
            print(f"所有股票处理完毕。成功处理 {processed_count} 只中的 {processed_count} 只（此计数包括获取数据失败的情况）。")
        else:
            print("股票列表为空，未处理任何股票。")
        return True # 或者根据实际成功保存的数量返回更详细的信息

    except TypeError as te: # 单独捕获 TypeError，更明确地提示问题所在
        print(f"发生类型错误: {te}。请检查 get_engine 是否为可调用函数。")
        # 此时 stock 可能未定义，不应在错误信息中打印 stock
        return False
    except Exception as e:
        error_message = f"处理过程中发生错误 (在第 {processed_count} 只股票{f': {stock}' if stock else ''}): {e}"
        print(error_message)
        return False
    
if __name__ == "__main__":
    stock_list = get_stock_list()
    save_income_report(stock_list)