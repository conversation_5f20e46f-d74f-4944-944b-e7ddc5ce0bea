
# -*- coding: utf-8 -*-

import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime, timedelta
from typing import List, Optional, Union
from .db_config import DB_URL

class StockDataLoader:
    """股票数据加载器"""
    
    def __init__(self):
        """初始化数据库连接"""
        self.engine = create_engine(DB_URL)

    def get_stock_daily_data(self, 
                           ts_code: str,
                           start_date: str = None,
                           end_date: str = None,
                           fields: List[str] = None) -> pd.DataFrame:
        """
        获取股票日线数据（后复权）
        
        Args:
            ts_code: 股票代码（例如：000001.SZ）
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            fields: 需要获取的字段列表，如果为None则获取所有字段
            
        Returns:
            DataFrame: 包含股票日线数据的DataFrame
        """
        # 构建SQL查询
        if fields is None:
            fields = ['*']
        
        fields_str = ', '.join(fields) if fields != ['*'] else '*'
        
        query = f"""
        SELECT {fields_str}
        FROM no_rights_price_daily
        WHERE ts_code = '{ts_code}'
        """
        
        if start_date:
            query += f" AND trade_date >= '{start_date}'"
        if end_date:
            query += f" AND trade_date <= '{end_date}'"
            
        query += " ORDER BY trade_date"
        
        # 执行查询
        df = pd.read_sql(query, self.engine)
        return df

    def get_stock_factors(self,
                         ts_code: str,
                         start_date: str = None,
                         end_date: str = None,
                         factors: List[str] = None) -> pd.DataFrame:
        """
        获取股票因子数据
        
        Args:
            ts_code: 股票代码（例如：000001.SZ）
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            factors: 需要获取的因子列表，如果为None则获取所有因子
            
        Returns:
            DataFrame: 包含股票因子数据的DataFrame
        """
        # 构建SQL查询
        if factors is None:
            factors = ['*']
        
        factors_str = ', '.join(factors) if factors != ['*'] else '*'
        
        query = f"""
        SELECT {factors_str}
        FROM stk_factor
        WHERE ts_code = '{ts_code}'
        """
        
        if start_date:
            query += f" AND trade_date >= '{start_date}'"
        if end_date:
            query += f" AND trade_date <= '{end_date}'"
            
        query += " ORDER BY trade_date"
        
        # 执行查询
        df = pd.read_sql(query, self.engine)
        return df

    def get_multiple_stocks_data(self,
                               ts_codes: List[str],
                               start_date: str = None,
                               end_date: str = None,
                               fields: List[str] = None) -> pd.DataFrame:
        """
        获取多个股票的日线数据
        
        Args:
            ts_codes: 股票代码列表
            start_date: 开始日期，格式：YYYY-MM-DD
            end_date: 结束日期，格式：YYYY-MM-DD
            fields: 需要获取的字段列表，如果为None则获取所有字段
            
        Returns:
            DataFrame: 包含多个股票日线数据的DataFrame
        """
        # 构建SQL查询
        if fields is None:
            fields = ['*']
        
        fields_str = ', '.join(fields) if fields != ['*'] else '*'
        ts_codes_str = "', '".join(ts_codes)
        
        query = f"""
        SELECT {fields_str}
        FROM no_rights_price_daily
        WHERE ts_code IN ('{ts_codes_str}')
        """
        
        if start_date:
            query += f" AND trade_date >= '{start_date}'"
        if end_date:
            query += f" AND trade_date <= '{end_date}'"
            
        query += " ORDER BY ts_code, trade_date"
        
        # 执行查询
        df = pd.read_sql(query, self.engine)
        return df

# 使用示例
if __name__ == '__main__':
    loader = StockDataLoader()
    
    # 获取单个股票的日线数据
    df_daily = loader.get_stock_daily_data(
        ts_code='000001.SZ',
        start_date='2023-01-01',
        end_date='2023-12-31',
        fields=['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol']
    )
    print("日线数据示例：")
    print(df_daily.head())
    
    # 获取因子数据
    df_factors = loader.get_stock_factors(
        ts_code='000001.SZ',
        start_date='2023-01-01',
        end_date='2023-12-31',
        factors=['ts_code', 'trade_date', 'macd', 'kdj_k', 'kdj_d', 'kdj_j']
    )
    print("\n因子数据示例：")
    print(df_factors.head())
