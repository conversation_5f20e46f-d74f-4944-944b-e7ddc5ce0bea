"""
@Author: ji<PERSON><PERSON>
@Date: 2025/5/16 14:19
@File:
@Version: 1.0
@Description:获取现金流量表
"""

# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text  # 导入 text
import time  # 导入 time 模块
from db_config import TUSHARE_TOKEN, DB_CONFIG, DB_URL, get_engine

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)


# 从stock_basic表中获取股票代码，用于遍历获取收入报告
def get_stock_list():
    try:
        # 创建数据库连接
        engine = get_engine()

        # 查询所有股票代码
        sql = "SELECT ts_code FROM stock_basic"
        df = pd.read_sql(sql, engine)

        if df is not None:
            # 获取结果
            stock_list = df['ts_code']
        else:
            raise Exception("获取股票代码失败")

        return stock_list

    except Exception as e:
        print("Error getting stock list: ", e)
        return None


# 将获取到的股票代码遍历调用pro.income接口获取流量报告
def save_balance_sheet_report(stock_list):
    stock = None  # 初始化 stock
    processed_count = 0  # processed_count 已在外部初始化
    try:
        total_stocks = len(stock_list)
        # 注意：请确保 db_config.py 中的 get_engine 是一个函数，而不是一个 Engine 实例。
        # 如果 get_engine() 调用持续出现 TypeError，问题很可能在 db_config.py 或此文件其他地方对 get_engine 的错误赋值。
        engine = get_engine()
        for current_stock_data in stock_list:  # 使用新变量名以避免混淆
            stock = current_stock_data  # 在循环内为 stock 赋值
            processed_count += 1
            print(f"正在处理: {stock} ({processed_count}/{total_stocks})")
            df = pro.balancesheet(**{
                "ts_code": stock,
                "ann_date": "",
                "f_ann_date": "",
                "start_date": "20150331",
                "end_date": "",
                "period": "",
                "report_type": "",
                "comp_type": "",
                "limit": "",
                "offset": ""
            }, fields=[
                "ts_code",
                "ann_date",
                "f_ann_date",
                "end_date",
                "report_type",
                "comp_type",
                "end_type",
                "total_share",
                "cap_rese",
                "undistr_porfit",
                "surplus_rese",
                "special_rese",
                "money_cap",
                "trad_asset",
                "notes_receiv",
                "accounts_receiv",
                "oth_receiv",
                "prepayment",
                "div_receiv",
                "int_receiv",
                "inventories",
                "amor_exp",
                "nca_within_1y",
                "sett_rsrv",
                "loanto_oth_bank_fi",
                "premium_receiv",
                "reinsur_receiv",
                "reinsur_res_receiv",
                "pur_resale_fa",
                "oth_cur_assets",
                "total_cur_assets",
                "fa_avail_for_sale",
                "htm_invest",
                "lt_eqt_invest",
                "invest_real_estate",
                "time_deposits",
                "oth_assets",
                "lt_rec",
                "fix_assets",
                "cip",
                "const_materials",
                "fixed_assets_disp",
                "produc_bio_assets",
                "oil_and_gas_assets",
                "intan_assets",
                "r_and_d",
                "goodwill",
                "lt_amor_exp",
                "defer_tax_assets",
                "decr_in_disbur",
                "oth_nca",
                "total_nca",
                "cash_reser_cb",
                "depos_in_oth_bfi",
                "prec_metals",
                "deriv_assets",
                "rr_reins_une_prem",
                "rr_reins_outstd_cla",
                "rr_reins_lins_liab",
                "rr_reins_lthins_liab",
                "refund_depos",
                "ph_pledge_loans",
                "refund_cap_depos",
                "indep_acct_assets",
                "client_depos",
                "client_prov",
                "transac_seat_fee",
                "invest_as_receiv",
                "total_assets",
                "lt_borr",
                "st_borr",
                "cb_borr",
                "depos_ib_deposits",
                "loan_oth_bank",
                "trading_fl",
                "notes_payable",
                "acct_payable",
                "adv_receipts",
                "sold_for_repur_fa",
                "comm_payable",
                "payroll_payable",
                "taxes_payable",
                "int_payable",
                "div_payable",
                "oth_payable",
                "acc_exp",
                "deferred_inc",
                "st_bonds_payable",
                "payable_to_reinsurer",
                "rsrv_insur_cont",
                "acting_trading_sec",
                "acting_uw_sec",
                "non_cur_liab_due_1y",
                "oth_cur_liab",
                "total_cur_liab",
                "bond_payable",
                "lt_payable",
                "specific_payables",
                "estimated_liab",
                "defer_tax_liab",
                "defer_inc_non_cur_liab",
                "oth_ncl",
                "total_ncl",
                "depos_oth_bfi",
                "deriv_liab",
                "depos",
                "agency_bus_liab",
                "oth_liab",
                "prem_receiv_adva",
                "depos_received",
                "ph_invest",
                "reser_une_prem",
                "reser_outstd_claims",
                "reser_lins_liab",
                "reser_lthins_liab",
                "indept_acc_liab",
                "pledge_borr",
                "indem_payable",
                "policy_div_payable",
                "total_liab",
                "treasury_share",
                "ordin_risk_reser",
                "forex_differ",
                "invest_loss_unconf",
                "minority_int",
                "total_hldr_eqy_exc_min_int",
                "total_hldr_eqy_inc_min_int",
                "total_liab_hldr_eqy",
                "lt_payroll_payable",
                "oth_comp_income",
                "oth_eqt_tools",
                "oth_eqt_tools_p_shr",
                "lending_funds",
                "acc_receivable",
                "st_fin_payable",
                "payables",
                "hfs_assets",
                "hfs_sales",
                "cost_fin_assets",
                "fair_value_fin_assets",
                "contract_assets",
                "contract_liab",
                "accounts_receiv_bill",
                "accounts_pay",
                "oth_rcv_total",
                "fix_assets_total",
                "cip_total",
                "oth_pay_total",
                "long_pay_total",
                "debt_invest",
                "oth_debt_invest",
                "oth_eq_invest",
                "oth_illiq_fin_assets",
                "oth_eq_ppbond",
                "receiv_financing",
                "use_right_assets",
                "lease_liab",
                "update_flag"
            ])

            # 检查数据是否为空
            if df is None or df.empty:
                print(f"警告: {stock} 没有获取到任何资产负债表数据")
                continue  # 跳过当前股票的处理

            # 不再需要根据 update_flag 筛选:
            # df = df[df['update_flag'] == 1]

            time.sleep(0.1)  # 加入延时

            # 保存到数据库表：income中
            if not df.empty:
                try:
                    # 首先删除该股票已有的所有利润表数据
                    with engine.connect() as connection:
                        connection.execute(text(f"DELETE FROM balance_sheet WHERE ts_code = :ts_code"), {"ts_code": stock})
                        connection.commit()

                    # 然后追加新获取的数据
                    df.to_sql('balance_sheet', engine, if_exists='append', index=False, chunksize=3000)
                    print(f"成功保存 {stock} 的报告，共{len(df)}条记录")
                except Exception as e:
                    print(f"保存 {stock} 数据到数据库时发生错误: {e}")
            else:
                # 这段理论上不会执行了，因为前面已经continue了df.empty的情况
                print(f"警告: {stock} 没有资产负债表数据需要保存，跳过。")

        # 当所有股票都处理完毕后，可以考虑返回一个状态，例如成功处理的数量
        # 如果 stock_list 为空，也会执行到这里
        if total_stocks > 0:
            print(
                f"所有股票处理完毕。成功处理 {processed_count} 只中的 {processed_count} 只（此计数包括获取数据失败的情况）。")
        else:
            print("股票列表为空，未处理任何股票。")
        return True  # 或者根据实际成功保存的数量返回更详细的信息

    except TypeError as te:  # 单独捕获 TypeError，更明确地提示问题所在
        print(f"发生类型错误: {te}。请检查 get_engine 是否为可调用函数。")
        # 此时 stock 可能未定义，不应在错误信息中打印 stock
        return False
    except Exception as e:
        error_message = f"处理过程中发生错误 (在第 {processed_count} 只股票{f': {stock}' if stock else ''}): {e}"
        print(error_message)
        return False


if __name__ == "__main__":
    stock_list = get_stock_list()
    save_balance_sheet_report(stock_list)