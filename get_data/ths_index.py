"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/20 17:52
@File: ths_index.py
@Version: 1.0
@Description: 同花顺行业概念板块
"""

# 导入tushare
import tushare as ts
import pandas as pd
from sqlalchemy import create_engine, text  # 导入 text
import time  # 导入 time 模块
from datetime import datetime
from db_config import TUSHARE_TOKEN, DB_CONFIG, DB_URL, get_engine

# 初始化pro接口
pro = ts.pro_api(TUSHARE_TOKEN)



# 获取并增量保存同花顺行业概念板块数据
def save_ths_index():

    try:
        print(f"开始获取同花顺行业概念板块数据 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 注意：请确保 db_config.py 中的 get_engine 是一个函数，而不是一个 Engine 实例。
        # 如果 get_engine() 调用持续出现 TypeError，问题很可能在 db_config.py 或此文件其他地方对 get_engine 的错误赋值。
        engine = get_engine()

        # 首先查询数据库中已存在的数据
        try:
            existing_query = text("SELECT ts_code, name, count, exchange, list_date, type FROM ths_index")
            existing_data = pd.read_sql(existing_query, engine)
            existing_codes = existing_data['ts_code'].tolist()
            print(f"数据库中已存在 {len(existing_codes)} 个同花顺板块代码")
        except Exception as e:
            print(f"查询已存在数据时发生错误: {e}")
            print("可能是表不存在，将创建新表并保存所有数据")
            existing_data = pd.DataFrame()
            existing_codes = []

        # 获取最新数据
        print("正在从Tushare获取最新数据...")
        df = pro.ths_index(**{
            "ts_code": "",
            "exchange": "A",
            "type": "",
            "name": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "ts_code",
            "name",
            "count",
            "exchange",
            "list_date",
            "type"
        ])

        # 检查数据是否为空
        if df is None or df.empty:
            print(f"警告: 没有获取到任何同花顺行业及概念板块数据")
            return False

        print(f"从Tushare获取到 {len(df)} 条数据")

        # 数据类型转换，确保list_date为日期类型
        if 'list_date' in df.columns:
            df['list_date'] = pd.to_datetime(df['list_date'], errors='coerce')
        if not existing_data.empty and 'list_date' in existing_data.columns:
            existing_data['list_date'] = pd.to_datetime(existing_data['list_date'], errors='coerce')

        # 分析数据变化
        new_records = 0
        updated_records = 0
        
        if existing_codes:
            # 筛选出新的记录（数据库中不存在的ts_code）
            new_df = df[~df['ts_code'].isin(existing_codes)]
            new_records = len(new_df)
            
            # 检查已存在记录是否有更新
            existing_df = df[df['ts_code'].isin(existing_codes)]
            updated_df = pd.DataFrame()
            
            if not existing_df.empty and not existing_data.empty:
                # 设置索引为ts_code进行比较
                existing_df_indexed = existing_df.set_index('ts_code')
                existing_data_indexed = existing_data.set_index('ts_code')
                
                # 找出有变化的记录
                for ts_code in existing_df_indexed.index:
                    if ts_code in existing_data_indexed.index:
                        # 比较各字段是否有变化
                        old_record = existing_data_indexed.loc[ts_code]
                        new_record = existing_df_indexed.loc[ts_code]
                        
                        # 检查各字段是否有变化（排除NaN比较问题）
                        has_change = False
                        for col in ['name', 'count', 'exchange', 'list_date', 'type']:
                            if col in old_record and col in new_record:
                                old_val = old_record[col]
                                new_val = new_record[col]
                                
                                # 处理NaN比较
                                if pd.isna(old_val) and pd.isna(new_val):
                                    continue
                                elif pd.isna(old_val) or pd.isna(new_val):
                                    has_change = True
                                    break
                                elif old_val != new_val:
                                    has_change = True
                                    break
                        
                        if has_change:
                            updated_df = pd.concat([updated_df, existing_df[existing_df['ts_code'] == ts_code]])
                
                updated_records = len(updated_df)
            
            # 合并需要保存的数据
            if not new_df.empty and not updated_df.empty:
                save_df = pd.concat([new_df, updated_df], ignore_index=True)
            elif not new_df.empty:
                save_df = new_df
            elif not updated_df.empty:
                save_df = updated_df
            else:
                save_df = pd.DataFrame()
                
        else:
            # 数据库为空，保存全部数据
            save_df = df
            new_records = len(df)

        # 保存数据
        if not save_df.empty:
            try:
                if updated_records > 0:
                    # 如果有更新的记录，需要使用replace模式
                    save_df.to_sql('ths_index', engine, if_exists='replace', index=False, chunksize=3000)
                    print(f"成功保存同花顺板块数据:")
                    print(f"  - 新增记录: {new_records} 条")
                    print(f"  - 更新记录: {updated_records} 条")
                    print(f"  - 总计保存: {len(save_df)} 条")
                else:
                    # 只有新增记录，使用append模式
                    save_df.to_sql('ths_index', engine, if_exists='append', index=False, chunksize=3000)
                    print(f"成功保存同花顺板块数据:")
                    print(f"  - 新增记录: {new_records} 条")
                
                # 显示一些保存的数据示例
                if len(save_df) > 0:
                    print("\n保存的数据示例:")
                    print(save_df.head().to_string(index=False))
                
                return True
            except Exception as e:
                print(f"保存数据到数据库时发生错误: {e}")
                return False
        else:
            print("没有新的或更新的同花顺板块数据需要保存")
            return True
            
    except Exception as e:
        error_message = f"处理过程中发生错误: {e}"
        print(error_message)
        return False


if __name__ == "__main__":
    result = save_ths_index()
    if result:
        print(f"\n程序执行完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"\n程序执行失败 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")