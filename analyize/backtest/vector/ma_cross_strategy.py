import pandas as pd
import numpy as np
import vectorbt as vbt
import pymysql
from datetime import datetime, timedelta

class MACrossStrategy:
    def __init__(self, 
                 stock_code,
                 start_date=None,
                 lookback_days=250,
                 short_window=3,
                 long_window=7,
                 volatility_window=15,
                 volatility_threshold=0.10,
                 price_up_days=3,
                 price_up_threshold=0.05,
                 db_config=None):
        """
        初始化策略参数
        :param stock_code: 股票代码
        :param start_date: 开始日期，如果为None则取最近lookback_days的数据
        :param lookback_days: 回看的天数
        :param short_window: 短期均线窗口
        :param long_window: 长期均线窗口
        :param volatility_window: 波动率计算窗口
        :param volatility_threshold: 波动率阈值
        :param price_up_days: 价格上涨考察天数
        :param price_up_threshold: 价格上涨阈值
        :param db_config: 数据库配置
        """
        self.stock_code = stock_code
        self.start_date = start_date
        self.lookback_days = lookback_days
        self.short_window = short_window
        self.long_window = long_window
        self.volatility_window = volatility_window
        self.volatility_threshold = volatility_threshold
        self.price_up_days = price_up_days
        self.price_up_threshold = price_up_threshold
        
        # 默认数据库配置
        self.db_config = db_config or {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'tushare',
            'charset': 'utf8mb4'
        }
        
        self.data = None
        
    def fetch_data(self):
        """从MySQL获取股票数据"""
        if self.start_date is None:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.lookback_days)
            start_date = start_date.strftime('%Y-%m-%d')
        else:
            start_date = self.start_date
            
        print(f"\n开始获取股票数据: {self.stock_code}, 开始日期: {start_date}")
        
        try:
            # 使用SQLAlchemy创建数据库连接
            from sqlalchemy import create_engine
            db_url = f"mysql+pymysql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}/{self.db_config['database']}?charset={self.db_config['charset']}"
            engine = create_engine(db_url)
            
            query = f"""
            SELECT trade_date, close_hfq
            FROM stk_factor
            WHERE ts_code = %s
            AND trade_date >= %s
            ORDER BY trade_date
            """
            
            df = pd.read_sql_query(query, engine, params=(self.stock_code, start_date))
            print(f"查询到 {len(df)} 条数据记录")
            
            if df.empty:
                raise ValueError(f"No data found for stock {self.stock_code} from {start_date}")
            
            # 将trade_date转换为datetime格式并设置为索引
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df.set_index('trade_date', inplace=True)
            print(f"数据样例:\n{df.head()}\n")
            self.data = df
            
        except Exception as e:
            print(f"Error fetching data: {str(e)}")
            raise
            
    def calculate_signals(self):
        """计算交易信号"""
        if self.data is None or self.data.empty:
            raise ValueError("No data available for analysis")
            
        print("\n开始计算交易信号...")
        
        # 计算移动平均线
        short_ma = self.data['close_hfq'].rolling(window=self.short_window).mean()
        long_ma = self.data['close_hfq'].rolling(window=self.long_window).mean()
        
        # 计算波动率
        volatility = self.data['close_hfq'].pct_change().rolling(
            window=self.volatility_window).std()
        
        # 计算价格累计涨幅
        price_change = self.data['close_hfq'].pct_change()
        cumulative_return = (1 + price_change).rolling(
            window=self.price_up_days).apply(lambda x: np.prod(x) - 1)
        
        # 生成交叉信号
        cross_above = (short_ma > long_ma) & (short_ma.shift(1) <= long_ma.shift(1))
        cross_below = (short_ma < long_ma) & (short_ma.shift(1) >= long_ma.shift(1))
        
        # 买入条件：均线金叉 + 波动率条件 + 累计涨幅条件
        buy_signals = (
            cross_above & 
            (volatility <= self.volatility_threshold) & 
            (cumulative_return >= self.price_up_threshold)
        )
        
        # 初始化持仓状态和卖出信号
        position = pd.Series(0, index=self.data.index)
        sell_signals = pd.Series(False, index=self.data.index)
        
        # 遍历时间序列，更新持仓状态和卖出信号
        for i in range(len(self.data)):
            if i == 0:
                continue
                
            # 更新持仓状态
            if buy_signals.iloc[i]:
                position.iloc[i] = 1
            elif position.iloc[i-1] == 1:
                position.iloc[i] = 1
            
            # 只在有持仓时考虑卖出信号
            if position.iloc[i] == 1 and cross_below.iloc[i]:
                sell_signals.iloc[i] = True
                position.iloc[i] = 0
        
        print(f"\n信号统计:")
        print(f"买入信号数量: {buy_signals.sum()}")
        print(f"卖出信号数量: {sell_signals.sum()}")
        
        if buy_signals.sum() > 0:
            print("\n买入信号日期:")
            buy_dates = buy_signals[buy_signals].index
            for date in buy_dates:
                price = self.data.loc[date, 'close_hfq']
                print(f"{date.strftime('%Y-%m-%d')}: 价格 {price:.2f}")
        
        if sell_signals.sum() > 0:
            print("\n卖出信号日期:")
            sell_dates = sell_signals[sell_signals].index
            for date in sell_dates:
                price = self.data.loc[date, 'close_hfq']
                print(f"{date.strftime('%Y-%m-%d')}: 价格 {price:.2f}")
        
        # 打印一些额外的统计信息
        print("\n策略统计:")
        print(f"平均持仓天数: {position.sum() / (buy_signals.sum() if buy_signals.sum() > 0 else 1):.1f}天")
        print(f"总持仓天数: {position.sum()}天")
        print(f"总回测天数: {len(position)}天")
        print(f"持仓比例: {(position.sum() / len(position) * 100):.1f}%")
        
        return buy_signals, sell_signals
    
    def backtest(self):
        """执行回测"""
        print("\n开始执行回测...")
        self.fetch_data()
        buy_signals, sell_signals = self.calculate_signals()
        
        # 使用vectorbt进行回测
        portfolio = vbt.Portfolio.from_signals(
            close=self.data['close_hfq'],
            entries=buy_signals,
            exits=sell_signals,
            freq='1D',
            init_cash=100000,  # 初始资金
            fees=0.001,        # 手续费
            slippage=0.001     # 滑点
        )
        
        # 获取回测结果
        final_value = float(portfolio.value().iloc[-1])
        total_return = float(portfolio.total_return())
        
        print(f"\n回测完成")
        print(f"初始资金: {portfolio.init_cash:,.2f}")
        print(f"最终资金: {final_value:,.2f}")
        print(f"总收益率: {total_return:.2%}")
        print(f"总交易次数: {len(portfolio.orders)}")
        
        return portfolio
    
    def get_performance_metrics(self, portfolio):
        """获取回测性能指标"""
        metrics = {
            'Total Return (%)': portfolio.total_return() * 100,
            'Sharpe Ratio': portfolio.sharpe_ratio(),
            'Max Drawdown (%)': portfolio.max_drawdown() * 100,
            'Total Trades': len(portfolio.orders),  # 总交易次数
        }
        
        # 获取交易记录
        trades = portfolio.trades.records_readable
        print("\n交易记录列名:", trades.columns.tolist())  # 打印列名以便调试
        
        if len(trades) > 0:
            # 计算胜率
            winning_trades = trades[trades['PnL'] > 0]
            metrics['Win Rate (%)'] = (len(winning_trades) / len(trades)) * 100
            
            # 计算盈亏比
            if len(winning_trades) > 0:
                avg_win = winning_trades['PnL'].mean()
                losing_trades = trades[trades['PnL'] < 0]
                avg_loss = abs(losing_trades['PnL'].mean()) if len(losing_trades) > 0 else 1
                metrics['Profit/Loss Ratio'] = avg_win / avg_loss if avg_loss != 0 else float('inf')
            
            # 计算总盈利和总亏损
            total_profit = winning_trades['PnL'].sum() if len(winning_trades) > 0 else 0
            total_loss = abs(trades[trades['PnL'] < 0]['PnL'].sum()) if len(trades[trades['PnL'] < 0]) > 0 else 0
            metrics['Total Profit (%)'] = (total_profit / portfolio.init_cash) * 100
            metrics['Total Loss (%)'] = (total_loss / portfolio.init_cash) * 100
            
            # 计算持仓时间（如果有相关列）
            if 'Exit Idx' in trades.columns and 'Entry Idx' in trades.columns:
                durations = trades['Exit Idx'] - trades['Entry Idx']
                metrics['Average Trade Duration (days)'] = durations.mean()
                metrics['Max Trade Duration (days)'] = durations.max()
                metrics['Min Trade Duration (days)'] = durations.min()
            
            # 计算每笔交易的平均收益
            metrics['Average Profit per Trade (%)'] = (trades['PnL'].mean() / portfolio.init_cash) * 100
            
            # 打印详细的交易统计
            print("\n交易统计:")
            print(f"总交易次数: {len(trades)}")
            print(f"盈利交易次数: {len(winning_trades)}")
            print(f"亏损交易次数: {len(trades) - len(winning_trades)}")
            print(f"平均每笔交易收益: {metrics['Average Profit per Trade (%)']:.2f}%")
            
        else:
            metrics.update({
                'Win Rate (%)': 0.0,
                'Profit/Loss Ratio': 0.0,
                'Total Profit (%)': 0.0,
                'Total Loss (%)': 0.0,
                'Average Trade Duration (days)': 0.0,
                'Max Trade Duration (days)': 0.0,
                'Min Trade Duration (days)': 0.0,
                'Average Profit per Trade (%)': 0.0
            })
            print("\n没有交易记录")
            
        return metrics

def run_backtest(stock_code, **kwargs):
    """运行回测的便捷函数"""
    strategy = MACrossStrategy(stock_code, **kwargs)
    portfolio = strategy.backtest()
    metrics = strategy.get_performance_metrics(portfolio)
    
    print(f"\nBacktest Results for {stock_code}:")
    for metric, value in metrics.items():
        print(f"{metric}: {value:.2f}")
        
    return portfolio, metrics

if __name__ == "__main__":
    # 示例使用
    stock_code = "000007.SZ"  # 示例股票代码
    
    # 配置参数
    config = {
        'lookback_days': 250,
        'short_window': 3,
        'long_window': 10,
        'volatility_window': 15,
        'volatility_threshold': 0.10,
        'price_up_days': 3,
        'price_up_threshold': 0.05,
        'db_config': {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'tushare',
            'charset': 'utf8mb4'
        }
    }
    
    # 运行回测
    portfolio, metrics = run_backtest(stock_code, **config)
