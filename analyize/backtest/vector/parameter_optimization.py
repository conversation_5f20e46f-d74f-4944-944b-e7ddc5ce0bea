import pandas as pd
import numpy as np
from itertools import product
from datetime import datetime
from ma_cross_strategy import MACrossStrategy
import json
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns

# 设置matplotlib中文显示和样式
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
sns.set_style("whitegrid")  # 使用seaborn的网格样式

class StrategyOptimizer:
    def __init__(self, stock_code):
        self.stock_code = stock_code
        # 参数网格定义
        self.param_grid = {
            'short_window': [3, 5, 7, 10],           # 短期均线
            'long_window': [10, 15, 20, 30],         # 长期均线
            'volatility_window': [10, 15, 20],       # 波动率窗口
            'volatility_threshold': [0.08, 0.10, 0.12], # 波动率阈值
            'price_up_days': [3, 5, 7],              # 涨幅统计天数
            'price_up_threshold': [0.03, 0.05, 0.07] # 涨幅阈值
        }
        
        # 中文参数名映射
        self.param_names_cn = {
            'short_window': '短期均线',
            'long_window': '长期均线',
            'volatility_window': '波动率窗口',
            'volatility_threshold': '波动率阈值',
            'price_up_days': '涨幅统计天数',
            'price_up_threshold': '涨幅阈值'
        }
        
        # 中文指标名映射
        self.metric_names_cn = {
            'Total Return (%)': '总收益率(%)',
            'Sharpe Ratio': '夏普比率',
            'Max Drawdown (%)': '最大回撤(%)',
            'Win Rate (%)': '胜率(%)'
        }
        
        self.valid_combinations = []
        self.results = []
        self.results_df = None
        
    def generate_valid_combinations(self):
        """生成有效的参数组合"""
        param_names = list(self.param_grid.keys())
        param_values = list(self.param_grid.values())
        
        for values in product(*param_values):
            params = dict(zip(param_names, values))
            # 确保长期均线窗口大于短期均线窗口
            if params['long_window'] > params['short_window']:
                self.valid_combinations.append(params)
        
        print(f"生成了 {len(self.valid_combinations)} 个有效参数组合")
        return self.valid_combinations
    
    def evaluate_params(self, params, lookback_days=250):
        """评估单个参数组合"""
        try:
            strategy = MACrossStrategy(
                self.stock_code,
                lookback_days=lookback_days,
                **params
            )
            portfolio = strategy.backtest()
            metrics = strategy.get_performance_metrics(portfolio)
            
            # 添加参数到结果中
            result = {
                **params,
                **metrics
            }
            return result
            
        except Exception as e:
            print(f"参数评估失败: {str(e)}")
            return None
    
    def optimize(self, lookback_days=250):
        """执行参数优化"""
        print(f"\n开始优化股票 {self.stock_code} 的策略参数...")
        
        # 生成有效参数组合
        self.generate_valid_combinations()
        
        # 评估所有参数组合
        for params in tqdm(self.valid_combinations, desc="参数优化进度"):
            result = self.evaluate_params(params, lookback_days)
            if result is not None:
                self.results.append(result)
        
        # 转换为DataFrame并重命名列
        self.results_df = pd.DataFrame(self.results)
        
        # 重命名列为中文
        rename_dict = {**self.param_names_cn, **self.metric_names_cn}
        self.results_df = self.results_df.rename(columns=rename_dict)
        
        # 按不同指标排序并获取最优参数
        best_params = {
            '夏普比率': self.get_best_params(sort_by='夏普比率', ascending=False),
            '总收益率': self.get_best_params(sort_by='总收益率(%)', ascending=False),
            '最大回撤': self.get_best_params(sort_by='最大回撤(%)', ascending=True),
            '胜率': self.get_best_params(sort_by='胜率(%)', ascending=False)
        }
        
        return best_params
    
    def get_best_params(self, sort_by='夏普比率', ascending=False):
        """获取特定指标下的最优参数"""
        if self.results_df is None or len(self.results_df) == 0:
            return None
            
        sorted_df = self.results_df.sort_values(sort_by, ascending=ascending)
        best_row = sorted_df.iloc[0]
        
        param_keys = list(self.param_names_cn.values())
        metrics_keys = ['总收益率(%)', '夏普比率', '最大回撤(%)', '胜率(%)']
        
        best_params = {
            '参数': {key: best_row[key] for key in param_keys},
            '指标': {key: best_row[key] for key in metrics_keys}
        }
        
        return best_params
    
    def plot_parameter_impact(self, metric='总收益率(%)', save_path=None):
        """绘制参数对性能指标的影响"""
        if self.results_df is None or len(self.results_df) == 0:
            print("没有可用的优化结果")
            return
            
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'参数对{metric}的影响分析', fontsize=14)
        
        params = list(self.param_names_cn.values())
        param_keys = list(self.param_names_cn.keys())
        
        for i, (param_key, param_cn) in enumerate(zip(param_keys, params)):
            ax = axes[i//3, i%3]
            sns.boxplot(data=self.results_df, x=param_cn, y=metric, ax=ax)
            ax.set_title(f'{param_cn}的影响')
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path)
        plt.show()
    
    def save_results(self, filename_prefix):
        """保存优化结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        csv_file = f'{filename_prefix}_{timestamp}.csv'
        self.results_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        # 保存参数影响分析图
        for metric in ['总收益率(%)', '夏普比率', '最大回撤(%)', '胜率(%)']:
            plot_file = f'{filename_prefix}_{metric}_{timestamp}.png'
            self.plot_parameter_impact(metric=metric, save_path=plot_file)
        
        print(f"结果已保存到: {csv_file}")

def optimize_single_stock(stock_code, lookback_days=250):
    """优化单个股票的策略参数"""
    optimizer = StrategyOptimizer(stock_code)
    best_params = optimizer.optimize(lookback_days)
    
    # 打印最优参数
    print(f"\n{stock_code} 的最优参数:")
    for metric, params in best_params.items():
        print(f"\n基于 {metric}:")
        print("参数:")
        for param, value in params['参数'].items():
            print(f"  {param}: {value}")
        print("性能指标:")
        for metric_name, value in params['指标'].items():
            print(f"  {metric_name}: {value:.2f}")
    
    # 保存结果
    optimizer.save_results(f'optimization_results_{stock_code}')
    return optimizer

if __name__ == "__main__":
    # 示例使用
    stock_code = "300042.SZ"
    optimizer = optimize_single_stock(stock_code)
