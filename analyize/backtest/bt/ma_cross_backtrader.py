import backtrader as bt
import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import math


class MySQLData(bt.feeds.PandasData):
    """自定义从MySQL读取数据的Data Feed"""
    params = (
        ('datetime', None),
        ('open', 'open_hfq'),
        ('high', 'high_hfq'),
        ('low', 'low_hfq'),
        ('close', 'close_hfq'),
        ('volume', 'vol'),
        ('openinterest', None),
    )


class MACrossStrategy(bt.Strategy):
    """
    移动平均线交叉策略
    - 当快速MA上穿慢速MA，且满足波动率和短期涨幅条件时买入
    - 当快速MA下穿慢速MA时卖出
    """
    params = (
        ('fast_period', 3),  # 快速MA周期
        ('slow_period', 7),  # 慢速MA周期
        ('volatility_period', 15),  # 波动率检查周期
        ('volatility_threshold', 0.10),  # 波动率阈值
        ('short_term_period', 3),  # 短期涨幅检查周期
        ('short_term_threshold', 0.05),  # 短期涨幅阈值
        ('print_log', False),  # 是否打印日志
        ('order_size', 1)  # 买卖的股票数量
    )

    def __init__(self):
        # 初始化移动平均线指标
        self.fast_ma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.fast_period)
        self.slow_ma = bt.indicators.SimpleMovingAverage(
            self.data.close, period=self.params.slow_period)
        self.crossover = bt.indicators.CrossOver(self.fast_ma, self.slow_ma)

    def check_volatility(self):
        """检查前N天的波动率是否在阈值内"""
        if len(self.data) < self.params.volatility_period + 1:
            return False

        high = max(self.data.high.get(size=self.params.volatility_period, ago=-1))
        low = min(self.data.low.get(size=self.params.volatility_period, ago=-1))
        volatility = (high - low) / low
        return volatility <= self.params.volatility_threshold

    def check_short_term_rise(self):
        """检查最近几天的累计涨幅是否满足条件"""
        if len(self.data) < self.params.short_term_period:
            return False

        start_price = self.data.close[-self.params.short_term_period]
        end_price = self.data.close[0]
        rise = (end_price - start_price) / start_price
        return rise >= self.params.short_term_threshold

    def next(self):
        """主策略逻辑"""
        if self.crossover > 0:  # 金叉
            if self.check_volatility() and self.check_short_term_rise():
                if not self.position:  # 没有持仓
                    self.order_target_size(target=self.params.order_size)
                    if self.params.print_log:
                        self.log(f'BUY CREATE, {self.data.close[0]:.2f}')
        elif self.crossover < 0:  # 死叉
            if self.position:  # 持有仓位
                self.order_target_size(target=0)
                if self.params.print_log:
                    self.log(f'SELL CREATE, {self.data.close[0]:.2f}')

    def log(self, txt, dt=None):
        """记录日志"""
        dt = dt or self.datas[0].datetime.date(0)
        print(f'{dt.isoformat()}, {txt}')

    def notify_order(self, order):
        """订单状态变化"""
        # 订单提交给券商
        if order.status in [order.Submitted, order.Accepted]:
            return

        # 订单完成
        if order.status in [order.Completed]:
            if order.isbuy():
                if self.params.print_log:
                    self.log(
                        f'BUY EXECUTED, Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, '
                        f'Comm: {order.executed.comm:.2f}'
                    )
            elif order.issell():
                if self.params.print_log:
                    self.log(
                        f'SELL EXECUTED, Price: {order.executed.price:.2f}, '
                        f'Cost: {order.executed.value:.2f}, '
                        f'Comm: {order.executed.comm:.2f}'
                    )

        # 订单失败
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            if self.params.print_log:
                self.log(f'Order Canceled/Margin/Rejected: {order.status}')


def get_stock_data(ts_code, days=250, db_config=None):
    """
    从MySQL数据库获取股票数据，使用SQLAlchemy

    参数:
        ts_code (str): 股票代码
        days (int): 获取的天数
        db_config (dict): 数据库配置
    """
    if db_config is None:
        db_config = DB_CONFIG

    # 创建SQLAlchemy引擎
    engine_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}/{db_config['database']}"
    engine = create_engine(engine_url)

    # 计算起始日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days * 2)  # 日期改为原来天数的两倍

    # SQL查询
    query = f"""
    SELECT trade_date, open_hfq, high_hfq, low_hfq, close_hfq, vol
    FROM stk_factor
    WHERE ts_code = '{ts_code}' AND trade_date >= '{start_date.date()}'
    ORDER BY trade_date
    """

    try:
        # 使用pandas直接从SQLAlchemy引擎读取数据
        df = pd.read_sql_query(query, engine)

        # 设置日期索引
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)

        return df

    except Exception as e:
        print(f"Error fetching data: {e}")
        return None
    finally:
        engine.dispose()


def run_backtest(ts_code, cash=100000.0, commission=0.001, db_config=None, strategy_params=None):
    """
    运行回测

    参数:
        ts_code (str): 股票代码
        cash (float): 初始资金
        commission (float): 佣金率
        db_config (dict): 数据库配置
        strategy_params (dict): 策略参数
    """
    # 获取数据
    df = get_stock_data(ts_code, db_config=db_config)
    if df is None or df.empty:
        print("No data available for backtest")
        return

    # 创建cerebro引擎
    cerebro = bt.Cerebro()

    # 加载数据，并指定日期范围
    data = MySQLData(
        dataname=df,
        fromdate=df.index[0],  # 使用数据的第一天作为起始日期
        todate=df.index[-1]  # 使用数据的最后一天作为结束日期
    )
    print(f'Data loaded for {data}')
    cerebro.adddata(data)

    # 设置初始资金
    cerebro.broker.setcash(cash)

    # 设置佣金
    cerebro.broker.setcommission(commission=commission)

    # 添加策略和参数
    cerebro.addstrategy(MACrossStrategy, **strategy_params if strategy_params else {})

    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe_ratio', riskfreerate=0.02)
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')

    # 运行回测
    initial_value = cerebro.broker.getvalue()
    print(f'Starting Portfolio Value: {initial_value:.2f}')
    results = cerebro.run()
    final_value = cerebro.broker.getvalue()
    print(f'Final Portfolio Value: {final_value:.2f}')

    # 输出分析结果
    strat = results[0]

    # 基础指标
    print('\nBacktest Results:')

    # 计算总收益率
    total_return = (final_value - initial_value) / initial_value
    print(f'Total Return: {total_return:.2%}')

    # 安全获取Sharpe ratio
    try:
        sharpe = strat.analyzers.sharpe_ratio.get_analysis()['sharperatio']
        if sharpe is not None:
            print(f'Sharpe Ratio: {sharpe:.3f}')
        else:
            print('Sharpe Ratio: Not available (insufficient data or no trades)')
    except (KeyError, AttributeError):
        print('Sharpe Ratio: Not available')

    # 安全获取最大回撤
    try:
        max_drawdown = strat.analyzers.drawdown.get_analysis()['max']['drawdown']
        if max_drawdown is not None:
            print(f'Max Drawdown: {max_drawdown:.2%}')
        else:
            print('Max Drawdown: Not available')
    except (KeyError, AttributeError):
        print('Max Drawdown: Not available')

    # 交易统计
    trade_analysis = strat.analyzers.trades.get_analysis()
    print('\nTrade Statistics:')

    total_trades = trade_analysis.total.total if hasattr(trade_analysis, 'total') else 0
    print(f'Total Trades: {total_trades}')

    if total_trades > 0:
        won_trades = trade_analysis.won.total if hasattr(trade_analysis.won, 'total') else 0
        lost_trades = trade_analysis.lost.total if hasattr(trade_analysis.lost, 'total') else 0
        print(f'Winning Trades: {won_trades}')
        print(f'Losing Trades: {lost_trades}')

        if won_trades > 0 and hasattr(trade_analysis.won, 'pnl'):
            print(f'Average Winning Trade: {trade_analysis.won.pnl.average:.2f}')
        if lost_trades > 0 and hasattr(trade_analysis.lost, 'pnl'):
            print(f'Average Losing Trade: {trade_analysis.lost.pnl.average:.2f}')

    # 如果有交易发生，才绘制图表
    # if total_trades > 0:
    #     try:
    #         cerebro.plot(style='candlestick', barup='red', bardown='green')
    #     except Exception as e:
    #         print(f'\nError plotting results: {e}')
    # else:
    #     print('\nNo trades executed - skipping plot generation')


if __name__ == '__main__':
    # 配置参数
    TS_CODE = '300059.SZ'  # 股票代码
    INITIAL_CASH = 100000.0  # 初始资金
    COMMISSION_RATE = 0.003  # 佣金率

    # 数据库配置
    from get_data.db_config import DB_CONFIG

    DB_CONFIG = DB_CONFIG

    # 策略参数
    STRATEGY_PARAMS = {
        'fast_period': 3,
        'slow_period': 7,
        'volatility_period': 15,
        'volatility_threshold': 0.10,
        'short_term_period': 3,
        'short_term_threshold': 0.05,
        'print_log': True  # 是否打印日志
    }

    # 运行回测
    run_backtest(
        ts_code=TS_CODE,
        cash=INITIAL_CASH,
        commission=COMMISSION_RATE,
        db_config=DB_CONFIG,
        strategy_params=STRATEGY_PARAMS
    )
