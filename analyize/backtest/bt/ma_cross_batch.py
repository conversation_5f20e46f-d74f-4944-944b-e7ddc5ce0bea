import pandas as pd
from sqlalchemy import create_engine
import os
from datetime import datetime
import json
from urllib.parse import quote_plus

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'tushare'
}

def get_stock_list():
    """获取符合条件的股票列表"""
    try:
        # 创建数据库连接
        password = quote_plus(DB_CONFIG['password'])
        engine_url = f"mysql+pymysql://{DB_CONFIG['user']}:{password}@{DB_CONFIG['host']}/{DB_CONFIG['database']}"
        print("Debug - Connection URL (masked):", engine_url.replace(password, "****"))
        engine = create_engine(engine_url)
    
        # SQL查询获取股票列表
        query = """
        SELECT ts_code, name, industry
        FROM stock_basic
        WHERE list_status = 'L'
        AND ts_code LIKE '300%%'
        LIMIT 300
        """
        print("Debug - SQL Query:", query)
        
        df = pd.read_sql_query(query, engine)
        return df
    
    except Exception as e:
        print(f"Error fetching stock list: {str(e)}")
        import traceback
        print("Full error traceback:")
        print(traceback.format_exc())
        return None
    finally:
        engine.dispose()

def run_batch_backtest():
    """运行批量回测"""
    # 获取股票列表
    stocks_df = get_stock_list()
    if stocks_df is None or stocks_df.empty:
        print("No stocks found for backtesting")
        return
    
    # 创建结果目录
    results_dir = "backtest_results"
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    # 创建结果文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(results_dir, f"ma_cross_results_{timestamp}.csv")
    summary_file = os.path.join(results_dir, f"ma_cross_summary_{timestamp}.json")
    
    # 准备存储结果的列表
    results = []
    summary = {
        'total_stocks': len(stocks_df),
        'successful_tests': 0,
        'failed_tests': 0,
        'best_performance': {'ts_code': None, 'return': -float('inf')},
        'worst_performance': {'ts_code': None, 'return': float('inf')},
        'z_score': 0.0,
        'timestamp': timestamp
    }
    
    # 循环进行回测
    for idx, row in stocks_df.iterrows():
        ts_code = row['ts_code']
        print(f"\nBacktesting {ts_code} ({row['name']}) - {idx + 1}/{len(stocks_df)}")
        
        try:
            # 运行回测
            from ma_cross_backtrader import run_backtest
            backtest_result = run_backtest(ts_code, cash=100000.0, commission=0.001, db_config=DB_CONFIG)
            
            if backtest_result:  # 如果回测成功
                result_dict = {
                    'ts_code': ts_code,
                    'name': row['name'],
                    'industry': row['industry'],
                    'total_return': backtest_result['total_return'],
                    'sharpe_ratio': backtest_result['sharpe_ratio'],
                    'max_drawdown': backtest_result['max_drawdown'],
                    'total_trades': backtest_result['total_trades'],
                    'win_rate': backtest_result['win_rate'],
                    'timestamp': timestamp
                }
                
                results.append(result_dict)
                
                # 更新汇总信息
                summary['successful_tests'] += 1
                if backtest_result['total_return'] > summary['best_performance']['return']:
                    summary['best_performance'] = {
                        'ts_code': ts_code,
                        'return': backtest_result['total_return']
                    }
                if backtest_result['total_return'] < summary['worst_performance']['return']:
                    summary['worst_performance'] = {
                        'ts_code': ts_code,
                        'return': backtest_result['total_return']
                    }
            
        except Exception as e:
            print(f"Error testing {ts_code}: {e}")
            import traceback
            print("Full error traceback:")
            print(traceback.format_exc())
            summary['failed_tests'] += 1
    
    # 保存结果
    if results:
        # 计算平均收益率
        total_returns = [r['total_return'] for r in results]
        summary['z_score'] = sum(total_returns) / len(total_returns)
        
        # 保存详细结果到CSV
        results_df = pd.DataFrame(results)
        results_df.to_csv(results_file, index=False)
        print(f"\nDetailed results saved to: {results_file}")
        
        # 保存汇总信息到JSON
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=4)
        print(f"Summary saved to: {summary_file}")
        
        # 打印汇总信息
        print("\nBacktest Summary:")
        print(f"Total stocks tested: {summary['total_stocks']}")
        print(f"Successful tests: {summary['successful_tests']}")
        print(f"Failed tests: {summary['failed_tests']}")
        print(f"Average return: {summary['z_score']:.2%}")
        print(f"Best performance: {summary['best_performance']['ts_code']} ({summary['best_performance']['return']:.2%})")
        print(f"Worst performance: {summary['worst_performance']['ts_code']} ({summary['worst_performance']['return']:.2%})")
    else:
        print("No successful backtest results to save")

if __name__ == "__main__":
    run_batch_backtest()
