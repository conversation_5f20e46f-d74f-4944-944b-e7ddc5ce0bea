import backtrader as bt

class DualMAStrategy(bt.Strategy):
    """双移动平均线交叉策略"""
    params = (
        ('short_period', 10),  # 短期均线周期
        ('long_period', 30),   # 长期均线周期
        ('printlog', False),   # 是否打印交易日志
    )

    def __init__(self):
        # 引用收盘价数据
        self.dataclose = self.datas[0].close

        # 跟踪订单状态
        self.order = None
        self.buyprice = None
        self.buycomm = None

        # 添加移动平均线指标
        self.sma_short = bt.indicators.SimpleMovingAverage(
            self.datas[0], period=self.params.short_period)
        self.sma_long = bt.indicators.SimpleMovingAverage(
            self.datas[0], period=self.params.long_period)

        # 交叉信号指标
        self.crossover = bt.indicators.CrossOver(self.sma_short, self.sma_long)

    def log(self, txt, dt=None, doprint=False):
        """策略日志记录"""
        if self.params.printlog or doprint:
            dt = dt or self.datas[0].datetime.date(0)
            print(f'{dt.isoformat()}, {txt}')

    def notify_order(self, order):
        """订单状态通知"""
        if order.status in [order.Submitted, order.Accepted]:
            # 订单已提交/已接受 - 无需操作
            return

        # 检查订单是否完成
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(
                    f'BUY EXECUTED, Price: {order.executed.price:.2f}, Cost: {order.executed.value:.2f}, Comm {order.executed.comm:.2f}'
                )
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
            elif order.issell():
                self.log(
                    f'SELL EXECUTED, Price: {order.executed.price:.2f}, Cost: {order.executed.value:.2f}, Comm {order.executed.comm:.2f}'
                )
            self.bar_executed = len(self) # 记录交易发生的bar

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')

        # 重置订单跟踪
        self.order = None

    def notify_trade(self, trade):
        """交易状态通知"""
        if not trade.isclosed:
            return

        self.log(f'OPERATION PROFIT, GROSS {trade.pnl:.2f}, NET {trade.pnlcomm:.2f}')

    def next(self):
        """策略核心逻辑 - 每个bar执行一次"""
        # 记录收盘价
        # self.log(f'Close, {self.dataclose[0]:.2f}')

        # 如果有订单正在处理，则不执行新的交易
        if self.order:
            return

        # 检查是否持有仓位
        if not self.position:
            # 没有仓位，检查买入信号
            if self.crossover > 0:  # 短期均线上穿长期均线
                self.log(f'BUY CREATE, {self.dataclose[0]:.2f}')
                # 创建买入订单
                self.order = self.buy()
        else:
            # 持有仓位，检查卖出信号
            if self.crossover < 0:  # 短期均线下穿长期均线
                self.log(f'SELL CREATE, {self.dataclose[0]:.2f}')
                # 创建卖出订单
                self.order = self.sell()

    def stop(self):
        """策略结束时调用"""
        self.log(f'(Short Period {self.params.short_period:2d}, Long Period {self.params.long_period:2d}) Ending Value {self.broker.getvalue():.2f}', doprint=True)
