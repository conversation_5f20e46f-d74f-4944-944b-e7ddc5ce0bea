import backtrader as bt
import pandas as pd
from sqlalchemy import text
from datetime import datetime
import matplotlib.pyplot as plt

# 导入策略类
from analyize.trend.strategies import DualMAStrategy
# 导入数据库引擎
from get_data.db_config import get_engine

# --- 中文显示配置 ---
# 尝试设置 matplotlib 支持中文的字体
try:
    plt.rcParams['font.sans-serif'] = ['FangSong']  # 指定默认字体为黑体
    plt.rcParams['axes.unicode_minus'] = False   # 解决保存图像是负号'-'显示为方块的问题
except Exception as e:
    print(f"无法设置中文字体 'SimHei'，绘图可能无法正确显示中文: {e}")
    # 可以尝试其他字体，如 'Microsoft YaHei', 'FangSong' 等
    # plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']

def fetch_stock_data(ts_code, start_date, end_date):
    """从数据库获取股票数据"""
    engine = get_engine
    query = text(f"""
        SELECT
            trade_date AS datetime,
            open_hfq AS open,
            high_hfq AS high,
            low_hfq AS low,
            close_hfq AS close,
            vol AS volume
        FROM stk_factor
        WHERE ts_code = :ts_code AND trade_date BETWEEN :start_date AND :end_date
        ORDER BY trade_date ASC
    """)
    try:
        with engine.connect() as connection:
            df = pd.read_sql(query, connection, params={'ts_code': ts_code, 'start_date': start_date, 'end_date': end_date}, index_col='datetime', parse_dates=['datetime'])
        # Backtrader 需要 openinterest 列，即使不用，也填充为0
        df['openinterest'] = 0
        # 确保列名符合 backtrader 要求 (小写)
        df.columns = [col.lower() for col in df.columns]
        print(f"成功获取 {ts_code} 从 {start_date} 到 {end_date} 的数据，共 {len(df)} 条")
        return df
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return pd.DataFrame() # 返回空 DataFrame

def run_backtest(ts_code, start_date, end_date, short_period, long_period, initial_cash=100000.0, commission=0.0005, printlog=False, plot=True):
    """运行回测"""
    cerebro = bt.Cerebro()

    # 添加策略
    cerebro.addstrategy(DualMAStrategy, short_period=short_period, long_period=long_period, printlog=printlog)

    # 获取数据
    dataframe = fetch_stock_data(ts_code, start_date, end_date)
    if dataframe.empty:
        print("数据为空，无法进行回测。")
        return None

    # 创建数据 feed
    data = bt.feeds.PandasData(dataname=dataframe)

    # 添加数据到 Cerebro
    cerebro.adddata(data)

    # 设置初始资金
    cerebro.broker.setcash(initial_cash)

    # 设置佣金 (双边收取)
    cerebro.broker.setcommission(commission=commission)

    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe_ratio', timeframe=bt.TimeFrame.Days, compression=1, factor=252, annualize=True) # 假设一年252个交易日
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns') # 移除 annualize=True
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trade_analyzer')

    print('开始回测...')
    # 运行回测
    results = cerebro.run()
    print('回测结束.')

    # 获取策略实例和分析结果
    strat = results[0]
    analyzers = strat.analyzers

    # 打印回测报告
    print("-" * 30)
    print(f"回测报告: {ts_code}")
    print(f"时间范围: {start_date} to {end_date}")
    print(f"策略参数: 短期均线={short_period}, 长期均线={long_period}")
    print("-" * 30)
    print(f"初始资金: {initial_cash:,.2f}")
    print(f"结束资金: {cerebro.broker.getvalue():,.2f}")
    final_return = ((cerebro.broker.getvalue() / initial_cash) - 1) * 100
    print(f"总收益率: {final_return:.2f}%")

    # 打印分析器结果
    if hasattr(analyzers, 'returns') and analyzers.returns.get_analysis():
        total_return_analyzer = analyzers.returns.get_analysis()['rtot'] * 100
        # 尝试使用 'rnorm100' 获取年化收益率
        annualized_return_analyzer = analyzers.returns.get_analysis().get('rnorm100', None)
        if annualized_return_analyzer is not None:
             print(f"年化收益率 (Analyzer): {annualized_return_analyzer:.2f}%")
        else:
             print("年化收益率 (Analyzer): N/A (无法获取)")
        # print(f"总收益率 (Analyzer): {total_return_analyzer:.2f}%") # 与上面计算的总收益率可能因计算方式略有差异

    if hasattr(analyzers, 'sharpe_ratio') and analyzers.sharpe_ratio.get_analysis():
         sharpe = analyzers.sharpe_ratio.get_analysis()['sharperatio']
         print(f"年化夏普比率: {sharpe:.3f}" if sharpe is not None else "年化夏普比率: N/A")


    if hasattr(analyzers, 'drawdown') and analyzers.drawdown.get_analysis():
        max_drawdown = analyzers.drawdown.get_analysis().max.drawdown
        print(f"最大回撤: {max_drawdown:.2f}%")

    if hasattr(analyzers, 'trade_analyzer') and analyzers.trade_analyzer.get_analysis():
        trade_analysis = analyzers.trade_analyzer.get_analysis()
        print("-" * 30)
        print("交易分析:")
        print(f"  总交易次数: {trade_analysis.total.total}")
        if trade_analysis.total.total > 0:
            print(f"  盈利交易次数: {trade_analysis.won.total}")
            print(f"  亏损交易次数: {trade_analysis.lost.total}")
            print(f"  胜率: {trade_analysis.won.total / trade_analysis.total.total * 100:.2f}%")
            print(f"  平均盈利: {trade_analysis.won.pnl.average:.2f}")
            print(f"  平均亏损: {trade_analysis.lost.pnl.average:.2f}")
            print(f"  盈亏比: {abs(trade_analysis.won.pnl.average / trade_analysis.lost.pnl.average):.2f}" if trade_analysis.lost.pnl.average !=0 else "N/A")
            print(f"  最大盈利: {trade_analysis.won.pnl.max:.2f}")
            print(f"  最大亏损: {trade_analysis.lost.pnl.max:.2f}")
        print("-" * 30)

    # 绘图
    if plot:
        print("生成回测图表...")
        # 设置图表大小
        plt.rcParams['figure.figsize'] = [15, 12] # 宽, 高 (英寸)
        # style='candlestick' 可以让成交量显示为红绿色
        cerebro.plot(style='candlestick', barup='red', bardown='green', volup='red', voldown='green')
        print("图表已生成。请查看弹出的窗口。")

    return results

if __name__ == '__main__':
    # --- 回测参数配置 ---
    ts_code_to_backtest = '688719.SH'  # 平安银行
    start_date_str = '2023-01-01'
    end_date_str = '2024-12-31'
    short_ma_period = 3
    long_ma_period = 5
    initial_portfolio_value = 100000.0
    commission_rate = 0.0005 # 万分之五
    enable_printlog = True # 是否打印详细交易日志
    enable_plot = False      # 是否绘制结果图表

    # 运行回测
    run_backtest(
        ts_code=ts_code_to_backtest,
        start_date=start_date_str,
        end_date=end_date_str,
        short_period=short_ma_period,
        long_period=long_ma_period,
        initial_cash=initial_portfolio_value,
        commission=commission_rate,
        printlog=enable_printlog,
        plot=enable_plot
    )
