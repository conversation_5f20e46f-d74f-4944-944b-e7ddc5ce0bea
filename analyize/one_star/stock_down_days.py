from sqlalchemy import create_engine, text
from datetime import datetime, timedelta
import pandas as pd
import sys
import os

# 添加get_data目录到系统路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'get_data'))
from get_data.db_config import DB_URL

def get_stock_down_days(engine, days=20, top_n=30):
    """
    获取最近指定交易日数内股票下跌天数统计并降序排序
    
    Args:
        engine: SQLAlchemy 数据库引擎
        days: 统计的交易日数，默认20天
        top_n: 返回前N个股票，默认30个
    
    Returns:
        DataFrame: 包含股票代码和下跌天数的数据框
    """
    # 构建SQL查询语句
    sql = """
    WITH latest_dates AS (
        -- 获取每只股票最近的n个交易日
        SELECT 
            ts_code COLLATE utf8mb4_general_ci as ts_code,
            trade_date,
            ROW_NUMBER() OVER (PARTITION BY ts_code ORDER BY trade_date DESC) as date_rank
        FROM stk_factor
    ),
    recent_data AS (
        -- 选择最近n个交易日的数据
        SELECT 
            f.ts_code COLLATE utf8mb4_general_ci as ts_code,
            f.trade_date,
            f.`change`
        FROM stk_factor f
        INNER JOIN latest_dates d ON f.ts_code = d.ts_code AND f.trade_date = d.trade_date
        INNER JOIN stock_basic b ON f.ts_code COLLATE utf8mb4_general_ci = b.ts_code
        WHERE d.date_rank <= :days
        AND b.list_status = 'L'  -- 只选择上市状态的股票
    ),
    down_stats AS (
        -- 计算每只股票的下跌统计
        SELECT 
            r.ts_code,
            b.name as stock_name,
            b.industry,
            COUNT(*) as total_days,
            SUM(CASE 
                WHEN r.`change` < 0 THEN 1 
                ELSE 0 
            END) as down_days,
            SUM(CASE 
                WHEN r.`change` < 0 THEN 1 
                ELSE 0 
            END) * 100.0 / COUNT(*) as down_ratio
        FROM recent_data r
        INNER JOIN stock_basic b ON r.ts_code COLLATE utf8mb4_general_ci = b.ts_code
        GROUP BY r.ts_code, b.name, b.industry
        HAVING COUNT(*) >= :min_days
    )
    -- 选择跌幅天数最多的股票
    SELECT *
    FROM down_stats
    ORDER BY down_days DESC, down_ratio DESC
    LIMIT :top_n
    """
    
    # 执行查询
    with engine.connect() as conn:
        df = pd.read_sql(
            text(sql), 
            conn, 
            params={
                'days': days,
                'min_days': days * 0.8,  # 要求至少有80%的交易日数据
                'top_n': top_n
            }
        )
    #将结果保存到 csv 文件中
    df.to_csv('stock_down_days.csv', index=False)
    return df

if __name__ == "__main__":
    # 使用配置文件中的数据库连接信息
    engine = create_engine(DB_URL)
    
    # 获取下跌天数统计
    result_df = get_stock_down_days(engine)
    
    # 显示结果
    pd.set_option('display.max_rows', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_columns', None)
    print("\n最近20个交易日股票下跌天数排行（前30名）:")
    print(result_df)
