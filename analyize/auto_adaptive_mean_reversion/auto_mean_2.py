#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import sys
import os
import time
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor
import traceback
import random # Keep for potential future use, removed from critical path

# --- Basic Configuration ---
# Set Matplotlib font for Chinese characters
try:
    # Try common fonts for Chinese support
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Heiti TC', 'Microsoft YaHei'] 
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"Warning: Could not set Chinese font for Matplotlib. Plots might not display CJK characters correctly. Error: {e}")

# Add project root to sys.path if necessary (adjust path as needed)
# project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# sys.path.append(project_root)
# print(f"Project root added to path: {project_root}") # Optional: confirm path

# --- Database Configuration ---
try:
    # Assume db_config.py is in a 'get_data' subdirectory relative to this script's location
    # Or adjust the import path based on your project structure
    current_dir = os.path.dirname(os.path.abspath(__file__))
    get_data_dir = os.path.join(current_dir, 'get_data') 
    if get_data_dir not in sys.path:
        sys.path.insert(0, get_data_dir) # Add get_data dir temporarily to path
        
    from get_data.db_config import DB_URL # Now import should work
    print("Successfully imported DB_URL from get_data.db_config")
except ImportError:
    print("Error: Could not find or import 'db_config.py'. Make sure it exists and DB_URL is defined.")
    print("Please create 'get_data/db_config.py' with DB_URL = 'your_database_connection_string'")
    # Provide a placeholder URL to allow the script to run partially for structure review
    DB_URL = "postgresql+psycopg2://user:password@host:port/database" # Replace with your actual URL
    print(f"Using placeholder DB_URL: {DB_URL}")
except Exception as e:
    print(f"An unexpected error occurred during DB_URL import: {e}")
    sys.exit(1) # Exit if DB config is critical and fails unexpectedly

from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool

# --- Backtest Parameters ---
START_DATE = '2020-01-01'
END_DATE = '2023-12-31'
WINDOW = 20          # Moving average and standard deviation window (N)
K_FACTOR = 2.0       # Standard deviation multiplier (k)

# --- Database Connection Pool Configuration ---
MAX_POOL_SIZE = 10   # Max connections in the pool
MAX_OVERFLOW = 5     # Max connections allowed beyond pool size
POOL_TIMEOUT = 30    # Seconds to wait for a connection before timing out
POOL_RECYCLE = 1800  # Seconds after which a connection is recycled (prevents stale connections)
MAX_RETRIES = 3      # Max retries for database operations
RETRY_DELAY = 2      # Initial delay in seconds for retries (exponential backoff)

# --- Parallel Processing Configuration ---
# Adjust based on your CPU cores and memory. Don't exceed MAX_POOL_SIZE if processes access DB.
# Since data fetching is now batched, this mainly affects CPU-bound calculations.
MAX_WORKERS = os.cpu_count() or 4 # Default to number of CPU cores or 4 if detection fails

# === 辅助函数 ===

def create_db_engine(db_url, pool_size, max_overflow, pool_timeout, pool_recycle):
    """创建一个带有连接池的SQLAlchemy引擎。"""
    try:
        engine = create_engine(
            db_url,
            poolclass=QueuePool,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=pool_timeout,
            pool_recycle=pool_recycle,
            # Add connect_args for specific drivers if needed, e.g.,
            # connect_args={'options': '-c timezone=utc'} # For PostgreSQL
        )
        # Test connection
        with engine.connect() as connection:
            print("Database connection successful.")
        return engine
    except Exception as e:
        print(f"Error creating database engine: {e}")
        print("Please check your DB_URL and database server status.")
        return None

def get_sh_stock_list(engine):
    """
    获取上海证券交易所(SSE)活跃上市股票列表。
    使用重试逻辑处理数据库连接问题。

    参数:
        engine: SQLAlchemy引擎实例。

    返回:
        list: SSE股票代码(ts_code)列表，失败时返回空列表。
    """
    print("Fetching SSE stock list from database...")
    query = text("""
        SELECT ts_code
        FROM stock_basic
        WHERE exchange = 'SSE' AND list_status = 'L'
    """) # Use text() for clarity and potential parameterization later

    for attempt in range(MAX_RETRIES + 1):
        try:
            with engine.connect() as conn:
                df = pd.read_sql(query, conn)
            
            stock_list = df['ts_code'].tolist()
            if not stock_list:
                print("Warning: No SSE stocks found in 'stock_basic' table for exchange='SSE' and list_status='L'.")
                return []
                
            print(f"Successfully fetched {len(stock_list)} SSE stock codes.")
            return stock_list

        except Exception as e:
            error_msg = str(e)
            print(f"Attempt {attempt + 1}/{MAX_RETRIES + 1}: Error fetching stock list: {error_msg}")
            
            # Check for common connection-related errors
            if "connect" in error_msg.lower() or "timeout" in error_msg.lower() or "operationalerror" in error_msg.lower():
                if attempt < MAX_RETRIES:
                    wait_time = RETRY_DELAY * (2 ** attempt) # Exponential backoff
                    print(f"Database connection issue. Retrying in {wait_time:.1f} seconds...")
                    time.sleep(wait_time)
                else:
                    print("Max retries reached for fetching stock list. Aborting.")
                    return []
            else:
                # Non-connection error, likely SQL or data issue, don't retry
                print("Non-connection error encountered. Aborting stock list fetch.")
                return []
    return [] # Should not be reached if loop completes, but added for safety

def fetch_batch_stock_data(engine, stock_list, start_date, end_date):
    """
    批量获取一组股票的历史数据。

    参数:
        engine: SQLAlchemy引擎实例。
        stock_list: 股票代码(ts_code)列表。
        start_date: 开始日期字符串(YYYY-MM-DD)。
        end_date: 结束日期字符串(YYYY-MM-DD)。

    返回:
        pandas.DataFrame: 包含所有请求股票数据的DataFrame，
                          失败或无数据时返回空DataFrame。
                          包含'ts_code', 'trade_date', 'close_hfq'等字段。
    """
    if not stock_list:
        print("Stock list is empty. Cannot fetch data.")
        return pd.DataFrame()

    print(f"Fetching historical data for {len(stock_list)} stocks from {start_date} to {end_date}...")
    
    # Using text() and bindparams for security and clarity, especially if stock_list gets large
    query = text("""
        SELECT 
            ts_code, trade_date, close_hfq, open_hfq, high_hfq, low_hfq, vol, amount
        FROM stk_factor
        WHERE ts_code IN :stock_codes -- Use IN for tuple binding (MySQL compatible)
        AND trade_date BETWEEN :start_dt AND :end_dt
        ORDER BY ts_code, trade_date -- Crucial for groupby later
    """)

    # For MySQL using IN clause with tuple binding
    params = {'stock_codes': tuple(stock_list), 'start_dt': start_date, 'end_dt': end_date}

    start_fetch_time = time.time()
    for attempt in range(MAX_RETRIES + 1):
        try:
            with engine.connect() as conn:
                all_data = pd.read_sql(query, conn, params=params)
            
            fetch_duration = time.time() - start_fetch_time
            if all_data.empty:
                print(f"Warning: No historical data found for the specified stocks and date range (Duration: {fetch_duration:.2f}s).")
                return pd.DataFrame()
                
            print(f"Successfully fetched {len(all_data)} data points for {all_data['ts_code'].nunique()} unique stocks (Duration: {fetch_duration:.2f}s).")
            
            # Convert trade_date to datetime objects *once* after fetching
            all_data['trade_date'] = pd.to_datetime(all_data['trade_date'])
            
            return all_data

        except Exception as e:
            error_msg = str(e)
            print(f"Attempt {attempt + 1}/{MAX_RETRIES + 1}: Error fetching batch stock data: {error_msg}")
            if attempt < MAX_RETRIES:
                 wait_time = RETRY_DELAY * (2 ** attempt)
                 print(f"Retrying data fetch in {wait_time:.1f} seconds...")
                 time.sleep(wait_time)
            else:
                 print("Max retries reached for fetching batch data. Aborting.")
                 return pd.DataFrame()
                 
    return pd.DataFrame() # Should not be reached

# === 核心回测逻辑 ===

def backtest_single_stock(ts_code, window, k_factor, stock_data):
    """
    对单只股票执行自适应均值回归策略回测计算。
    假设已提供并预处理了stock_data DataFrame。

    参数:
        ts_code: 股票代码(用于报告)。
        window: 滚动窗口大小(N)。
        k_factor: 标准差倍数(k)。
        stock_data: 包含'trade_date'(作为索引), 'close_hfq'的DataFrame。

    返回:
        dict: 包含该股票回测结果的字典，错误或数据不足时返回None。
    """
    try:
        # --- Data Validation and Preparation ---
        if stock_data is None or stock_data.empty:
            # This check might be redundant if caller ensures data exists, but safe to keep
            # print(f"[{ts_code}] No data provided, skipping.")
            return None

        # 确保数据是副本，以防止在并行环境中修改原始分组数据
        data = stock_data.copy()

        required_columns = ['close_hfq'] # trade_date is expected as index now
        if not all(col in data.columns for col in required_columns):
            print(f"[{ts_code}] Data missing required columns ({required_columns}), skipping.")
            return None

        # Ensure index is datetime
        if not isinstance(data.index, pd.DatetimeIndex):
             data['trade_date'] = pd.to_datetime(data['trade_date'])
             data.set_index('trade_date', inplace=True)
             
        # Rename for consistency with original logic
        data.rename(columns={'close_hfq': 'Close'}, inplace=True)

        # Check for sufficient data length for rolling calculations + some buffer
        min_data_points = window + 30 # Need at least window size + buffer for meaningful results
        if len(data) < min_data_points:
            # print(f"[{ts_code}] Insufficient data ({len(data)} points, need {min_data_points}), skipping.")
            return None

        # --- Indicator Calculation ---
        data['SMA'] = data['Close'].rolling(window=window, min_periods=window).mean()
        data['StdDev'] = data['Close'].rolling(window=window, min_periods=window).std()
        data['UpperBand'] = data['SMA'] + k_factor * data['StdDev']
        data['LowerBand'] = data['SMA'] - k_factor * data['StdDev']

        # Drop initial rows with NaNs from rolling calculations
        data.dropna(subset=['SMA', 'StdDev', 'UpperBand', 'LowerBand'], inplace=True)

        if len(data) < 2: # Need at least 2 points to calculate returns
            # print(f"[{ts_code}] Insufficient data after NaN removal, skipping.")
            return None

        # --- Signal Generation (Vectorized Approach - Faster) ---
        # Conditions for entry/exit
        buy_condition = (data['Close'] < data['LowerBand'])
        sell_condition = (data['Close'] > data['SMA']) # Exit condition

        data['Signal'] = 0
        data['Position'] = 0
        
        # Use shift to check previous state efficiently
        prev_close = data['Close'].shift(1)
        prev_lower_band = data['LowerBand'].shift(1)
        prev_sma = data['SMA'].shift(1)
        
        # 简化逻辑(仅做多):
        # 如果价格跌破下轨(与前一天相比)，则做多
        # 如果价格突破移动平均线(与前一天相比)，则平仓
        
        # 更健壮的状态机逻辑(迭代可能更清晰，但尝试向量化部分)
        position_active = 0 # 0 = no position, 1 = long position
        positions = []
        signals = []

        # Iteration is often clearer for stateful signal logic
        for i in range(len(data)):
            current_signal = 0
            close = data['Close'].iloc[i]
            lower = data['LowerBand'].iloc[i]
            sma = data['SMA'].iloc[i]

            if position_active == 0: # If no position
                if close < lower: # Check for buy entry
                    current_signal = 1 # Buy signal
                    position_active = 1 # Enter position
            else: # If holding a position
                if close > sma: # Check for sell exit
                    current_signal = -1 # Sell signal
                    position_active = 0 # Exit position
            
            signals.append(current_signal)
            positions.append(position_active) # Store the state *after* potential action

        data['Signal'] = signals
        # Position reflects state *at the end* of the day. Shift for PnL calc.
        data['Position'] = positions 
        data['Position_Shifted'] = data['Position'].shift(1).fillna(0)

        # --- Performance Calculation ---
        data['DailyReturn'] = data['Close'].pct_change()
        data['StrategyReturn'] = data['Position_Shifted'] * data['DailyReturn']
        
        # Drop initial NaN from pct_change
        data.dropna(subset=['DailyReturn'], inplace=True)
        
        if data.empty:
             # print(f"[{ts_code}] No valid returns calculated, skipping.")
             return None

        data['CumulativeMarketReturn'] = (1 + data['DailyReturn']).cumprod() - 1
        data['CumulativeStrategyReturn'] = (1 + data['StrategyReturn']).cumprod() - 1

        # --- Result Aggregation ---
        total_strategy_return = data['CumulativeStrategyReturn'].iloc[-1]
        total_market_return = data['CumulativeMarketReturn'].iloc[-1]
        num_trades = (data['Signal'] == 1).sum() # Count buy signals as trades

        # Calculate Annualized Return
        start_dt = data.index.min()
        end_dt = data.index.max()
        days = (end_dt - start_dt).days
        approx_years = max(days / 365.25, 1/365.25) # Avoid division by zero for short periods

        # Handle potential NaN/inf returns if cumulative return is -1 or less
        if pd.isna(total_strategy_return) or total_strategy_return <= -1:
             annualized_strategy_return = -1.0 # Or np.nan
        else:
             annualized_strategy_return = (1 + total_strategy_return)**(1/approx_years) - 1
             
        if pd.isna(total_market_return) or total_market_return <= -1:
             annualized_market_return = -1.0 # Or np.nan
        else:
             annualized_market_return = (1 + total_market_return)**(1/approx_years) - 1


        return {
            'ts_code': ts_code,
            'start_date': start_dt.strftime('%Y-%m-%d'),
            'end_date': end_dt.strftime('%Y-%m-%d'),
            'total_days': len(data),
            'strategy_return': total_strategy_return,
            'market_return': total_market_return,
            'annualized_strategy_return': annualized_strategy_return,
            'annualized_market_return': annualized_market_return,
            'num_trades': num_trades
        }

    except Exception as e:
        print(f"Error processing backtest for {ts_code}: {e}")
        # print(traceback.format_exc()) # Uncomment for detailed stack trace during debugging
        return None

# === 批量处理和主执行 ===

def batch_backtest(engine, stock_list, start_date, end_date, window, k_factor, max_workers):
    """
    管理批量回测流程：获取数据、分配任务、收集结果。

    参数:
        engine: SQLAlchemy引擎实例。
        stock_list: 要回测的股票代码列表。
        start_date, end_date: 日期范围。
        window, k_factor: 策略参数。
        max_workers: 并行进程数。

    返回:
        pandas.DataFrame: 包含所有成功回测股票结果的DataFrame。
    """
    total_stocks = len(stock_list)
    if total_stocks == 0:
        print("No stocks to backtest.")
        return pd.DataFrame()

    print(f"\n--- Starting Batch Backtest for {total_stocks} Stocks ---")
    overall_start_time = time.time()

    # 1. Fetch all data in one go
    all_stock_data = fetch_batch_stock_data(engine, stock_list, start_date, end_date)
    if all_stock_data.empty:
        print("Failed to fetch batch data. Cannot proceed with backtesting.")
        return pd.DataFrame()

    # 2. Group data by stock code
    print("Grouping data by stock code...")
    group_start_time = time.time()
    # Ensure the index is set correctly *before* grouping if not done in fetch
    if 'trade_date' in all_stock_data.columns and not isinstance(all_stock_data.index, pd.DatetimeIndex):
         all_stock_data.set_index('trade_date', inplace=True)
         
    grouped_data = dict(tuple(all_stock_data.groupby('ts_code')))
    del all_stock_data # Free up memory
    print(f"Data grouping completed in {time.time() - group_start_time:.2f} seconds.")
    
    found_stocks = len(grouped_data)
    missing_stocks = total_stocks - found_stocks
    print(f"Data found for {found_stocks} stocks. {missing_stocks} stocks had no data in the date range or were missing.")


    # 3. Execute backtests in parallel
    print(f"Starting parallel backtest calculations using {max_workers} workers...")
    results = []
    futures = []
    processed_count = 0
    skipped_count = 0
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        submission_start_time = time.time()
        for stock_code in stock_list: # Iterate original list to know which were submitted/skipped
            if stock_code in grouped_data:
                stock_df = grouped_data[stock_code]
                future = executor.submit(backtest_single_stock, stock_code, window, k_factor, stock_df)
                futures.append((future, stock_code)) # Keep track of stock code with future
            else:
                # print(f"Skipping {stock_code} - no data found in batch fetch.")
                skipped_count += 1
        print(f"Submitted {len(futures)} tasks to process pool in {time.time() - submission_start_time:.2f}s. Skipped {skipped_count} stocks.")

        # 4. Collect results as they complete
        collection_start_time = time.time()
        for future, stock_code in futures:
            try:
                result = future.result() # Wait for task to complete and get result
                if result is not None:
                    results.append(result)
            except Exception as e:
                print(f"Error retrieving result for {stock_code}: {e}")
            finally:
                processed_count += 1
                if processed_count % 100 == 0 or processed_count == len(futures): # Print progress periodically
                    elapsed = time.time() - collection_start_time
                    print(f"  Processed {processed_count}/{len(futures)} results... ({elapsed:.1f}s elapsed)")

    print(f"Result collection finished in {time.time() - collection_start_time:.2f} seconds.")

    # 5. Finalize and return results
    if not results:
        print("No successful backtest results were generated.")
        return pd.DataFrame()

    results_df = pd.DataFrame(results)
    # Sort by annualized strategy return
    results_df.sort_values(by='annualized_strategy_return', ascending=False, inplace=True)
    
    overall_duration = time.time() - overall_start_time
    print(f"--- Batch Backtest Completed in {overall_duration:.2f} seconds ---")
    
    return results_df

def save_results_to_csv(results_df, output_dir=None):
    """Saves the results DataFrame to a CSV file with a timestamp."""
    if results_df.empty:
        print("No results to save.")
        return None

    if output_dir is None:
        output_dir = os.path.dirname(os.path.abspath(__file__)) # Save in script's directory

    os.makedirs(output_dir, exist_ok=True) # Create directory if it doesn't exist

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"adaptive_mean_reversion_results_{timestamp}.csv"
    filepath = os.path.join(output_dir, filename)

    try:
        results_df.to_csv(filepath, index=False, float_format='%.6f') # Use more precision
        print(f"Results successfully saved to: {filepath}")
        return filepath
    except Exception as e:
        print(f"Error saving results to CSV: {e}")
        return None

def main():
    """Main execution function."""
    print("\n" + "="*30)
    print(" Adaptive Mean Reversion Batch Backtester")
    print("="*30 + "\n")
    print(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Parameters: Window={WINDOW}, K-Factor={K_FACTOR}, Range={START_DATE} to {END_DATE}")
    
    engine = None # Initialize engine to None
    try:
        # --- 1. Create Database Engine ---
        print("\n--- Step 1: Initializing Database Connection ---")
        engine = create_db_engine(DB_URL, MAX_POOL_SIZE, MAX_OVERFLOW, POOL_TIMEOUT, POOL_RECYCLE)
        if engine is None:
            print("Fatal: Database engine creation failed. Exiting.")
            return # Exit if DB connection fails

        # --- 2. Get Stock List ---
        print("\n--- Step 2: Fetching Target Stock List ---")
        stock_list = get_sh_stock_list(engine)
        if not stock_list:
            print("Fatal: Could not retrieve stock list. Exiting.")
            return

        # --- 3. Run Batch Backtest ---
        print("\n--- Step 3: Running Batch Backtest ---")
        results_df = batch_backtest(engine, stock_list, START_DATE, END_DATE, WINDOW, K_FACTOR, MAX_WORKERS)

        # --- 4. Process and Save Results ---
        print("\n--- Step 4: Processing and Saving Results ---")
        if results_df.empty:
            print("Warning: Backtesting completed, but no valid results were generated.")
            return

        csv_path = save_results_to_csv(results_df)

        # --- 5. Display Summary ---
        print("\n" + "="*30)
        print(" Backtest Summary")
        print("="*30)
        total_attempted = len(stock_list)
        total_successful = len(results_df)
        success_rate = (total_successful / total_attempted * 100) if total_attempted > 0 else 0
        
        print(f"Total Stocks Attempted: {total_attempted}")
        print(f"Successfully Backtested: {total_successful} ({success_rate:.1f}%)")
        
        if total_successful > 0:
             # Calculate overall averages safely, handling potential NaNs
             avg_strat_ret = pd.to_numeric(results_df['strategy_return'], errors='coerce').mean()
             avg_ann_strat_ret = pd.to_numeric(results_df['annualized_strategy_return'], errors='coerce').mean()
             avg_trades = pd.to_numeric(results_df['num_trades'], errors='coerce').mean()

             print(f"Average Strategy Return (Cumulative): {avg_strat_ret:.2%}")
             print(f"Average Annualized Strategy Return: {avg_ann_strat_ret:.2%}")
             print(f"Average Number of Trades: {avg_trades:.1f}")

             print("\nTop 5 Performing Stocks (by Annualized Strategy Return):")
             top5 = results_df.head(5)[['ts_code', 'annualized_strategy_return', 'strategy_return', 'market_return', 'num_trades']]
             # Custom formatter for better alignment and percentage display
             def format_row(row):
                 return f"  {row['ts_code']:<10} | Ann. Strat: {row['annualized_strategy_return']:>8.2%} | Cum. Strat: {row['strategy_return']:>8.2%} | Cum. Market: {row['market_return']:>8.2%} | Trades: {int(row['num_trades']):>4}"
             
             for index, row in top5.iterrows():
                 print(format_row(row))

        if csv_path:
            print(f"\nDetailed results saved to: {csv_path}")
        else:
            print("\nFailed to save detailed results.")

    except Exception as e:
        print("\n" + "="*30)
        print(" An Unhandled Error Occurred During Execution!")
        print("="*30)
        print(f"Error Type: {type(e).__name__}")
        print(f"Error Message: {e}")
        print("\n--- Traceback ---")
        print(traceback.format_exc())
        print("--- End Traceback ---")
        print("\nExecution aborted due to error.")

    finally:
        # --- 6. Clean up: Dispose Engine ---
        if engine is not None:
            print("\n--- Step 5: Closing Database Connections ---")
            try:
                engine.dispose()
                print("Database connection pool disposed.")
            except Exception as e:
                print(f"Error disposing database engine: {e}")
        
        print(f"\nEnd Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*30)


if __name__ == "__main__":
    main()
