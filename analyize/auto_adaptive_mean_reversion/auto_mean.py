#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import sys
import os
import time
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到系统路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 导入数据加载器
from get_data.stock_data import StockDataLoader
from get_data.db_config import DB_URL
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
import time
import random

# --- 1. 参数设置 ---
start_date = '2020-01-01'
end_date = '2023-12-31'
window = 20          # 移动平均和标准差的窗口期 (N)
k = 2.0              # 标准差倍数 (k)

# --- 数据库连接池配置 ---
MAX_POOL_SIZE = 10   # 连接池最大连接数
MAX_OVERFLOW = 5     # 连接池最大溢出连接数
POOL_TIMEOUT = 30    # 连接池超时时间(秒)
POOL_RECYCLE = 1800  # 连接池回收时间(秒)
MAX_RETRIES = 3      # 最大重试次数
RETRY_DELAY = 2      # 重试延迟时间(秒)

# --- 2. 获取上交所股票列表 ---
def get_sh_stock_list():
    """
    获取上海交易所股票列表
    
    Returns:
        list: 上交所股票代码列表
    """
    # 创建连接池
    engine = create_engine(
        DB_URL,
        poolclass=QueuePool,
        pool_size=MAX_POOL_SIZE,
        max_overflow=MAX_OVERFLOW,
        pool_timeout=POOL_TIMEOUT,
        pool_recycle=POOL_RECYCLE
    )
    
    # 重试计数器
    retry_count = 0
    
    while retry_count <= MAX_RETRIES:
        try:
            # 从数据库获取股票基本信息
            query = """
            SELECT ts_code
            FROM stock_basic
            WHERE exchange = 'SSE' AND list_status = 'L'
            """
            
            # 执行查询
            with engine.connect() as conn:
                df = pd.read_sql(query, conn)
            
            stock_list = df['ts_code'].tolist()
            print(f"成功获取上交所股票列表，共 {len(stock_list)} 只股票")
            
            # 关闭连接池
            engine.dispose()
            
            return stock_list
            
        except Exception as e:
            retry_count += 1
            error_msg = str(e)
            
            # 检查是否是连接错误
            if "Too many connections" in error_msg or "OperationalError" in error_msg:
                wait_time = RETRY_DELAY * (2 ** (retry_count - 1))  # 指数退避策略
                print(f"获取股票列表时遇到数据库连接错误: {e}")
                print(f"等待 {wait_time} 秒后进行第 {retry_count} 次重试...")
                time.sleep(wait_time)
            else:
                # 非连接错误，直接退出重试
                print(f"获取上交所股票列表时出错: {e}")
                engine.dispose()
                return []
    
    # 如果重试次数用尽仍未成功
    if retry_count > MAX_RETRIES:
        print(f"获取上交所股票列表失败: 已达到最大重试次数 {MAX_RETRIES}")
        engine.dispose()
        return []


# --- 3. 单只股票回测函数 ---
def backtest_single_stock(ts_code, start_date, end_date, window, k, data=None):
    """
    对单只股票进行自适应均值回归策略回测
    
    Args:
        ts_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        window: 移动平均窗口期
        k: 标准差倍数
        data: 预先获取的股票数据，如果为None则需要从数据库获取
        
    Returns:
        dict: 回测结果
    """
    # 如果没有提供数据，则返回None（数据应该在批处理中预先获取）
    if data is None:
        print(f"未提供 {ts_code} 的数据，跳过该股票。")
        return None
        
    # 如果数据为空，跳过该股票
    if data.empty:
        print(f"无法获取 {ts_code} 的数据，跳过该股票。")
        return None
    
    # 检查数据是否包含必要的列
    required_columns = ['trade_date', 'close_hfq']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        print(f"{ts_code} 数据缺少必要的列: {missing_columns}，跳过该股票。")
        return None
        
    try:
        # 检查数据量是否足够
        if len(data) < window + 10:  # 至少需要窗口期+10天的数据
            print(f"{ts_code} 数据量不足，需要至少 {window + 10} 天，实际只有 {len(data)} 天，跳过该股票。")
            return None
            
        # 将trade_date设置为索引并转换为datetime格式
        data['trade_date'] = pd.to_datetime(data['trade_date'])
        data.set_index('trade_date', inplace=True)
        
        # 重命名列以匹配原代码
        data.rename(columns={'close_hfq': 'Close'}, inplace=True)
        
        # --- 计算指标 ---
        # 自适应均值: N日简单移动平均线
        data['SMA'] = data['Close'].rolling(window=window).mean()
        
        # 自适应波动: N日滚动标准差
        data['StdDev'] = data['Close'].rolling(window=window).std()
        
        # 计算上下轨 (布林带)
        data['UpperBand'] = data['SMA'] + k * data['StdDev']
        data['LowerBand'] = data['SMA'] - k * data['StdDev']
        
        # 删除包含NaN值的初始行 (因为rolling计算需要window期的数据)
        data.dropna(inplace=True)
        
        if len(data) < 30:  # 确保有足够的数据进行回测
            print(f"{ts_code} 数据不足，跳过该股票。")
            return None
        
        # --- 生成交易信号 (简化版，仅做多) ---
        data['Signal'] = 0  # 0: 无信号, 1: 买入信号, -1: 卖出(平仓)信号
        data['Position'] = 0 # 0: 无持仓, 1: 持有多头仓位
        
        position_active = False # 标记当前是否持有仓位
        
        for i in range(1, len(data)):
            # 检查买入条件
            close_price = data['Close'].iloc[i]
            lower_band = data['LowerBand'].iloc[i]
            sma = data['SMA'].iloc[i]
            
            if close_price < lower_band and not position_active:
                data.loc[data.index[i], 'Signal'] = 1  # 发出买入信号
                data.loc[data.index[i], 'Position'] = 1  # 标记开始持仓
                position_active = True
            # 检查卖出(平仓)条件
            elif close_price > sma and position_active:
                data.loc[data.index[i], 'Signal'] = -1 # 发出卖出信号
                data.loc[data.index[i], 'Position'] = 0  # 标记结束持仓 (下一天生效)
                position_active = False
            # 如果没有触发买卖，则维持前一天的持仓状态
            elif position_active:
                data.loc[data.index[i], 'Position'] = 1
        
        # 注意：实际交易中，信号产生后，通常在下一个交易日执行操作。
        # Position列反映的是 *当天收盘后* 应该持有的仓位状态。
        # 为了计算回报，我们需要将Position后移一天，因为交易是在信号出现的 *之后* 发生的。
        data['Position_Shifted'] = data['Position'].shift(1).fillna(0) # 假设第一天不持仓
        
        # --- 计算策略回报 ---
        # 计算股票的日收益率
        data['DailyReturn'] = data['Close'].pct_change()
        
        # 计算策略的日收益率 (持仓状态 * 股票日收益率)
        data['StrategyReturn'] = data['Position_Shifted'] * data['DailyReturn']
        
        # 丢弃计算收益率产生的第一个NaN
        data.dropna(subset=['DailyReturn'], inplace=True)
        
        # 计算累积收益率
        data['CumulativeMarketReturn'] = (1 + data['DailyReturn']).cumprod() - 1
        data['CumulativeStrategyReturn'] = (1 + data['StrategyReturn']).cumprod() - 1
        
        # 计算性能指标
        total_strategy_return = data['CumulativeStrategyReturn'].iloc[-1]
        total_market_return = data['CumulativeMarketReturn'].iloc[-1]
        
        # 计算年化收益率
        days = (data.index.max() - data.index.min()).days
        approx_years = days / 365.25
        if approx_years > 0:
            annualized_strategy_return = (1 + total_strategy_return)**(1/approx_years) - 1
            annualized_market_return = (1 + total_market_return)**(1/approx_years) - 1
        else:
            annualized_strategy_return = float('nan')
            annualized_market_return = float('nan')
        
        # 计算交易次数
        buy_signals = data[data['Signal'] == 1]
        num_trades = len(buy_signals)
        
        # 返回结果
        return {
            'ts_code': ts_code,
            'start_date': data.index.min().strftime('%Y-%m-%d'),
            'end_date': data.index.max().strftime('%Y-%m-%d'),
            'total_days': len(data),
            'strategy_return': total_strategy_return,
            'market_return': total_market_return,
            'annualized_strategy_return': annualized_strategy_return,
            'annualized_market_return': annualized_market_return,
            'num_trades': num_trades
        }
        
    except Exception as e:
        print(f"处理 {ts_code} 数据分析时出错: {e}")
        return None

# --- 4. 批量回测函数 ---
def batch_backtest(stock_list, start_date, end_date, window, k, max_workers=None):
    """
    批量回测多只股票
    
    Args:
        stock_list: 股票代码列表
        start_date: 开始日期
        end_date: 结束日期
        window: 移动平均窗口期
        k: 标准差倍数
        max_workers: 最大进程数，默认为None（根据CPU核心数自动设置）
        
    Returns:
        DataFrame: 回测结果
    """
    results = []
    total_stocks = len(stock_list)
    
    print(f"开始批量回测 {total_stocks} 只股票...")
    start_time = time.time()
    
    # 限制最大进程数，避免创建过多数据库连接
    if max_workers is None or max_workers > MAX_POOL_SIZE:
        max_workers = min(MAX_POOL_SIZE, os.cpu_count() or 4)
    
    print(f"使用 {max_workers} 个进程进行并行计算")
    
    # 创建数据加载器
    data_loader = StockDataLoader()
    
    # 创建连接池
    engine = create_engine(
        DB_URL,
        poolclass=QueuePool,
        pool_size=MAX_POOL_SIZE,
        max_overflow=MAX_OVERFLOW,
        pool_timeout=POOL_TIMEOUT,
        pool_recycle=POOL_RECYCLE
    )
    
    try:
        # 使用多进程加速计算
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 将股票列表分成多个批次，避免同时提交过多任务
            batch_size = max(1, len(stock_list) // (max_workers * 2))
            stock_batches = [stock_list[i:i+batch_size] for i in range(0, len(stock_list), batch_size)]
            
            # 逐批次提交任务
            completed = 0
            for batch in stock_batches:
                # 为当前批次的股票获取数据
                future_to_stock = {}
                for stock in batch:
                    try:
                        # 添加随机延迟，避免同时创建大量连接
                        time.sleep(random.uniform(0.05, 0.2))
                        
                        # 获取股票数据
                        stock_data = None
                        try:
                            # 使用stk_factor表获取后复权价格数据
                            query = f"""
                            SELECT 
                                ts_code, trade_date, close_hfq, open_hfq, high_hfq, low_hfq, vol, amount
                            FROM stk_factor
                            WHERE ts_code = '{stock}'
                            AND trade_date BETWEEN '{start_date}' AND '{end_date}'
                            ORDER BY trade_date
                            """
                            
                            with engine.connect() as conn:
                                stock_data = pd.read_sql(query, conn)
                                
                            if stock_data.empty:
                                print(f"无法获取 {stock} 的数据，跳过该股票。")
                                completed += 1
                                continue
                                
                        except Exception as e:
                            print(f"获取 {stock} 数据时出错: {e}")
                            completed += 1
                            continue
                        
                        # 提交回测任务
                        future = executor.submit(backtest_single_stock, stock, start_date, end_date, window, k, stock_data)
                        future_to_stock[future] = stock
                        
                    except Exception as e:
                        print(f"处理 {stock} 时出错: {e}")
                        completed += 1
                
                # 处理当前批次的结果
                for future in future_to_stock:
                    stock = future_to_stock[future]
                    try:
                        result = future.result()
                        if result is not None:
                            results.append(result)
                        completed += 1
                        # 打印进度
                        if completed % 10 == 0 or completed == total_stocks:
                            elapsed_time = time.time() - start_time
                            print(f"进度: {completed}/{total_stocks} ({completed/total_stocks:.1%}) - 已用时间: {elapsed_time:.1f}秒")
                    except Exception as e:
                        print(f"处理 {stock} 回测结果时出错: {e}")
                        completed += 1
    finally:
        # 关闭连接池
        engine.dispose()
        print("数据库连接池已关闭")
    
    # 转换为DataFrame
    if results:
        results_df = pd.DataFrame(results)
        # 按策略年化收益率降序排序
        results_df.sort_values(by='annualized_strategy_return', ascending=False, inplace=True)
        return results_df
    else:
        print("没有有效的回测结果")
        return pd.DataFrame()

# --- 5. 保存结果到CSV ---
def save_results_to_csv(results_df, output_dir=None):
    """
    保存回测结果到CSV文件
    
    Args:
        results_df: 回测结果DataFrame
        output_dir: 输出目录，默认为None（使用当前脚本所在目录）
    
    Returns:
        str: CSV文件路径
    """
    if results_df.empty:
        print("没有结果可保存")
        return None
    
    # 设置输出目录
    if output_dir is None:
        output_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成文件名（包含时间戳）
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"mean_reversion_results_{timestamp}.csv"
    filepath = os.path.join(output_dir, filename)
    
    # 保存到CSV
    results_df.to_csv(filepath, index=False, float_format='%.4f')
    print(f"结果已保存到: {filepath}")
    
    return filepath

# --- 6. 主函数 ---
def main():
    """
    主函数：获取上交所股票列表，进行批量回测，并保存结果
    """
    print("\n=== 自适应均值回归策略批量回测 ===\n")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"回测参数: 窗口期={window}, 标准差倍数={k}, 回测区间: {start_date} 至 {end_date}")
    
    try:
        # 获取上交所股票列表
        print("\n1. 正在获取上交所股票列表...")
        stock_list = get_sh_stock_list()
        if not stock_list:
            print("错误: 无法获取上交所股票列表，程序退出")
            return
        
        print(f"成功获取 {len(stock_list)} 只上交所股票")
        
        # 批量回测
        print("\n2. 开始批量回测...")
        start_time = time.time()
        results_df = batch_backtest(stock_list, start_date, end_date, window, k)
        elapsed_time = time.time() - start_time
        
        # 保存结果到CSV
        if results_df.empty:
            print("\n警告: 没有成功的回测结果，请检查数据获取和处理过程")
            return
            
        print("\n3. 保存回测结果...")
        csv_path = save_results_to_csv(results_df)
        
        # 打印汇总统计
        print("\n=== 回测汇总统计 ===")
        print(f"总股票数量: {len(stock_list)}")
        print(f"成功回测股票数量: {len(results_df)} ({len(results_df)/len(stock_list):.2%})")
        print(f"总耗时: {elapsed_time:.2f}秒 (平均每只股票 {elapsed_time/len(stock_list):.2f}秒)")
        print(f"平均策略收益率: {results_df['strategy_return'].mean():.2%}")
        print(f"平均年化策略收益率: {results_df['annualized_strategy_return'].mean():.2%}")
        print(f"平均交易次数: {results_df['num_trades'].mean():.1f}次")
        
        print("\n表现最好的前5只股票:")
        top5 = results_df.head(5)[['ts_code', 'annualized_strategy_return', 'market_return', 'num_trades']]
        print(top5.to_string(index=False, float_format=lambda x: f"{x:.2%}" if isinstance(x, float) else str(x)))
        
        print(f"\n结果已保存到: {csv_path}")
        print(f"\n完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"\n程序执行过程中出错: {e}")
        import traceback
        print(traceback.format_exc())
        print("程序异常退出")
        return

# 如果直接运行此脚本，则执行主函数
if __name__ == "__main__":
    main()
