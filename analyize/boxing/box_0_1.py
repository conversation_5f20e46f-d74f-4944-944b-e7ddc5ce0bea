"""
@Author: JiangXin
@Date: 2024/12/21 15:25
@Description: 
"""

import pandas as pd
from sqlalchemy import create_engine
import backtrader as bt
import numpy as np
import os
import datetime

class TradingStrategy(bt.Strategy):
    params = (
        ('target_rise', 5),  # 卖出目标涨幅百分比
        ('max_drawdown', 15),  # 最大波动范围百分比
        ('stop_loss', 15),  # 止损范围百分比
    )

    def __init__(self):
        # 记录买入价格
        self.buy_price = None
        # 记录交易日志
        self.trade_log = []
        # 计算移动平均线
        self.sma3 = bt.indicators.SMA(self.data.close, period=3)
        self.sma7 = bt.indicators.SMA(self.data.close, period=7)
        # 记录上一次的移动平均线值
        self.last_sma3 = None
        self.last_sma7 = None
        # 记录是否正在卖出
        self.order = None
        # 记录持仓数量
        self.position_size = 0
        # 记录待记录的交易信息
        self.pending_trade = None
        
    def log_trade(self, type, price, size, reason=""):
        """记录交易信息"""
        self.pending_trade = {
            'date': self.data.datetime.date(),
            'type': type,
            'price': price,
            'size': size,
            'value': price * size,
            'reason': reason,
            'sma3': self.sma3[0],
            'sma7': self.sma7[0],
            'portfolio_value': self.broker.getvalue()
        }

    # def notify_order(self, order):
    #     """处理订单状态"""
    #     if order.status in [order.Submitted, order.Accepted]:
    #         return
    #
    #     if order.status in [order.Completed]:
    #         if order.isbuy():
    #             self.position_size = order.executed.size
    #             self.buy_price = order.executed.price
    #             # 更新实际成交信息并记录
    #             if self.pending_trade and self.pending_trade['type'] == '买入':
    #                 self.pending_trade['price'] = order.executed.price
    #                 self.pending_trade['value'] = order.executed.price * order.executed.size
    #                 self.trade_log.append(self.pending_trade)
    #         elif order.issell():
    #             # 更新实际成交信息并记录
    #             if self.pending_trade and self.pending_trade['type'] == '卖出':
    #                 self.pending_trade['price'] = order.executed.price
    #                 self.pending_trade['value'] = order.executed.price * order.executed.size
    #                 self.trade_log.append(self.pending_trade)
    #             self.position_size = 0
    #             self.buy_price = None
    #         self.pending_trade = None
    #
    #     elif order.status in [order.Canceled, order.Margin, order.Rejected]:
    #         self.pending_trade = None
    #
    #     self.order = None

    def next(self):
        # 如果有未完成的订单，等待订单完成
        if self.order:
            return

        # 更新移动平均线值
        if len(self.data) > 1:  # 确保有前一天的数据
            self.last_sma3 = self.sma3[-1]
            self.last_sma7 = self.sma7[-1]

        # 检查买入条件
        if not self.position_size:
            # 计算可用资金的20%
            available_cash = self.broker.getcash()
            target_value = available_cash * 0.2
            potential_size = int(target_value / self.data.close[0])
            
            # 检查是否满足最小100股要求
            if potential_size < 100:
                return
            
            # 检查买入条件
            if (self.last_sma3 is not None and self.last_sma7 is not None and
                self.sma3[0] > self.sma7[0] and self.last_sma3 <= self.last_sma7):
                
                # 检查最近30个交易日的波动
                high_30 = max([self.data.high[-i] for i in range(30)])
                low_30 = min([self.data.low[-i] for i in range(30)])
                volatility = (high_30 - low_30) / low_30 * 100

                if volatility <= self.params.max_drawdown:
                    # 先记录交易信息
                    self.log_trade("买入", self.data.close[0], potential_size, 
                                 f"波动率={volatility:.2f}%, 3日均线上穿7日均线")
                    # 执行买入
                    self.order = self.buy(size=potential_size)

        elif self.position_size > 0:  # 确保有持仓才考虑卖出
            # 检查卖出条件
            current_price = self.data.close[0]
            
            # 计算收益率
            returns = (current_price - self.buy_price) / self.buy_price * 100
            
            # 止盈条件
            if returns >= self.params.target_rise:
                # 先记录交易信息
                self.log_trade("卖出", current_price, self.position_size,
                             f"达到目标收益：{returns:.2f}%")
                # 执行卖出
                self.order = self.sell(size=self.position_size)
                
            # 止损条件
            elif returns <= -self.params.stop_loss:
                # 先记录交易信息
                self.log_trade("卖出", current_price, self.position_size,
                             f"触发止损：{returns:.2f}%")
                # 执行卖出
                self.order = self.sell(size=self.position_size)

    def get_trade_log(self):
        """返回交易日志"""
        return self.trade_log

class BacktestEngine:
    def __init__(self, db_url):
        self.engine = create_engine(db_url)

    def fetch_stock_data(self, ts_code):
        """从数据库提取后复权价格数据"""
        query = f"""
        SELECT trade_date, close_hfq AS close, open_hfq AS open, high_hfq AS high, low_hfq AS low, vol
        FROM stk_factor
        WHERE ts_code = '{ts_code}'
        ORDER BY trade_date ASC
        """
        df = pd.read_sql(query, self.engine, parse_dates=['trade_date'])
        df.set_index('trade_date', inplace=True)
        return df

    def print_backtest_report(self, results, initial_cash, ts_code):
        """生成回测报告并保存到Excel"""
        # 创建输出目录
        output_dir = '../../code_demo/backtest_results'
        os.makedirs(output_dir, exist_ok=True)
        current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

        # 获取第一个策略的统计信息
        strat = results[0]

        # 计算基本指标
        portfolio_value = float(strat.broker.getvalue())
        returns = (portfolio_value - initial_cash) / initial_cash * 100

        # 创建Excel写入器
        excel_file = os.path.join(output_dir, f'backtest_report_{ts_code}_{current_time}.xlsx')
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 1. 总体表现sheet
            summary_data = pd.DataFrame([
                ['初始资金', f"{initial_cash:,.2f}"],
                ['最终资金', f"{portfolio_value:,.2f}"],
                ['总收益率', f"{returns:.2f}%"],
            ], columns=['指标', '数值'])

            summary_data.to_excel(writer, sheet_name='总体表现', index=False)

            # 2. 详细交易记录sheet
            trade_log = strat.get_trade_log()
            if trade_log:
                # 处理交易记录
                records = []
                for trade in trade_log:
                    try:
                        record = {
                            '交易日期': trade['date'].strftime('%Y-%m-%d') if hasattr(trade['date'], 'strftime') else str(trade['date']),
                            '交易类型': str(trade['type']),
                            '交易价格': round(float(trade['price']), 2),
                            '交易数量': int(trade['size']),
                            '交易金额': round(float(trade['value']), 2),
                            '交易原因': str(trade['reason']),
                            '3日均线': round(float(trade['sma3']), 2),
                            '7日均线': round(float(trade['sma7']), 2),
                            '组合价值': round(float(trade['portfolio_value']), 2)
                        }
                        records.append(record)
                    except (KeyError, ValueError, TypeError, AttributeError) as e:
                        print(f"处理交易记录时出错: {str(e)}")
                        continue

                if records:
                    trade_df = pd.DataFrame(records)
                    trade_df.to_excel(writer, sheet_name='交易记录', index=False)

        print(f"\n回测报告已保存到: {excel_file}")
        return excel_file

    def run_backtest(self, ts_code, start_date, end_date, strategy_params):
        """运行回测"""
        # 获取股票数据
        df = self.fetch_stock_data(ts_code)
        df = df[(df.index >= start_date) & (df.index <= end_date)]
        
        # 数据分析和检查
        print(f"开始回测 {ts_code}...")
        print(f"数据范围: {df.index.min()} 到 {df.index.max()}")
        
        # 确保数据都是有限数值
        df = df.replace([np.inf, -np.inf], np.nan).dropna()
        
        # 检查成交量是否合理
        df['vol'] = df['vol'].replace(0, np.nan)
        df = df.dropna()
        
        if len(df) == 0:
            raise ValueError("清理后没有有效数据，请检查数据源或调整日期范围")
        
        # 转换为 Backtrader 数据格式
        data = bt.feeds.PandasData(dataname=df)
        
        # 设置回测引擎
        cerebro = bt.Cerebro()
        cerebro.adddata(data)
        
        # 设置初始资金
        initial_cash = 100000.0
        cerebro.broker.setcash(initial_cash)
        
        # 设置交易成本
        cerebro.broker.setcommission(commission=0.001)
        
        # 添加策略
        cerebro.addstrategy(TradingStrategy,
                          target_rise=strategy_params.get('target_rise', 5),
                          max_drawdown=strategy_params.get('max_drawdown', 15),
                          stop_loss=strategy_params.get('stop_loss', 10))
        
        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
        
        # 运行回测
        results = cerebro.run()
        
        # 生成回测报告
        report_file = self.print_backtest_report(results, initial_cash, ts_code)
        
        return results

# 示例用法
if __name__ == "__main__":
    db_url = "mysql+pymysql://root:123456@localhost/tushare"
    backtest_engine = BacktestEngine(db_url)

    ts_code = "002201.SZ"
    start_date = "2023-01-01"
    end_date = "2024-01-01"

    strategy_params = {
        'target_rise': 5,
        'max_drawdown': 15,
        'stop_loss': 15
    }

    backtest_engine.run_backtest(ts_code, start_date, end_date, strategy_params)
