"""
批量回测脚本 - 简化版
"""

import pandas as pd
from sqlalchemy import create_engine, text
import json
import os
from datetime import datetime
from box_0_1 import BacktestEngine

# 设置输出目录
OUTPUT_DIR = '../../code_demo/backtest_results'
os.makedirs(OUTPUT_DIR, exist_ok=True)

def get_stock_list():
    """获取符合条件的股票列表"""
    engine = create_engine('mysql+pymysql://root:123456@localhost/tushare')
    query = text("""
    SELECT ts_code, name, industry, list_date
    FROM stock_basic
    WHERE list_status = 'L'
    AND industry IS NOT NULL
    AND list_date <= '2023-01-01'
    limit 2
    """)
    with engine.connect() as conn:
        df = pd.read_sql(query, conn)
        # 确保list_date是字符串格式
        df['list_date'] = pd.to_datetime(df['list_date']).dt.strftime('%Y-%m-%d')
        return df

class DateTimeEncoder(json.JSONEncoder):
    """处理日期时间的JSON编码器"""
    def default(self, obj):
        if hasattr(obj, 'strftime'):
            return obj.strftime('%Y-%m-%d')
        if hasattr(obj, 'isoformat'):
            return obj.isoformat()
        return super().default(obj)

def save_results(results, current_time):
    """保存回测结果"""
    if not results:
        return pd.DataFrame(), {}
    
    # 准备汇总数据
    summary_results = [{
        'ts_code': r['ts_code'],
        'name': r['name'],
        'industry': r['industry'],
        'total_trades': r['total_trades'],
        'win_rate': r['win_rate'],
        'total_return': r['total_return'],
        'final_value': r['final_value']
    } for r in results]
    
    # 转换为DataFrame并排序
    results_df = pd.DataFrame(summary_results)
    if len(results_df) > 0:
        results_df = results_df.sort_values('total_return', ascending=False)
    
    #保存结果
    results_df.to_csv(os.path.join(OUTPUT_DIR, f'backtest_results_{current_time}.csv'),
                      index=False, encoding='utf-8-sig')
    
    # 使用自定义编码器保存JSON
    # with open(os.path.join(OUTPUT_DIR, f'backtest_details_{current_time}.json'), 'w',
    #           encoding='utf-8') as f:
    #     json.dump(results, f, ensure_ascii=False, indent=4, cls=DateTimeEncoder)
    
    # 计算汇总统计
    summary = {
        '回测股票数量': len(results_df),
        '平均交易次数': results_df['total_trades'].mean() if len(results_df) > 0 else 0,
        '平均胜率': results_df['win_rate'].mean() if len(results_df) > 0 else 0,
        '平均收益率': results_df['total_return'].mean() if len(results_df) > 0 else 0,
        '最大收益率': results_df['total_return'].max() if len(results_df) > 0 else 0,
        '最小收益率': results_df['total_return'].min() if len(results_df) > 0 else 0
    }
    
    with open(os.path.join(OUTPUT_DIR, f'backtest_summary_{current_time}.json'), 'w', 
              encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=4)
    
    return results_df, summary

def batch_backtest(start_date='2023-01-01', end_date='2024-01-01', 
                  target_rise=5, max_drawdown=15, stop_loss=15):
    """批量回测函数"""
    try:
        stocks_df = get_stock_list()
        print(f"获取到 {len(stocks_df)} 只股票进行回测")
    except Exception as e:
        print(f"获取股票列表失败: {str(e)}")
        return pd.DataFrame(), {}

    backtest_engine = BacktestEngine('mysql+pymysql://root:123456@localhost/tushare')
    results = []
    error_stocks = []
    current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 遍历股票进行回测
    total_stocks = len(stocks_df)
    for idx, row in stocks_df.iterrows():
        try:
            print(f"\n[{idx+1}/{total_stocks}] 正在回测 {row['ts_code']} - {row['name']}")
            
            if pd.isna(row['list_date']):
                continue
            
            # 运行回测
            strategy_params = {
                'target_rise': target_rise,
                'max_drawdown': max_drawdown,
                'stop_loss': stop_loss
            }
            
            # 确保日期格式统一
            stock_start_date = max(start_date, row['list_date'])
            
            results_list = backtest_engine.run_backtest(
                ts_code=row['ts_code'],
                start_date=stock_start_date,
                end_date=end_date,
                strategy_params=strategy_params
            )
            
            # 获取结果
            strategy = results_list[0]
            trade_log = strategy.get_trade_log()
            final_value = strategy.broker.getvalue()
            
            # 处理交易记录中的日期
            processed_trade_log = []
            for trade in trade_log:
                trade_copy = trade.copy()
                if hasattr(trade_copy['date'], 'strftime'):
                    trade_copy['date'] = trade_copy['date'].strftime('%Y-%m-%d')
                processed_trade_log.append(trade_copy)
            
            # 保存结果
            results.append({
                'ts_code': row['ts_code'],
                'name': row['name'],
                'industry': row['industry'],
                'total_trades': len(processed_trade_log) // 2,
                'win_rate': len([t for t in processed_trade_log if t['type'] == '卖出' and '目标收益' in t['reason']]) / (len(processed_trade_log) // 2) if processed_trade_log else 0,
                'total_return': (final_value - 100000.0) / 100000.0,
                'final_value': final_value,
                'trade_log': processed_trade_log
            })
            
        except Exception as e:
            error_stocks.append({'ts_code': row['ts_code'], 'reason': str(e)})
            print(f"回测 {row['ts_code']} 时发生错误: {str(e)}")
            continue
    
    # 保存错误记录
    if error_stocks:
        with open(os.path.join(OUTPUT_DIR, f'backtest_errors_{current_time}.json'), 'w', 
                  encoding='utf-8') as f:
            json.dump(error_stocks, f, ensure_ascii=False, indent=4)
    
    return save_results(results, current_time)

if __name__ == '__main__':
    results_df, summary = batch_backtest()
    print("\n回测汇总统计:")
    for key, value in summary.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2%}")
        else:
            print(f"{key}: {value}")
