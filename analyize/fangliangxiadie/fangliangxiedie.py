import pandas as pd
from sqlalchemy import create_engine
from datetime import date, timedelta
import sys
import os

# 将项目根目录添加到 sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from get_data.db_config import get_engine

# 获取最近的两个交易日
sql_latest_dates = """
SELECT DISTINCT trade_date
FROM stk_factor
ORDER BY trade_date DESC
LIMIT 2
"""

try:
    engine = get_engine()
    with engine.connect() as connection:
        latest_dates_df = pd.read_sql_query(sql_latest_dates, connection)

    if len(latest_dates_df) < 2:
        print("数据库中没有足够的交易日数据（至少需要两个交易日）。")
        sys.exit()

    latest_trade_date = latest_dates_df['trade_date'].iloc[0].strftime('%Y-%m-%d')
    previous_trade_date = latest_dates_df['trade_date'].iloc[1].strftime('%Y-%m-%d')
    latest_trade_date_yyyymmdd = latest_dates_df['trade_date'].iloc[0].strftime('%Y%m%d')


    print(f"正在从数据库拉取最近两个交易日 {latest_trade_date} 和 {previous_trade_date} 的行情数据...")

    # 构建SQL查询语句
    # 查询最近交易日数据 (stk_factor 和 stock_basic 合并)
    sql_latest = f"""
    SELECT
        sf.ts_code,
        sb.name,
        sf.pct_change AS pct_chg,
        sf.vol,
        sf.low,
        sf.close
    FROM
        stk_factor sf
    LEFT JOIN
        stock_basic sb ON sf.ts_code = sb.ts_code
    WHERE
        sf.trade_date = '{latest_trade_date}'
    """

    # 查询前一个交易日数据 (stk_factor)
    sql_previous = f"""
    SELECT
        ts_code,
        vol
    FROM
        stk_factor
    WHERE
        trade_date = '{previous_trade_date}'
    """

    # 使用数据库连接引擎执行查询
    with engine.connect() as connection:
        df_latest = pd.read_sql_query(sql_latest, connection)
        df_previous = pd.read_sql_query(sql_previous, connection)

    print(f"成功拉取 {len(df_latest)} 条最近交易日数据和 {len(df_previous)} 条前一个交易日数据。")

    # 合并数据并计算放量比
    df = df_latest.merge(df_previous[['ts_code', 'vol']], on='ts_code', suffixes=('', '_prev'))
    # 避免除以零
    df['vol_prev'] = df['vol_prev'].replace(0, 0.000001)
    df['vol_ratio'] = df['vol'] / df['vol_prev']

    # 筛选条件
    df['pct_chg'] = df['pct_chg'].astype(float)
    signal_df = df[(df['pct_chg'] <= -9) & (df['vol_ratio'] >= 2)]

    print(f"找到暴跌放量票数: {len(signal_df)}")

    # 输出核心字段
    output_df = signal_df[['ts_code', 'name', 'pct_chg', 'vol_ratio', 'low', 'close']]
    print(output_df)

    # 保存到 CSV
    output_dir = 'analyize/fangliangxiadie/output'
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f'暴跌信号_{latest_trade_date_yyyymmdd}.csv')
    output_df.to_csv(output_path, index=False)
    print(f"✅ 信号已保存到 {output_path}")

except Exception as e:
    print(f"发生错误: {e}")
    sys.exit()
