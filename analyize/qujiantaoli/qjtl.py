import sys
import os

# 获取当前脚本的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录 (脚本在 analyize/qujiantaoli/ 下，根目录是向上两级)
project_root = os.path.dirname(os.path.dirname(current_dir))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.append(project_root)

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from get_data.stock_data import StockDataLoader

def identify_ideal_ranging(df, n_period=20, m_period=5,
                           ma_flatness_threshold_ratio=0.1, # MA标准差相对于ATR的阈值
                           min_volatility_threshold_ratio=0.5, # 价格标准差相对于ATR的阈值
                           range_reach_sd_multiplier=1.0): # 波动范围需要达到多少倍标准差
    """
    识别理想的震荡区间

    参数:
    df (pd.DataFrame): 包含 'High', 'Low', 'Adj Close' 列的 K 线数据
    n_period (int): 计算 MA、标准差和高低点的主要回看周期
    m_period (int): 计算 MA 平坦度的较短回看周期 (必须 < n_period)
    ma_flatness_threshold_ratio (float): MA 标准差允许的最大值（相对于同期 ATR），越小表示要求 MA 越平坦
    min_volatility_threshold_ratio (float): 价格标准差要求的最小值（相对于同期 ATR），越大表示要求波动越大
    range_reach_sd_multiplier (float): 最高/最低价需要超出 MA +/- 多少倍标准差

    返回:
    pd.DataFrame: 原始 DataFrame 增加以下列:
        - 'MA': N 周期移动平均线
        - 'Price_SD': N 周期价格标准差
        - 'MA_SD': M 周期 MA 的标准差
        - 'ATR': N 周期平均真实波幅
        - 'Rolling_High': N 周期最高价
        - 'Rolling_Low': N 周期最低价
        - 'Is_Ranging': 布尔值，标记是否处于理想震荡状态
    """
    if m_period >= n_period:
        raise ValueError("m_period must be smaller than n_period")

    df = df.copy() # 创建副本以避免修改原始数据

    # 如果是 MultiIndex 列，则取最后一级作为列名
    if isinstance(df.columns, pd.MultiIndex):
        df.columns = df.columns.get_level_values(-1)

    # 标准化列名: 支持不同大小写和命名
    col_mapping = {
        'Adj Close': ['Adj Close', 'adj_close', 'Close', 'close'],
        'High': ['High', 'high'],
        'Low': ['Low', 'low']
    }
    for std_col, aliases in col_mapping.items():
        for alias in aliases:
            if alias in df.columns:
                if alias != std_col:
                    df[std_col] = df[alias]
                break
        else:
            raise KeyError(f"DataFrame 必须包含列之一: {aliases}")

    # --- 计算基础指标 ---
    # 1. N周期移动平均线
    df['MA'] = df['Adj Close'].rolling(window=n_period).mean()

    # 2. N周期价格标准差
    df['Price_SD'] = df['Adj Close'].rolling(window=n_period).std()

    # 3. M周期移动平均线的标准差 (衡量 MA 平坦度)
    df['MA_SD'] = df['MA'].rolling(window=m_period).std()

    # 4. N周期平均真实波幅 (ATR) - 用于相对阈值
    df['High-Low'] = df['High'] - df['Low']
    df['High-PrevClose'] = abs(df['High'] - df['Adj Close'].shift(1))
    df['Low-PrevClose'] = abs(df['Low'] - df['Adj Close'].shift(1))
    df['TrueRange'] = df[['High-Low', 'High-PrevClose', 'Low-PrevClose']].max(axis=1)
    df['ATR'] = df['TrueRange'].rolling(window=n_period).mean()

    # 5. N周期滚动最高价/最低价
    df['Rolling_High'] = df['High'].rolling(window=n_period).max()
    df['Rolling_Low'] = df['Low'].rolling(window=n_period).min()

    # --- 定义动态阈值 ---
    ma_flatness_threshold = ma_flatness_threshold_ratio * df['ATR']
    min_volatility_threshold = min_volatility_threshold_ratio * df['ATR']

    # --- 应用条件判断 ---
    # 条件1: MA 足够平坦 (MA的标准差小于阈值)
    cond1_ma_flat = df['MA_SD'] < ma_flatness_threshold

    # 条件2: 价格波动性足够大 (价格标准差大于阈值)
    cond2_sufficient_volatility = df['Price_SD'] > min_volatility_threshold

    # 条件3: 实际波动高点和低点触及或超过 +/- k*SD 边界
    cond3_range_reaches_sd = (
        (df['Rolling_High'] >= df['MA'] + range_reach_sd_multiplier * df['Price_SD']) &
        (df['Rolling_Low'] <= df['MA'] - range_reach_sd_multiplier * df['Price_SD'])
    )

    # --- 综合判断 ---
    df['Is_Ranging'] = cond1_ma_flat & cond2_sufficient_volatility & cond3_range_reaches_sd

    # 清理临时列 (可选)
    # df = df.drop(columns=['High-Low', 'High-PrevClose', 'Low-PrevClose', 'TrueRange'])

    return df

# 从数据库获取后复权的高/低/收盘价
def fetch_stock_data(ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    从数据库拉取高、低、后复权收盘价数据
    """
    loader = StockDataLoader()
    df = loader.get_stock_factors(
        ts_code=ts_code,
        start_date=start_date,
        end_date=end_date,
        factors=['trade_date', 'high_hfq', 'low_hfq', 'close_hfq']
    )
    df = df.rename(columns={
        'trade_date': 'Date',
        'high_hfq': 'High',
        'low_hfq': 'Low',
        'close_hfq': 'Adj Close'
    })
    df['Date'] = pd.to_datetime(df['Date'])
    return df.set_index('Date')

def main():
    ts_code = '300033.SZ'
    start_date = '2023-01-01'
    end_date = '2025-06-01'
    df = fetch_stock_data(ts_code, start_date, end_date)
    ranging_data = identify_ideal_ranging(
        df,
        n_period=30,
        m_period=7,
        ma_flatness_threshold_ratio=0.08,
        min_volatility_threshold_ratio=0.6,
        range_reach_sd_multiplier=1.0
    ).dropna()

    plt.figure(figsize=(15, 10))
    # 价格 & MA
    plt.subplot(3, 1, 1)
    plt.plot(ranging_data.index, ranging_data['Adj Close'], label='Adj Close')
    plt.plot(ranging_data.index, ranging_data['MA'], label='30-Period MA')
    plt.fill_between(
        ranging_data.index,
        ranging_data['MA'] - ranging_data['Price_SD'],
        ranging_data['MA'] + ranging_data['Price_SD'],
        color='gray', alpha=0.3
    )
    periods = ranging_data[ranging_data['Is_Ranging']]
    plt.scatter(periods.index, periods['Adj Close'], color='red', s=10)
    plt.title(f'{ts_code} - Identified Ranging')
    plt.legend(); plt.grid(True)

    # MA 平坦度
    plt.subplot(3, 1, 2)
    plt.plot(ranging_data.index, ranging_data['MA_SD'], label='7-Period MA SD', color='green')
    plt.plot(ranging_data.index, ranging_data['ATR'] * 0.08, '--', label='Flatness Threshold', color='red')
    plt.title('Moving Average Flatness'); plt.ylabel('MA SD')
    plt.legend(); plt.grid(True)

    # 价格波动性
    plt.subplot(3, 1, 3)
    plt.plot(ranging_data.index, ranging_data['Price_SD'], label='30-Period Price SD', color='purple')
    plt.plot(ranging_data.index, ranging_data['ATR'] * 0.6, '--', label='Volatility Threshold', color='red')
    plt.title('Price Volatility'); plt.ylabel('Price SD'); plt.xlabel('Date')
    plt.legend(); plt.grid(True)
    plt.tight_layout(); plt.show()

if __name__ == '__main__':
    main()
