"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/5/4 12:17
@File: max_value.py
@Version: 1.0
@Description: 
"""

import pandas as pd

# 假设你的数据已经读取为 DataFrame，命名为 df
df = pd.read_csv('optimization_results_20250504_1214.csv')  # 或 pd.read_excel('your_file.xlsx')

# 按 ts_code 分组，找到 final_value 最大的行
result = df.loc[df.groupby('ts_code')['final_value'].idxmax()]
print(result)
max_result = result.to_csv("max_value.csv")
