"""
<AUTHOR> ji<PERSON><PERSON>
@Date    : 2025-05-04
@File    : everyday_signals.py
@Version : 1.1
@Desc    : 读取每只股票的最优参数组合，判断最新交易日是否产生买/卖信号
"""
import datetime
from itertools import islice
from concurrent.futures import ProcessPoolExecutor, as_completed

import pandas as pd
import talib
from sqlalchemy import create_engine
from tqdm import tqdm

from get_data.db_config import DB_URL   # 请保持路径一致

# ---------- 全局数据库连接（懒加载） ----------
_engine = None
def get_engine():
    global _engine
    if _engine is None:
        # pool_pre_ping 避免长连接失效
        _engine = create_engine(DB_URL, pool_pre_ping=True)
    return _engine


# ---------- 工具函数 ----------
def get_latest_trade_date() -> str:
    """数据库中最新交易日（格式：YYYY-MM-DD）"""
    sql = "SELECT MAX(trade_date) AS last_day FROM stk_factor"
    latest = pd.read_sql(sql, get_engine()).iloc[0, 0]
    return str(latest)


def get_stock_data(ts_code: str, end_date: str, window: int = 60) -> pd.DataFrame | None:
    """
    获取某股票截止 end_date 的最近 window 根复权行情
    数据按 trade_date 升序返回，若无数据返回 None
    """
    sql = f"""
      SELECT trade_date, close_hfq, open_hfq, high_hfq, low_hfq, vol
      FROM stk_factor
      WHERE ts_code = '{ts_code}' AND trade_date <= '{end_date}'
      ORDER BY trade_date DESC
      LIMIT {window}
    """
    df = pd.read_sql(sql, get_engine())
    if df.empty:
        return None
    return df.sort_values("trade_date").reset_index(drop=True)


def judge_signal(df: pd.DataFrame, p: dict) -> str:
    """
    布林带 + RSI 组合策略信号：
      买入 : price < BB下轨 且 RSI < rsi_lower
      卖出 : price >= BB中轨 且 RSI > rsi_upper
    无信号 : 其他
    """
    close = df["close_hfq"].astype(float).values
    if len(close) < max(p["bb_period"], p["rsi_period"]):
        return "无数据"

    up, mid, low = talib.BBANDS(
        close,
        timeperiod=p["bb_period"],
        nbdevup=p["devfactor"],
        nbdevdn=p["devfactor"],
    )
    rsi = talib.RSI(close, timeperiod=p["rsi_period"])

    price, bb_mid, bb_low, rsi_val = close[-1], mid[-1], low[-1], rsi[-1]

    if price < bb_low and rsi_val < p["rsi_lower"]:
        return "买入"
    if price >= bb_mid and rsi_val > p["rsi_upper"]:
        return "卖出"
    return "无信号"


def process_one(row_dict: dict, end_date: str) -> dict:
    """
    子进程任务：判断单只股票在指定日期的买/卖信号
    输入参数：
        row_dict : 来自 max_value.csv 的一行（dict）
        end_date : 最新交易日
    返回：
        {ts_code, signal}
    """
    ts_code = row_dict["ts_code"]
    # 只取策略相关字段，避免多余序列化
    p = {
        "bb_period":  int(row_dict["bb_period"]),
        "devfactor":  float(row_dict["devfactor"]),
        "rsi_period": int(row_dict["rsi_period"]),
        "rsi_lower":  float(row_dict["rsi_lower"]),
        "rsi_upper":  float(row_dict["rsi_upper"]),
    }

    df = get_stock_data(ts_code, end_date)
    if df is None:
        return {"ts_code": ts_code, "signal": "无数据"}

    signal = judge_signal(df, p)
    return {"ts_code": ts_code, "signal": signal}


# ---------- 主程序 ----------
if __name__ == "__main__":
    param_df = pd.read_csv("max_value.csv")             # 每只股票的最优参数
    latest_day = get_latest_trade_date()                # 最近交易日

    # 多进程参数
    max_workers = 8
    tasks = [row.to_dict() for _, row in param_df.iterrows()]

    results = []
    with ProcessPoolExecutor(max_workers=max_workers) as ex:
        futs = [ex.submit(process_one, t, latest_day) for t in tasks]
        for fut in tqdm(as_completed(futs), total=len(futs), ncols=80, desc="Signal scan"):
            results.append(fut.result())

    # 结果保存
    res_df = pd.DataFrame(results).sort_values("ts_code")
    ts = datetime.datetime.now().strftime("%Y%m%d_%H%M")
    out_file = f"everyday_signals_{ts}.csv"
    res_df.to_csv(out_file, index=False, encoding="utf-8-sig")
    print(f"\n信号检测完成：{out_file} 生成成功！")
