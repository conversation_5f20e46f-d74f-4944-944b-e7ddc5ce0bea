"""
<AUTHOR> ji<PERSON><PERSON>
@Date   : 2025-05-04
@File   : combined_strategy_mp.py
@Desc   : 布林带 + RSI 策略 —— 多进程参数优化（10 进程，实时进度）
"""
import backtrader as bt
import pandas as pd
from sqlalchemy import create_engine
from get_data.db_config import DB_URL

from itertools import product
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import datetime


# ---------------- 策略 ----------------
class CombinedBBRSIStrategy(bt.Strategy):
    params = (
        ("bb_period", 20),
        ("devfactor", 2),
        ("rsi_period", 14),
        ("rsi_lower", 30),
        ("rsi_upper", 70),
        ("order_pct", 0.10),
    )

    def __init__(self):
        self.bb  = bt.indicators.BollingerBands(period=self.p.bb_period,
                                                devfactor=self.p.devfactor)
        self.rsi = bt.indicators.RSI(period=self.p.rsi_period)

    def next(self):
        cash = self.broker.getcash()
        size = int((cash * self.p.order_pct) // self.data.close[0])
        if not self.position:
            if self.data.close[0] < self.bb.bot[0] and self.rsi[0] < self.p.rsi_lower:
                self.buy(size=size)
        else:
            if self.data.close[0] >= self.bb.mid[0] and self.rsi[0] > self.p.rsi_upper:
                self.close()


# ---------------- 子进程函数 ----------------
def run_backtest(ts_code: str, df: pd.DataFrame, params: dict) -> dict:
    """
    单只股票 + 单组参数 的一次回测
    返回最终净值、夏普比率及参数
    """
    cerebro = bt.Cerebro()
    cerebro.broker.setcash(100_000)
    cerebro.broker.setcommission(commission=0.0003)

    # feed
    datafeed = bt.feeds.PandasData(dataname=df)
    cerebro.adddata(datafeed)

    # 分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")

    # 策略
    cerebro.addstrategy(CombinedBBRSIStrategy, **params)

    try:
        strat = cerebro.run()[0]
        final_value = strat.broker.getvalue()
        sharpe      = strat.analyzers.sharpe.get_analysis().get("sharperatio", None)
        return {**params,
                "final_value": round(final_value, 2),
                "sharpe": round(sharpe, 2) if sharpe is not None else None,
                "ts_code": ts_code}
    except Exception:
        # 可能碰到极端数据错误；返回 None 由主进程过滤
        return None


# ---------------- 主进程入口 ----------------
if __name__ == "__main__":
    TS_CODE    = None                     # 单只股票时可指定，否则为 None -> 自动选股
    START_DATE = "2024-01-01"
    END_DATE   = "2025-01-01"
    MAX_WORKERS = 10                      # 并行进程数
    engine = create_engine(DB_URL)

    # 1. 选股
    latest_date = pd.read_sql("SELECT MAX(trade_date) dt FROM daily_basic", engine).iloc[0, 0]
    if TS_CODE:
        stock_list = [TS_CODE]
    else:
        stock_df = pd.read_sql(
            f"""
            SELECT ts_code FROM daily_basic
            WHERE trade_date = '{latest_date}'
              AND circ_mv BETWEEN 200000 AND 300000
            """, engine
        )
        stock_list = stock_df["ts_code"].tolist()

    if not stock_list:
        raise ValueError("无符合条件的股票")

    # 2. 参数网格
    param_grid = {
        "bb_period":  [10, 20, 30],
        "devfactor":  [1.5, 2.0, 2.5],
        "rsi_period": [10, 14, 21],
        "rsi_lower":  [20, 30, 40],
        "rsi_upper":  [60, 70, 80],
        "order_pct":  [0.05, 0.10, 0.20],
    }
    param_combos = [dict(zip(param_grid, v)) for v in product(*param_grid.values())]

    # 3. 构建任务列表  (ts_code, df, params)
    tasks = []
    for ts_code in stock_list:
        sql = f"""
            SELECT trade_date AS datetime,
                   open_hfq  AS open,
                   high_hfq  AS high,
                   low_hfq   AS low,
                   close_hfq AS close,
                   vol       AS volume
            FROM stk_factor
            WHERE ts_code = '{ts_code}'
              AND trade_date BETWEEN '{START_DATE}' AND '{END_DATE}'
            ORDER BY trade_date
        """
        df = pd.read_sql(sql, engine, parse_dates=["datetime"]).set_index("datetime")
        if df.empty:
            continue
        df["openinterest"] = 0

        for p in param_combos:
            tasks.append((ts_code, df, p))

    total_jobs = len(tasks)
    print(f"共 {total_jobs} 份回测任务，使用 {MAX_WORKERS} 进程 …")

    # 4. 多进程跑 + 进度条
    results = []
    with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = [executor.submit(run_backtest, *t) for t in tasks]
        for fut in tqdm(as_completed(futures), total=total_jobs, ncols=80):
            res = fut.result()
            if res:
                results.append(res)

    # 5. 汇总 & 导出
    if results:
        results_df = (
            pd.DataFrame(results)
            .sort_values(by="final_value", ascending=False)
            .reset_index(drop=True)
        )
        best = results_df.iloc[0]
        print(f"\nBest Final Value: {best.final_value:,.2f}")
        print("Best Params:", best.drop(["final_value", "sharpe", "ts_code"]).to_dict())

        # 保留两位小数
        numeric_cols = results_df.select_dtypes(include=["float", "int"]).columns
        results_df[numeric_cols] = results_df[numeric_cols].round(2)

        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M")
        filename  = f"optimization_results_{timestamp}.csv"
        results_df.to_csv(filename, index=False, encoding="utf-8-sig")
        print(f"全部结果已保存到 {filename}")
    else:
        print("无有效回测结果")
