"""
@Author: AI Assistant
@Date: 2025/07/02
@Description: 股票筛选器 - 筛选当天涨幅>5%、成交金额>过去14天日均成交量1倍、换手率>5%、流通市值<100亿、ADX<30、主力资金占比>0.6的股票
筛选条件：
1. 当天涨幅 > 5%
2. 当天成交金额 > 过去14天日均成交量的1倍
3. 换手率 > 5%
4. 流通市值 < 100亿
5. ADX指标 < 30
6. 主力资金占比 > 0.6
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os
import warnings

warnings.filterwarnings('ignore')

# 添加路径以导入数据库配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from get_data.db_config import get_engine


class StockSelector:
    def __init__(self):
        self.engine = get_engine()
        self.today = datetime.now().strftime('%Y-%m-%d')
        
    def get_latest_trade_date(self):
        """获取最新交易日期"""
        query = """
        SELECT MAX(trade_date) as latest_date 
        FROM daily_basic 
        WHERE trade_date <= CURDATE()
        """
        result = pd.read_sql(query, self.engine)
        return result['latest_date'].iloc[0]
    
    def get_trading_dates(self, days_back=20):
        """获取最近的交易日期列表"""
        query = f"""
        SELECT DISTINCT trade_date 
        FROM daily_basic 
        WHERE trade_date <= CURDATE()
        ORDER BY trade_date DESC 
        LIMIT {days_back}
        """
        result = pd.read_sql(query, self.engine)
        return result['trade_date'].tolist()
    
    def get_stock_basic_info(self, ts_codes):
        """获取股票基础信息"""
        if not ts_codes:
            return pd.DataFrame()
            
        # 构建IN子句
        codes_str = "', '".join(ts_codes)
        codes_str = f"'{codes_str}'"
        
        query = f"""
        SELECT ts_code, name, industry, list_date
        FROM stock_basic 
        WHERE ts_code IN ({codes_str})
        AND list_status = 'L'
        AND name NOT LIKE '%%ST%%'
        AND name NOT LIKE '%%*ST%%'
        """
        return pd.read_sql(query, self.engine)
    
    def screen_stocks(self, target_date=None):
        """
        筛选符合条件的股票
        条件：
        1. 当天涨幅 > 5%
        2. 当天成交金额 > 过去14天日均成交量的1倍  
        3. 换手率 > 5%
        4. 流通市值 < 100亿
        5. ADX指标 < 30
        6. 主力资金占比 > 0.6
        """
        
        if target_date is None:
            target_date = self.get_latest_trade_date()
        
        print(f"开始筛选 {target_date} 的股票...")
        
        # 获取交易日期列表（包含目标日期及之前14天的交易日）
        trading_dates = self.get_trading_dates(20)  # 多获取几天以确保有足够的历史数据
        
        if target_date not in trading_dates:
            print(f"错误：{target_date} 不是交易日")
            return pd.DataFrame()
        
        # 找到目标日期在列表中的位置
        target_index = trading_dates.index(target_date)
        
        # 确保有足够的历史数据（14天）
        if target_index + 14 >= len(trading_dates):
            print(f"错误：没有足够的历史数据来计算14天平均成交量")
            return pd.DataFrame()
        
        # 获取目标日期的前14个交易日（不包含目标日期）
        history_dates = trading_dates[target_index + 1:target_index + 15]
        
        print(f"目标日期：{target_date}")
        print(f"历史数据日期范围：{history_dates[-1]} 到 {history_dates[0]}")
        
        # 1. 获取目标日期的涨跌幅、换手率、流通市值、ADX和主力资金数据  
        today_query = f"""
        SELECT db.ts_code, db.turnover_rate, db.circ_mv, sf.pct_change, sf.amount, adx.adx, mf.main_money_ratio
        FROM daily_basic db
        INNER JOIN stk_factor sf ON db.ts_code = sf.ts_code AND db.trade_date = sf.trade_date  
        INNER JOIN adx_data adx ON db.ts_code = adx.ts_code AND db.trade_date = adx.trade_date
        INNER JOIN factors_money_focus_data mf ON db.ts_code = mf.ts_code AND db.trade_date = mf.trade_date
        WHERE db.trade_date = '{target_date}'
        AND db.turnover_rate > 5
        AND sf.pct_change > 5
        AND db.circ_mv < 1000000
        AND adx.adx < 30
        AND adx.period = 14
        AND mf.main_money_ratio > 0.6
        AND (db.ts_code LIKE '%%.SZ' OR db.ts_code LIKE '%%.SH')
        """
        
        today_data = pd.read_sql(today_query, self.engine)
        
        if today_data.empty:
            print("没有找到符合条件的股票")
            return pd.DataFrame()
        
        print(f"符合涨幅>5%、换手率>5%、流通市值<100亿、ADX<30、主力资金流入>0.6的股票数量：{len(today_data)}")
        
        # 2. 获取这些股票过去14天的成交量数据
        candidate_stocks = today_data['ts_code'].tolist()
        
        # 构建历史日期的IN子句
        history_dates_str = "', '".join([str(d) for d in history_dates])
        history_dates_str = f"'{history_dates_str}'"
        
        # 构建股票代码的IN子句
        codes_str = "', '".join(candidate_stocks)
        codes_str = f"'{codes_str}'"
        
        history_query = f"""
        SELECT ts_code, trade_date, amount
        FROM stk_factor
        WHERE ts_code IN ({codes_str})
        AND trade_date IN ({history_dates_str})
        """
        
        history_data = pd.read_sql(history_query, self.engine)
        
        if history_data.empty:
            print("没有找到历史成交量数据")
            return pd.DataFrame()
        
        # 3. 计算每只股票过去14天的平均成交金额
        avg_amounts = history_data.groupby('ts_code')['amount'].mean().reset_index()
        avg_amounts.columns = ['ts_code', 'avg_14d_amount']
        
        # 4. 合并数据并进行最终筛选
        merged_data = today_data.merge(avg_amounts, on='ts_code', how='inner')
        
        # 筛选成交金额大于过去14天平均值1倍的股票
        final_candidates = merged_data[
            merged_data['amount'] > merged_data['avg_14d_amount'] * 1.0
        ].copy()
        
        if final_candidates.empty:
            print("没有找到符合成交量条件的股票")
            return pd.DataFrame()
        
        # 5. 添加股票名称和行业信息
        stock_info = self.get_stock_basic_info(final_candidates['ts_code'].tolist())
        
        result = final_candidates.merge(stock_info, on='ts_code', how='left')
        
        # 6. 计算成交量放大倍数
        result['amount_ratio'] = result['amount'] / result['avg_14d_amount']
        
        # 7. 整理输出列
        result = result[[
            'ts_code', 'name', 'industry', 'pct_change', 'turnover_rate', 
            'circ_mv', 'adx', 'main_money_ratio', 'amount', 'avg_14d_amount', 'amount_ratio'
        ]].copy()
        
        # 重命名列
        result.columns = [
            '股票代码', '股票名称', '所属行业', '涨跌幅(%)', '换手率(%)', 
            '流通市值(万元)', 'ADX指标', '主力资金占比', '成交金额(千元)', '14日均成交金额(千元)', '成交量放大倍数'
        ]
        
        # 按涨跌幅降序排列
        result = result.sort_values('涨跌幅(%)', ascending=False)
        
        # 重置索引
        result = result.reset_index(drop=True)
        
        print(f"✅ 筛选完成，共找到 {len(result)} 只符合条件的股票")
        
        return result
    
    def save_results(self, result_df, target_date):
        """保存筛选结果到CSV文件"""
        if result_df.empty:
            print("没有数据可保存")
            return
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"股票筛选结果_{target_date}_{timestamp}.csv"
        
        # 确保目录存在
        os.makedirs('output', exist_ok=True)
        filepath = os.path.join('output', filename)
        
        result_df.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"✅ 结果已保存到：{filepath}")
        
        return filepath


def main():
    """主函数"""
    selector = StockSelector()
    
    try:
        # 筛选股票
        result = selector.screen_stocks()
        
        if not result.empty:
            print("\n" + "="*80)
            print("股票筛选结果")
            print("="*80)
            print(f"筛选条件：")
            print(f"1. 当天涨幅 > 5%")
            print(f"2. 当天成交金额 > 过去14天日均成交金额的1倍")
            print(f"3. 换手率 > 5%")
            print(f"4. 流通市值 < 100亿")
            print(f"5. ADX指标 < 30")
            print(f"6. 主力资金占比 > 0.6")
            print(f"7. 排除ST股票")
            print("-"*80)
            
            # 显示结果
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.max_colwidth', 20)
            print(result.to_string(index=False))
            
            # 保存结果
            target_date = selector.get_latest_trade_date()
            selector.save_results(result, target_date)
            
            # 统计信息
            print(f"\n统计信息：")
            print(f"- 符合条件的股票数量：{len(result)}")
            print(f"- 平均涨幅：{result['涨跌幅(%)'].mean():.2f}%")
            print(f"- 平均换手率：{result['换手率(%)'].mean():.2f}%")
            print(f"- 平均成交量放大倍数：{result['成交量放大倍数'].mean():.2f}")
            print(f"- 最大涨幅：{result['涨跌幅(%)'].max():.2f}%")
            print(f"- 最大成交量放大倍数：{result['成交量放大倍数'].max():.2f}")
        else:
            print("未找到符合条件的股票")
            
    except Exception as e:
        print(f"❌ 程序执行出错：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 