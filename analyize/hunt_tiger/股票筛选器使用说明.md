# 股票筛选器 - 使用说明

## 功能概述

本程序用于筛选符合特定条件的股票，帮助投资者发现具有强势表现的投资标的。

## 筛选条件

程序根据以下六个核心条件筛选股票：

1. **当天涨幅 > 5%** - 识别强势股票
2. **当天成交金额 > 过去14天日均成交金额的1倍** - 确保有足够的资金关注度
3. **换手率 > 5%** - 保证交易活跃度
4. **流通市值 < 100亿** - 专注于中小市值股票
5. **ADX指标 < 30** - 筛选趋势强度适中的股票（ADX值越低表示趋势不够强烈，适合寻找突破机会）
6. **主力资金占比 > 0.6** - 确保主力资金大量流入（占比60%以上）
7. **排除ST股票** - 避免风险较高的特别处理股票

## 文件说明

- **主程序**: `stock_selector_20250702.py`
- **输出目录**: `output/`
- **输出文件**: `股票筛选结果_日期_时间戳.csv`

## 使用方法

### 1. 直接运行
```bash
cd analyize
python stock_selector_20250702.py
```

### 2. 程序会自动：
- 获取最新交易日期
- 从数据库查询符合条件的股票
- 计算各项指标
- 生成CSV结果文件
- 显示统计信息

## 输出结果说明

### 控制台输出
程序运行时会显示：
- 筛选进度信息
- 符合各阶段条件的股票数量
- 最终筛选结果表格
- 详细统计信息

### CSV文件字段
生成的CSV文件包含以下字段：

| 字段名 | 含义 | 单位 |
|--------|------|------|
| 股票代码 | 股票的TS代码 | - |
| 股票名称 | 股票名称 | - |
| 所属行业 | 股票所属行业 | - |
| 涨跌幅(%) | 当日涨跌幅度 | 百分比 |
| 换手率(%) | 当日换手率 | 百分比 |
| 流通市值(万元) | 当日流通市值 | 万元 |
| ADX指标 | 平均方向指数，衡量趋势强度（0-100，<25弱趋势，25-50中等趋势，>50强趋势） | 数值 |
| 主力资金占比 | 主力资金占当日总成交金额的比例（0-1，>0.6表示主力资金大量流入） | 比例 |
| 成交金额(千元) | 当日成交金额 | 千元 |
| 14日均成交金额(千元) | 过去14个交易日平均成交金额 | 千元 |
| 成交量放大倍数 | 当日成交金额/14日均成交金额 | 倍数 |

## 程序特点

### 1. 数据来源
- 使用本地MySQL数据库
- 数据表：`daily_basic`（基本面数据）、`stk_factor`（价格数据）、`adx_data`（ADX指标）、`factors_money_focus_data`（主力资金数据）
- 自动获取最新交易日数据

### 2. 筛选逻辑
- **分步筛选**：先按涨幅和换手率筛选，再计算成交量条件
- **历史数据**：使用14个交易日的历史数据计算平均成交金额
- **智能排除**：自动排除ST股票和退市股票

### 3. 性能优化
- 使用SQL优化查询性能
- 分批处理大量数据
- 内存友好的数据处理方式

## 最新运行结果（2025-07-01）

在最新的运行中，程序成功筛选出 **9只** 符合条件的股票：

### 统计信息
- **符合条件的股票数量**: 9只
- **平均涨幅**: 15.46%
- **平均换手率**: 16.60%
- **平均主力资金占比**: 70.33%
- **平均成交量放大倍数**: 7.30倍
- **最大涨幅**: 20.00%（旋极信息 300324.SZ）
- **最大成交量放大倍数**: 17.87倍（凌玮科技 301373.SZ）

### 表现突出的股票（全部）
1. **旋极信息(300324.SZ)** - 涨幅20.00%，流通市值89.26亿，主力资金占比87.25%，成交量放大2.20倍
2. **凌玮科技(301373.SZ)** - 涨幅19.99%，流通市值13.51亿，主力资金占比69.76%，成交量放大17.87倍
3. **飞鹿股份(300665.SZ)** - 涨幅19.98%，流通市值14.06亿，主力资金占比76.15%，成交量放大5.94倍
4. **山河药辅(300452.SZ)** - 涨幅19.97%，流通市值27.50亿，主力资金占比61.71%，成交量放大15.66倍
5. **阳普医疗(300030.SZ)** - 涨幅19.94%，流通市值21.42亿，主力资金占比83.05%，成交量放大4.80倍

## 注意事项

### 1. 数据依赖
- 确保数据库连接正常
- 需要有足够的历史数据（至少15个交易日）
- 数据的及时性影响筛选结果

### 2. 投资建议
- **筛选结果仅供参考**，不构成投资建议
- 建议结合基本面分析和技术分析
- 注意风险控制，合理配置仓位
- 关注市场整体趋势和个股后续表现

### 3. 程序限制
- 筛选基于历史数据，不能预测未来表现
- 未考虑行业轮动、市场情绪等因素
- 建议结合其他技术指标综合判断

## 自定义设置

如需调整筛选条件，可修改程序中的参数：

```python
# 在screen_stocks方法中修改筛选条件
AND db.turnover_rate > 5        # 换手率阈值
AND sf.pct_change > 5           # 涨跌幅阈值
AND db.circ_mv < 1000000        # 流通市值阈值（万元，1000000万元=100亿）
AND adx.adx < 30                # ADX指标阈值
AND adx.period = 14             # ADX计算周期为14日
AND mf.main_money_ratio > 0.6   # 主力资金占比阈值（60%以上）
merged_data['amount'] > merged_data['avg_14d_amount'] * 1.0  # 成交量倍数
```

## 技术支持

如遇到问题，请检查：
1. 数据库连接是否正常
2. 数据表是否存在且有数据
3. Python环境和依赖包是否正确安装

程序依赖：
- pandas
- sqlalchemy
- pymysql 