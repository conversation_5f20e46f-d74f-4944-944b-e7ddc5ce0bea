"""
@Author: <PERSON><PERSON><PERSON>
@Date: 2025/2/5 17:24
@Description: 计算多只股票之间的皮尔逊相关系数
该模块用于分析多只股票在指定时间段内的相关性，通过计算后复权收盘价的日对数收益率和简单收益率的皮尔逊相关系数来衡量。
https://www.mermaidchart.com/app/projects/ac360c34-8a97-44fa-b944-a2dfd27a3a33/diagrams/ddd49dd4-8a3f-48ec-88c6-4fd0bd0ab0f6/version/v0.1/edit
"""
import datetime
import logging
from contextlib import contextmanager
from itertools import combinations
import pandas as pd
import numpy as np
from scipy.stats import pearsonr
from sqlalchemy import create_engine, exc, text

# 导入项目的数据库配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from get_data.db_config import DB_CONFIG, DB_URL

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 分析配置
ANALYSIS_CONFIG = {
    'start_date': '2024-01-01',  # 起始日期
    'end_date': '2024-12-01',    # 结束日期
    'min_total_mv': 50000000,  # 最小市值（1亿）
}

@contextmanager
def get_db_connection():
    """数据库连接的上下文管理器"""
    engine = None
    try:
        engine = create_engine(DB_URL, pool_pre_ping=True)
        yield engine
    except exc.SQLAlchemyError as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise
    finally:
        if engine:
            engine.dispose()

def get_stock_list(engine) -> list:
    """
    从daily_basic表获取符合条件的股票列表
    
    Args:
        engine: SQLAlchemy engine对象
    
    Returns:
        list: 符合条件的股票代码列表
    """
    try:
        query = text("""
        WITH latest_data AS (
            SELECT ts_code, total_mv
            FROM daily_basic
            WHERE trade_date = (
                SELECT MAX(trade_date)
                FROM daily_basic
                WHERE trade_date <= :end_date
            )
        )
        SELECT ts_code
        FROM latest_data
        WHERE total_mv >= :min_total_mv
        ORDER BY total_mv DESC
        """)
        
        with engine.connect() as conn:
            result = conn.execute(query, {
                'end_date': ANALYSIS_CONFIG['end_date'],
                'min_total_mv': ANALYSIS_CONFIG['min_total_mv']
            })
            stock_list = [row[0] for row in result]
            
        logger.info(f"获取到{len(stock_list)}只符合市值条件的股票")
        return stock_list
    except exc.SQLAlchemyError as e:
        logger.error(f"获取股票列表时发生错误: {str(e)}")
        raise

def fetch_stock_data(engine, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    从数据库中查询股票的后复权收盘价数据
    
    Args:
        engine: SQLAlchemy engine对象
        ts_code: 股票代码，例如 '000001.SZ'
        start_date: 开始日期，格式 'YYYY-MM-DD'
        end_date: 结束日期，格式 'YYYY-MM-DD'
    
    Returns:
        pandas DataFrame: 包含 trade_date 和对应股票的 close_hfq 数据
    
    Raises:
        exc.SQLAlchemyError: 当数据库查询出错时
    """
    try:
        query = f"""
        SELECT trade_date, close_hfq
        FROM stk_factor
        WHERE ts_code = '{ts_code}'
          AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date ASC
        """
        df = pd.read_sql(query, engine, parse_dates=['trade_date'])
        
        if df.empty:
            logger.warning(f"股票 {ts_code} 在指定时间段内没有数据")
            return df
            
        # 重命名收盘价列，添加股票代码前缀
        df.rename(columns={'close_hfq': f'close_{ts_code}'}, inplace=True)
        return df
    except exc.SQLAlchemyError as e:
        logger.error(f"查询股票 {ts_code} 数据时发生错误: {str(e)}")
        raise

def compute_returns(df: pd.DataFrame, price_col: str) -> pd.DataFrame:
    """计算价格序列的收益率"""
    ts_code = price_col.split("_", 1)[1]
    
    # 计算简单收益率: (Pt - Pt-1) / Pt-1
    simple_return_col = f'simple_return_{ts_code}'
    df[simple_return_col] = df[price_col].pct_change()
    
    # 计算对数收益率: ln(Pt/Pt-1)
    log_return_col = f'log_return_{ts_code}'
    df[log_return_col] = np.log(df[price_col]).diff()
    
    return df

def calculate_both_correlations(df: pd.DataFrame, stock1: str, stock2: str) -> tuple:
    """
    同时计算简单收益率和对数收益率的相关系数
    
    Args:
        df: 包含两只股票收益率数据的 DataFrame
        stock1: 第一只股票代码
        stock2: 第二只股票代码
    
    Returns:
        tuple: (简单收益率相关系数, 简单收益率p值, 对数收益率相关系数, 对数收益率p值, 样本数量)
    """
    simple_return_col1 = f'simple_return_{stock1}'
    simple_return_col2 = f'simple_return_{stock2}'
    log_return_col1 = f'log_return_{stock1}'
    log_return_col2 = f'log_return_{stock2}'
    
    # 删除任何包含NaN的行
    df_clean = df.dropna(subset=[simple_return_col1, simple_return_col2, 
                                log_return_col1, log_return_col2])
    
    if len(df_clean) < 2:
        raise ValueError("有效数据点不足，无法计算相关系数")
    
    # 计算简单收益率的相关系数
    simple_returns1 = df_clean[simple_return_col1]
    simple_returns2 = df_clean[simple_return_col2]
    simple_corr, simple_p = pearsonr(simple_returns1, simple_returns2)
    
    # 计算对数收益率的相关系数
    log_returns1 = df_clean[log_return_col1]
    log_returns2 = df_clean[log_return_col2]
    log_corr, log_p = pearsonr(log_returns1, log_returns2)
    
    # 保留6位小数
    simple_corr = round(simple_corr, 6)
    simple_p = round(simple_p, 6)
    log_corr = round(log_corr, 6)
    log_p = round(log_p, 6)
    
    return simple_corr, simple_p, log_corr, log_p, len(df_clean)

def calculate_correlations(engine, stock_list: list) -> pd.DataFrame:
    """
    计算股票列表中所有股票两两之间的相关系数
    
    Args:
        engine: SQLAlchemy engine对象
        stock_list: 股票代码列表
    
    Returns:
        DataFrame: 包含所有股票对的相关系数
    """
    results = []
    total_pairs = len(list(combinations(stock_list, 2)))
    processed_pairs = 0
    
    for stock1, stock2 in combinations(stock_list, 2):
        try:
            # 获取股票数据
            df1 = fetch_stock_data(engine, stock1, 
                                 ANALYSIS_CONFIG['start_date'], 
                                 ANALYSIS_CONFIG['end_date'])
            df2 = fetch_stock_data(engine, stock2, 
                                 ANALYSIS_CONFIG['start_date'], 
                                 ANALYSIS_CONFIG['end_date'])
            
            if df1.empty or df2.empty:
                continue
            
            # 合并数据并计算收益率
            df_merged = pd.merge(df1, df2, on="trade_date", how="inner")
            df_merged.sort_values("trade_date", inplace=True)
            
            # 计算收益率
            for stock_code, price_col in [
                (stock1, f'close_{stock1}'),
                (stock2, f'close_{stock2}')
            ]:
                df_merged = compute_returns(df_merged, price_col)
            
            # 计算相关系数
            simple_corr, simple_p, log_corr, log_p, sample_size = calculate_both_correlations(
                df_merged, stock1, stock2
            )
            
            # 保存结果（相关系数差异也保留6位小数）
            results.append({
                "stock1": stock1,
                "stock2": stock2,
                "samples": sample_size,
                "simple_correlation": simple_corr,
                "simple_p_value": simple_p,
                "log_correlation": log_corr,
                "log_p_value": log_p,
                "correlation_difference": round(abs(simple_corr - log_corr), 6)
            })
            
            processed_pairs += 1
            if processed_pairs % 10 == 0:
                logger.info(f"已处理 {processed_pairs}/{total_pairs} 对股票")
                
        except Exception as e:
            logger.error(f"处理股票对 {stock1}-{stock2} 时发生错误: {str(e)}")
            continue
    
    # 创建DataFrame并设置显示格式
    results_df = pd.DataFrame(results)
    float_columns = ['simple_correlation', 'simple_p_value', 
                    'log_correlation', 'log_p_value', 
                    'correlation_difference']
    
    # 设置float列的显示格式为6位小数
    for col in float_columns:
        results_df[col] = results_df[col].round(6)
    
    return results_df

def get_output_filename():
    """
    生成包含导出时间信息的输出文件名
    
    Returns:
        str: 输出文件名，格式：stock_correlations_YYYYMMDD_HHMMSS.csv
    """
    current_time = datetime.datetime.now()
    timestamp = current_time.strftime('%Y%m%d_%H%M%S')
    return f'stock_correlations_{timestamp}.csv'

def main():
    """主函数，执行相关性分析"""
    try:
        with get_db_connection() as engine:
            # 获取股票列表
            stock_list = get_stock_list(engine)
            if not stock_list:
                logger.error("没有找到符合条件的股票")
                return
            
            # 计算相关系数
            results_df = calculate_correlations(engine, stock_list)
            
            # 生成包含导出时间的文件名
            output_file = get_output_filename()
            
            # 保存结果，设置float_format确保CSV中的数值保留6位小数
            results_df.to_csv(output_file, 
                            index=False, 
                            float_format='%.6f')
            logger.info(f"分析结果已保存到: {output_file}")
            
            # 输出统计信息（保留6位小数）
            logger.info("\n相关性分析统计：")
            logger.info(f"总共分析了 {len(results_df)} 对股票")
            logger.info(f"简单收益率相关系数平均值: {results_df['simple_correlation'].mean():.6f}")
            logger.info(f"对数收益率相关系数平均值: {results_df['log_correlation'].mean():.6f}")
            logger.info(f"相关系数差异平均值: {results_df['correlation_difference'].mean():.6f}")
            
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {str(e)}")

if __name__ == '__main__':
    main()